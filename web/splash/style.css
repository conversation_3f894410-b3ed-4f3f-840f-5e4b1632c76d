body {
  margin:0;
  height:100%;
  background: #FFFFFF;
  
  background-size: 100% 100%;
}

.center {
  margin: 0;
  position: absolute;
  top: 50%;
  left: 50%;
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.contain {
  display:block;
  width:100%; height:100%;
  object-fit: contain;
}

.stretch {
  display:block;
  width:100%; height:100%;
}

.cover {
  display:block;
  width:100%; height:100%;
  object-fit: cover;
}

@media (prefers-color-scheme: dark) {
  body {
    margin:0;
    height:100%;
    background: #FFFFFF;
    
    background-size: 100% 100%;
  }
}
