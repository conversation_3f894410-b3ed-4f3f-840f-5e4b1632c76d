## 1.0.8
- Hover effect in dropdownlist for web/desktop added [#16](https://github.com/srtraj/dropdown_textfield/issues/16)
- ThemeData based dropdownlist color and by using **dropdownColor** to customize color of overlay.[#19](https://github.com/srtraj/dropdown_textfield/issues/19)

## 1.0.7
- Clear validation error #14 -fixed
  autovalidate option added


## 1.0.6
-  keyboard failed to show #10 -fixed

## 1.0.5
- Option added to customize clear and dropdown icon properties
- checkBoxProperty added ,now you can customize default property of multiple checkbox style
- "InitialValue in multiSelection #9" bug .


## 1.0.4
Input decoration added for search textfield 
singleController and multiController renamed to controller


## 1.0.3

Animated GIF not displaying in pub page -fixed

## 1.0.1
- keyboardSubscription bug fixed

## 1.0.0
- added option to customize multiple dropdown okay button
  color
  Text and textStyle
- added option to customize padding and text style of dropdown list tile.
- Fixed setState bug on onChange function.
- added outside click to hide dropdown if textfield is hidden.
- new version of flutter (Flutter 3.0.0)

## 0.0.8

Single dropdown controller text clear function fix

## 0.0.7

added attribute to add space between textfield and list widget

## 0.0.6

added controller for dropdown

## 0.0.5

state change issue fixed

## 0.0.4

added a attribute to hide clear suffix icon button from textfield
Changed class name "DropDownTextField"

## 0.0.3

bug fix

## 0.0.2

* fix bug (https://github.com/srtraj/dropdown_textfield/issues/1)



## 0.0.1

First publication
