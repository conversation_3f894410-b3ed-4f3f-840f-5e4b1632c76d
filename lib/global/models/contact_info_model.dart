import 'dart:developer';

import 'package:get/get.dart';

class ContactInfoModel {
  int? code;
  bool? success;
  ContactInfoData? data;

  ContactInfoModel({this.code, this.success, this.data});

  ContactInfoModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    success = json['success'];
    data = json['data'] != null ? ContactInfoData.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['success'] = success;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class ContactInfoData {
  String? email;
  String? phone;
  String? whatsapp;
  String? whatsappProvider;

  ContactInfoData({this.email, this.phone});

  ContactInfoData.fromJson(List<dynamic> json) {
    log('ContactInfoDataaa ${json}');

    //       {
    //             "id": 7,
    //             "section": "contact",
    //             "name": "Email",
    //             "key": "email",
    //             "value": "<EMAIL>",
    //             "type": "email",
    //             "created_at": "2024-04-22T05:00:47.000000Z",
    //             "updated_at": "2024-04-22T05:00:47.000000Z"
    //         },
    //         {
    //             "id": 8,
    //             "section": "contact",
    //             "name": "Phone",
    //             "key": "phone",
    //             "value": "+972522227534/+972543272004",
    //             "type": "phone",
    //             "created_at": "2024-04-22T05:00:47.000000Z",
    //             "updated_at": "2024-04-22T05:00:47.000000Z"
    //         },
    //         {
    //             "id": 9,
    //             "section": "contact",
    //             "name": "whatsProvider",
    //             "key": "phone",
    //             "value": "+97252222",
    //             "type": "phone",
    //             "created_at": "2024-04-22T05:00:47.000000Z",
    //             "updated_at": "2024-04-22T05:00:47.000000Z"
    //         },
    //         {
    //             "id": 10,
    //             "section": "contact",
    //             "name": "whatsUser",
    //             "key": "phone",
    //             "value": "+97252222",
    //             "type": "phone",
    //             "created_at": "2024-04-22T05:00:47.000000Z",
    //             "updated_at": "2024-04-22T05:00:47.000000Z"
    //         }

    email = json
        .firstWhereOrNull((element) => element['name'] == 'Email')?['value'];
    phone = json
        .firstWhereOrNull((element) => element['name'] == 'Phone')?['value'];
    whatsapp = json.firstWhereOrNull(
        (element) => element['name'] == 'whatsUser')?['value'];
    whatsappProvider = json.firstWhereOrNull(
        (element) => element['name'] == 'whatsProvider')?['value'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['email'] = email ?? '';
    data['phone'] = phone ?? '';
    data['support'] = whatsapp ?? '';

    return data;
  }
}
