class WorkingTimesModel {
  int? code;
  bool? success;
  WorkingTimesData? data;

  WorkingTimesModel({this.code, this.success, this.data});

  WorkingTimesModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    success = json['success'];
    data =
        json['data'] != null ? WorkingTimesData.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['success'] = success;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class WorkingTimesData {
  List<WorkingTimes>? workingTimes;
  List<Holidays>? holidays;

  WorkingTimesData({this.workingTimes, this.holidays});

  WorkingTimesData.fromJson(Map<String, dynamic> json) {
    if (json['working_times'] != null) {
      workingTimes = <WorkingTimes>[];
      json['working_times'].forEach((v) {
        workingTimes!.add(WorkingTimes.fromJson(v));
      });
    }
    if (json['holidays'] != null) {
      holidays = <Holidays>[];
      json['holidays'].forEach((v) {
        holidays!.add(Holidays.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (workingTimes != null) {
      data['working_times'] = workingTimes!.map((v) => v.toJson()).toList();
    }
    if (holidays != null) {
      data['holidays'] = holidays!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class WorkingTimes {
  int? id;
  String? day;
  String? dayName;
  String? startsAt;
  String? endsAt;

  WorkingTimes({this.id, this.day, this.dayName, this.startsAt, this.endsAt});

  WorkingTimes.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    day = json['day'];
    dayName = json['day_name'];
    startsAt = json['starts_at'];
    endsAt = json['ends_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['day'] = day;
    data['day_name'] = dayName;
    data['starts_at'] = startsAt;
    data['ends_at'] = endsAt;
    return data;
  }
}

class Holidays {
  int? id;
  String? startsAt;
  String? endsAt;

  Holidays({this.id, this.startsAt, this.endsAt});

  Holidays.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    startsAt = json['starts_at'];
    endsAt = json['ends_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['starts_at'] = startsAt;
    data['ends_at'] = endsAt;
    return data;
  }
}
