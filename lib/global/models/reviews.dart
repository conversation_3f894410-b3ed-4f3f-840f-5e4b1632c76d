import 'user.dart';

class Reviews {
  int? id;
  User? user;
  int? rating;
  String? comment;
  String? createdFrom;

  Reviews({this.id, this.user, this.rating, this.comment, this.createdFrom});

  Reviews.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    user = json['user'] != null ? User.fromJson(json['user']) : null;
    rating = json['rating'];
    comment = json['comment'];
    createdFrom = json['created_from'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    if (user != null) {
      data['user'] = user!.toJson();
    }
    data['rating'] = rating;
    data['comment'] = comment;
    data['created_from'] = createdFrom;
    return data;
  }
}
