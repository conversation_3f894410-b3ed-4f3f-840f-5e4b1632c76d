import 'package:get_clean/global/models/provider_services.dart';

class MyServicesModel {
  int? code;
  bool? success;
  List<ProviderServices>? data = [];

  MyServicesModel({this.code, this.success, this.data});

  MyServicesModel.fromJson(Map<String, dynamic> json) {
    data = <ProviderServices>[];
    code = json['code'];
    success = json['success'];
    if (json['data'] != null) {
      json['data'].forEach((v) {
        data!.add(ProviderServices.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['success'] = success;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

// class MyServiceData {
//   int? id;
//   ProviderServices? service;
//   num? materialPrice;
//   String? minHours;
//   bool? deliver;
//   bool? withTax;
//   List<PricingList>? pricingList;
//   List<CarModel>? carServices;
//
//   MyServiceData({
//     this.id,
//     this.service,
//     this.materialPrice,
//     this.withTax,
//     this.pricingList,
//     this.minHours,
//     this.carServices,
//     this.deliver,
//   });
//
//   MyServiceData.fromJson(Map<String, dynamic> json) {
//     log('DATAAAA $json');
//     id = json['id'];
//     deliver = json['deliver'] == 1;
//     service =
//         json['service'] != null ? ProviderServices.fromJson(json['service']) : null;
//     materialPrice = num.tryParse(json['material_price'].toString()) ?? 0;
//     withTax = json['with_tax'];
//     if (json['pricing_list'] != null) {
//       pricingList = <PricingList>[];
//       json['pricing_list'].forEach((v) {
//         pricingList!.add(PricingList.fromJson(v));
//       });
//     }
//     minHours = json['min_hours']?.toString();
//     if (json['carServices'] != null) {
//       //TODO-Check
//       carServices = <CarModel>[];
//       json['carServices'].forEach((v) {
//         carServices!.add(CarModel.fromJson(v));
//       });
//     }
//   }
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = <String, dynamic>{};
//     data['id'] = id;
//     if (service != null) {
//       data['service'] = service!.toJson();
//     }
//     data['material_price'] = materialPrice;
//     data['with_tax'] = withTax;
//     if (pricingList != null) {
//       data['pricing_list'] = pricingList!.map((v) => v.toJson()).toList();
//     }
//     data['min_hours'] = minHours;
//     return data;
//   }
// }

// class OneServiceModel {
//   int? id;
//   String? name;
//   String? image;
//   PricingOption? pricingOption;
//
//   OneServiceModel({this.id, this.name, this.image, this.pricingOption});
//
//   OneServiceModel.fromJson(Map<String, dynamic> json) {
//     id = json['id'];
//     name = json['name'];
//     image = json['image'];
//     pricingOption = json['pricing_option'] != null
//         ? PricingOption.fromJson(json['pricing_option'])
//         : null;
//   }
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = <String, dynamic>{};
//     data['id'] = id;
//     data['name'] = name;
//     data['image'] = image;
//     if (pricingOption != null) {
//       data['pricing_option'] = pricingOption!.toJson();
//     }
//     return data;
//   }
// }

// class PricingOption {
//   int? id;
//   String? name;
//   bool? hasTypes;
//   List<OptionTypes>? optionTypes;
//
//   PricingOption({this.id, this.name, this.hasTypes, this.optionTypes});
//
//   PricingOption.fromJson(Map<String, dynamic> json) {
//     id = json['id'];
//     name = json['name'];
//     hasTypes = json['has_types'];
//     if (json['option_types'] != null) {
//       optionTypes = <OptionTypes>[];
//       json['option_types'].forEach((v) {
//         optionTypes!.add(OptionTypes.fromJson(v));
//       });
//     }
//   }
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = <String, dynamic>{};
//     data['id'] = id;
//     data['name'] = name;
//     data['has_types'] = hasTypes;
//     if (optionTypes != null) {
//       data['option_types'] = optionTypes!.map((v) => v.toJson()).toList();
//     }
//     return data;
//   }
// }

// class OptionTypes {
//   int? id;
//   String? name;
//   List<CarModel>? carServices;
//
//   OptionTypes({this.id, this.name, this.carServices = const []});
//
//   OptionTypes.fromJson(Map<String, dynamic> json) {
//     id = json['id'];
//     name = json['name'];
//     if (json['carServices'] != null) {
//       carServices = <CarModel>[];
//       json['carServices'].forEach((v) {
//         carServices?.add(CarModel.fromJson(v));
//       });
//     }
//   }
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = <String, dynamic>{};
//     data['id'] = id;
//     data['name'] = name;
//     if (carServices != null) {
//       data['carServices'] = carServices?.map((v) => v.toJson()).toList();
//     }
//
//     return data;
//   }
// }

// class PricingList {
//   OptionTypes? type;
//   num? price;
//   num? from;
//   num? to;
//   List<CarModel>? carServices;
//
//   PricingList({this.type, this.price, this.from, this.to});
//
//   PricingList.fromJson(Map<String, dynamic> json) {
//     type = json['type'] != null ? OptionTypes.fromJson(json['type']) : null;
//     price = num.tryParse(json['price'].toString()) ?? 0;
//     from = json['from'];
//     to = json['to'];
//     // if (json['carServices'] != null) {
//     //   carServices = <CarModel>[];
//     //   json['carServices'].forEach((v) {
//     //     carServices!.add(CarModel.fromJson(v));
//     //   });
//     // }
//   }
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = <String, dynamic>{};
//     if (type != null) {
//       data['type'] = type!.toJson();
//     }
//     data['price'] = price;
//     data['from'] = from;
//     data['to'] = to;
//     return data;
//   }
// }
