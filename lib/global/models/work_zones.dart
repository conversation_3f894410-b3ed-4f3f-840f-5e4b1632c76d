import 'dart:developer';

import 'city.dart';

class WorkZones {
  int? id;
  String? name;
  City? area;

  WorkZones({this.id, this.name, this.area});

  WorkZones.fromJson(Map<String, dynamic> json) {
    log('afafasfsafasfasf ${json['district']}');
    id = json['id'];
    name = json['name'];
    area = json['district'] != null ? City.fromJson(json['district']) : null;
    // area = City.fromJson(json['district']);
    // json['area'] == null
    //     ? City(name: json['district'] ?? {})
    //     : json['area'] != null
    //         ? City.fromJson(json['area'])
    //         : null;
    // area = json['area'] != null ? City.fromJson(json['area']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    if (area != null) {
      data['area'] = area!.toJson();
    }
    return data;
  }
}
