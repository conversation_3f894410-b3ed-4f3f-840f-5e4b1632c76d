import 'package:get_clean/global/models/albums_model.dart';
import 'package:get_clean/global/models/work_zones.dart';

import 'city.dart';
import 'provider_services.dart';
import 'reviews.dart';
import 'skills.dart';

class Provider {
  int? id;
  int? userId;
  String? name;
  String? phone;
  String? image;
  String? address;
  City? city;
  City? district;
  List<ProviderServices>? services;
  List<Skills>? skills = [];
  List<WorkAreas>? workAreas;
  List<WorkingTime>? workingTime;
  List<Holidays>? holidays;
  num? rating;
  List<Reviews>? reviews;
  List<WorkZones>? workZones;
  String? type;
  String? idFile;
  String? suspended;
  String? idNumber;
  num? firstPricing;
  num? commission;
  num? tax;
  List<Map>? pricingList;
  List<AlbumsModel> albums = [];

  Provider(
      {this.id,
      this.name,
      this.phone,
      this.image,
      this.address,
      this.city,
      this.district,
      this.services,
      this.tax,
      this.suspended,
      this.skills,
      this.workAreas,
      this.workingTime,
      this.holidays,
      this.userId,
      this.rating,
      this.reviews,
      this.firstPricing,
      this.pricingList,
      this.commission,
      this.albums = const [],
      this.type});

  Provider.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userId = json['user_id'];
    name = json['name'];
    phone = json['phone'];
    image = json['image'];
    address = json['address'];
    idFile = json['id_file'];
    idNumber = json['id_number'];
    suspended = json['is_suspended']?.toString();
    city = json['city'] == null
        ? null
        : json['city'].runtimeType == String
            ? City(name: json['city'])
            : City.fromJson(json['city']);

    district = json['district'] == null
        ? null
        : json['district'].runtimeType == String
            ? City(name: json['district'])
            : City.fromJson(json['district']);

    if (json['services'] != null) {
      // log('PROVIDERSERVICEEEE ${json['services']}');

      services = <ProviderServices>[];
      json['services'].forEach((v) {
        services!.add(ProviderServices.fromJson(v));
      });
    }
    if (json['album'] != null && json['album'].isNotEmpty) {
      albums = <AlbumsModel>[];
      json['album'].forEach((v) {
        albums.add(AlbumsModel.fromJson(v));
      });
    }
    if (json['skills'] != null) {
      skills = <Skills>[];
      json['skills'].forEach((v) {
        skills!.add(Skills.fromJson(v));
      });
    }
    // if (json['work_zones'] != null) {
    //   workAreas = <WorkAreas>[];
    //
    // }
    // log('aasfsfas ${json['work_zones']}');
    //
    if (json['work_zones'] != null) {
      workZones = <WorkZones>[];
      workAreas = <WorkAreas>[];

      json['work_zones'].forEach((v) {
        workZones!.add(WorkZones.fromJson(v));
        workAreas!.add(WorkAreas.fromJson(v)); //TODO-Check-API
      });
      // workAreas =
      //     workZones!.map((e) => WorkAreas(id: e.id, name: e.name)).toList();
    }
    if (json['working_time'] != null) {
      workingTime = <WorkingTime>[];
      json['working_time'].forEach((v) {
        workingTime!.add(WorkingTime.fromJson(v));
      });
    }
    tax = num.tryParse(json['tax'].toString()) ?? 0;
    if (json['holidays'] != null) {
      holidays = <Holidays>[];
      json['holidays'].forEach((v) {
        holidays!.add(Holidays.fromJson(v));
      });
    }
    rating = num.tryParse(json['rating'].toString()) ?? 0;
    if (json['reviews'] != null) {
      reviews = <Reviews>[];
      json['reviews'].forEach((v) {
        reviews!.add(Reviews.fromJson(v));
      });
    }
    type = json['type'];

    commission = json['commission'];
  }

  //? from String (dynamic link)
  toParams() {
    return '?id=$id&name=$name&phone=$phone&image=$image&address=$address'
        '&id_file=$idFile&id_number=$idNumber&city=${city?.name}'
        '&district=${district?.name}&rating=$rating&commission=$commission'
        '&work_areas=${workAreas?.map((e) => e.name).toString()}'
        '&services=${services?.map((e) => e.name).toString()}'
        '&skills=${skills?.map((e) => e.name).toString()}'
        '&work_zones=${workZones?.map((e) => e.name).toString()}'
        '&working_time_start=${workingTime?.map((e) => e.startsAt)}'
        '&working_time_end=${workingTime?.map((e) => e.endsAt)}'
        '&working_time_day=${workingTime?.map((e) => e.day)}'
        '&holiday_start=${holidays?.map((e) => e.startsAt)}'
        '&holiday_end=${holidays?.map((e) => e.endsAt)}';
  }

  //? from String (dynamic link)
  Provider.fromDynamicLink(Map params) {
    id = int.tryParse(params['id']) ?? 0;
    userId = int.tryParse(params['user_id']) ?? 0;
    name = params['name'];
    phone = params['phone'];
    image = params['image'];
    address = params['address'];
    idFile = params['id_file'];
    idNumber = params['id_number'];
    city = City(name: params['city']);

    district = City(name: params['district']);
    rating = num.tryParse(params['rating']) ?? 0;
    commission = num.tryParse(params['commission']) ?? 0;

    workAreas = [];

    final workAreasString = params['work_areas'].toString();

    if (workAreasString.isNotEmpty) {
      workAreasString.split(',').forEach((element) {
        workAreas!.add(WorkAreas(name: element));
      });
    }

    services = [];

    final servicesString = params['services'].toString();

    if (servicesString.isNotEmpty) {
      servicesString.split(',').forEach((element) {
        services!.add(ProviderServices(name: element));
      });
    }

    skills = [];

    final skillsString = params['skills'].toString();

    if (skillsString.isNotEmpty) {
      skillsString.split(',').forEach((element) {
        skills!.add(Skills(name: element));
      });
    }

    workZones = [];

    final workZonesString = params['work_zones'].toString();

    if (workZonesString.isNotEmpty) {
      workZonesString.split(',').forEach((element) {
        workZones!.add(WorkZones(name: element));
      });
    }

    workingTime = [];

    final workingTimeDayString = params['working_time_day'].toString();

    if (workingTimeDayString.isNotEmpty) {
      workingTimeDayString.split(',').forEach((element) {
        workingTime!.add(WorkingTime(day: element));
      });
    }

    final workingTimeString = params['working_time_start'].toString();

    if (workingTimeString.isNotEmpty) {
      workingTimeString.split(',').forEach((element) {
        //? copyWith
        for (int i = 0; i < workingTime!.length; i++) {
          if (workingTime![i].day == workingTimeDayString.split(',')[i]) {
            workingTime![i] = workingTime![i].copyWith(startsAt: element);
          }
        }
      });
    }

    final workingTimeEndString = params['working_time_end'].toString();

    if (workingTimeEndString.isNotEmpty) {
      workingTimeEndString.split(',').forEach((element) {
        //? copyWith
        for (int i = 0; i < workingTime!.length; i++) {
          if (workingTime![i].day == workingTimeDayString.split(',')[i]) {
            workingTime![i] = workingTime![i].copyWith(endsAt: element);
          }
        }
      });
    }

    holidays = [];

    final holidaysStartString = params['holiday_start'].toString();

    if (holidaysStartString.isNotEmpty) {
      holidaysStartString.split(',').forEach((element) {
        holidays!.add(Holidays(startsAt: element));
      });
    }

    final holidaysEndString = params['holiday_end'].toString();

    if (holidaysEndString.isNotEmpty) {
      holidaysEndString.split(',').forEach((element) {
        //? copyWith

        for (int i = 0; i < holidays!.length; i++) {
          holidays![i] = holidays![i].copyWith(endsAt: element);
        }
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['user_id'] = userId;
    data['name'] = name;
    data['phone'] = phone;
    data['image'] = image;
    data['address'] = address;
    data['id_file'] = idFile;
    data['id_number'] = idNumber;
    data['tax'] = tax;
    if (city != null) {
      data['city'] = city!.toJson();
    }
    if (district != null) {
      data['district'] = district!.toJson();
    }
    if (services != null) {
      data['services'] = services!.map((v) => v.toJson()).toList();
    }
    if (workZones != null) {
      data['work_zones'] = workZones!.map((v) => v.toJson()).toList();
    }
    if (skills != null) {
      data['skills'] = skills!.map((v) => v.toJson()).toList();
    }
    if (workAreas != null) {
      data['work_areas'] = workAreas!.map((v) => v.toJson()).toList();
    }
    if (workingTime != null) {
      data['working_time'] = workingTime!.map((v) => v.toJson()).toList();
    }
    if (holidays != null) {
      data['holidays'] = holidays!.map((v) => v.toJson()).toList();
    }
    data['rating'] = rating;
    if (reviews != null) {
      data['reviews'] = reviews!.map((v) => v.toJson()).toList();
    }
    if (firstPricing != null) {
      data['first_pricing'] = firstPricing;
    }

    if (pricingList != null) {
      data['pricing_list'] = pricingList!.map((v) => v).toList();
    }

    data['type'] = type;

    data['commission'] = commission;
    return data;
  }
}

extension RemoveBractes on String {
  String removeBrackets() {
    return replaceAll('(', '').replaceAll(')', '');
    // return replaceAll('(', '').replaceAll(')', '');
  }
}
