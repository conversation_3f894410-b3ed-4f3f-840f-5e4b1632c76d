import 'city.dart';

class Cities {
  int? code;
  bool? success;
  List<City>? data;

  Cities({this.code, this.success, this.data});

  Cities.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    success = json['success'];
    if (json['data'] != null) {
      data = <City>[];
      json['data'].forEach((v) {
        data!.add(City.fromJson(v));
      });
    }
  }

  // fromListJson
  // Cities.fromListJson(List<dynamic> json) {
  //   data = <City>[];
  //   for (var v in json) {
  //     data!.add(City.fromJson(v));
  //   }
  // }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['success'] = success;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}
