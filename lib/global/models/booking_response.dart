import 'package:get_clean/global/models/user_booking.dart';

class BookingResponse {
  int? code;
  bool? success;
  BookingData? data;
  String? message;

  BookingResponse({this.code, this.success, this.data, this.message});

  BookingResponse.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    success = json['success'];
    data = json['data'] != null ? BookingData.fromJson(json['data']) : null;
    message = json['message'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['success'] = success;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['message'] = message;
    return data;
  }
}
