class PrivacyPolicyModel {
  int? code;
  bool? success;
  PrivacyPolicyData? data;

  PrivacyPolicyModel({this.code, this.success, this.data});

  PrivacyPolicyModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    success = json['success'];
    data =
        json['data'] != null ? PrivacyPolicyData.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['success'] = success;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class PrivacyPolicyData {
  int? id;
  String? title;
  String? text;

  PrivacyPolicyData({this.id, this.title, this.text});

  PrivacyPolicyData.fromJson(Map<String, dynamic> json) {
    id = json['id'] ?? '';
    title = json['title'] ?? 'Privacy Policy';
    text = json['text'] ?? 'No Data';
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['title'] = title;
    data['text'] = text;
    return data;
  }
}
