class AboutUsModel {
  int? code;
  bool? success;
  AboutUsData? data;

  AboutUsModel({this.code, this.success, this.data});

  AboutUsModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    success = json['success'];
    data = json['data'] != null ? AboutUsData.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['success'] = success;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class AboutUsData {
  int? id;
  String? title;
  String? text;

  AboutUsData({this.id, this.title, this.text});

  AboutUsData.fromJson(Map<String, dynamic> json) {
    id = json['id'] ?? '';
    title = json['title'] ?? 'About Us';
    text = json['text'] ?? 'No Data';
  }

  Map<String, dynamic> toJ<PERSON>() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['title'] = title;
    data['text'] = text;
    return data;
  }
}
