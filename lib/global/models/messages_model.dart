class ChatModel {
  int? code;
  bool? success;
  MessageData? data;

  ChatModel({this.code, this.success, this.data});

  ChatModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    success = json['success'];
    data = json['data'] != null ? MessageData.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['success'] = success;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class MessageData {
  String? role;
  String? lastMessageAt;
  bool? readLastMessage;
  List<Messages>? messages;

  MessageData(
      {this.role, this.lastMessageAt, this.readLastMessage, this.messages});

  MessageData.fromJson(Map<String, dynamic> json) {
    role = json['role'];
    lastMessageAt = json['last_message_at'];
    readLastMessage = json['read_last_message'];
    if (json['messages'] != null) {
      messages = <Messages>[];
      json['messages'].forEach((v) {
        messages!.add(Messages.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['role'] = role;
    data['last_message_at'] = lastMessageAt;
    data['read_last_message'] = readLastMessage;
    if (messages != null) {
      data['messages'] = messages!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Messages {
  int? id;
  String? message;
  Sender? sender;
  bool? sentByMe;
  String? sentAt;

  Messages({this.id, this.message, this.sender, this.sentByMe, this.sentAt});

  Messages.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    message = json['message'];
    sender = json['sender'] != null ? Sender.fromJson(json['sender']) : null;
    sentByMe = json['sent_by_me'];
    sentAt = json['sent_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['message'] = message;
    if (sender != null) {
      data['sender'] = sender!.toJson();
    }
    data['sent_by_me'] = sentByMe;
    data['sent_at'] = sentAt;
    return data;
  }
}

class Sender {
  int? id;
  String? name;
  String? phone;
  String? image;
  String? type;

  Sender({this.id, this.name, this.phone, this.image, this.type});

  Sender.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    phone = json['phone'];
    image = json['image'];
    type = json['type'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['phone'] = phone;
    data['image'] = image;
    data['type'] = type;
    return data;
  }
}
