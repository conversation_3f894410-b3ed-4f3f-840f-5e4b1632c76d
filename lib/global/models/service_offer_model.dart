class ServiceOffersModel {
  int? code;
  bool? success;
  List<ServiceOfferData>? data = [];

  ServiceOffersModel({this.code, this.success, this.data});

  ServiceOffersModel.fromJson(Map<String, dynamic> json) {
    data = <ServiceOfferData>[];
    code = json['code'];
    success = json['success'];
    if (json['data'] != null) {
      json['data'].forEach((v) {
        data!.add(ServiceOfferData.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['success'] = success;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ServiceOfferData {
  //        "id": 17,
  //             "name": "{\"2\": \"Offer\", \"3\": \"عروض\", \"4\": \"Offer\"}",
  //             "image": "services/ibIkIJKuyNily3lNlT98gziP2grdi7Rm4RrPOYqY.png",
  //             "is_active": 1,
  //             "option_id": 5,
  //             "tracking": 0,
  //             "duration": 1,
  //             "deleted_at": null,
  //             "created_at": "2024-01-09T11:01:53.000000Z",
  //             "updated_at": "2024-01-09T11:43:32.000000Z",
  int? id;
  String? name;
  String? image;
  bool? isActive;
  int? optionId;
  int? tracking;
  int? duration;
  String? createdAt;
  bool? subscribeStatus;

  ServiceOfferData({
    this.id,
    this.name,
    this.image,
    this.isActive,
    this.optionId,
    this.tracking,
    this.duration,
    this.createdAt,
    this.subscribeStatus,
  });

  ServiceOfferData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    image = json['image'];
    isActive = json['is_active'] == 1 ? true : false;
    optionId = json['option_id'];
    tracking = json['tracking'];
    duration = json['duration'];
    createdAt = json['created_at'];
    subscribeStatus = json['subscribe_status'] ?? false;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['image'] = image;
    data['is_active'] = isActive == true ? 1 : 0;
    data['option_id'] = optionId;
    data['tracking'] = tracking;
    data['duration'] = duration;
    data['created_at'] = createdAt;
    data['subscribe_status'] = subscribeStatus;
    return data;
  }
}
