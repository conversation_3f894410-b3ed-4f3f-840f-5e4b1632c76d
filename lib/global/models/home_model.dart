import 'dart:developer';

import 'package:get_clean/global/models/provider.dart';
import 'package:get_clean/global/models/provider_services.dart';

import 'city.dart';

class HomeModel {
  int? code;
  bool? success;
  Data? data;

  HomeModel({this.code, this.success, this.data});

  HomeModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    success = json['success'];
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['success'] = success;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class Data {
  List<Sliders>? sliders = [];
  List<ProviderServices>? services = [];
  List<ProviderServices>? homeServices;

  List<Offers>? offers = [];
  List<PopularServices>? popularServices = [];

  Data({this.sliders, this.services, this.offers, this.popularServices});

  Data.fromJson(Map<String, dynamic> json) {
    log('Sliddders ${json['home_services']}');
    if (json['sliders'] != null) {
      sliders = <Sliders>[];
      json['sliders'].forEach((v) {
        sliders!.add(Sliders.fromJson(v));
      });
    }
    if (json['services'] != null) {
      services = <ProviderServices>[];
      json['services'].forEach((v) {
        services!.add(ProviderServices.fromJson(v));
      });
    }
    if (json['home_services'] != null) {
      homeServices = <ProviderServices>[];
      json['home_services'].forEach((v) {
        // homeServices!.add(); //TODO-check-API
        v['service'].forEach((element) {
          homeServices!.add(ProviderServices.fromJson(element));
        });
      });
    }
    if (json['offers'] != null) {
      offers = <Offers>[];
      json['offers'].forEach((v) {
        offers!.add(Offers.fromJson(v));
      });
    }
    if (json['popular_services'] != null) {
      popularServices = <PopularServices>[];
      json['popular_services'].forEach((v) {
        popularServices!.add(PopularServices.fromJson(v)); //TODO-check-API
        // v['service'].forEach((element) {
        //   popularServices!
        //       .add(PopularServices(service: Services.fromJson(element)));
        // });
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (sliders != null) {
      data['sliders'] = sliders!.map((v) => v.toJson()).toList();
    }
    if (services != null) {
      data['services'] = services!.map((v) => v.toJson()).toList();
    }
    if (offers != null) {
      data['offers'] = offers!.map((v) => v.toJson()).toList();
    }
    if (popularServices != null) {
      data['popular_services'] =
          popularServices!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Sliders {
  int? id;
  String? image;
  Clickable? clickable;

  Sliders({this.id, this.image, this.clickable});

  Sliders.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    image = json['image'];
    clickable = json['clickable'] != null
        ? Clickable.fromJson(json['clickable'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['image'] = image;
    if (clickable != null) {
      data['clickable'] = clickable!.toJson();
    }
    return data;
  }
}

class Clickable {
  String? type;
  Provider? provider;
  String? url;

  Clickable({this.type, this.provider});

  Clickable.fromJson(Map<String, dynamic> json) {
    type = json['type'];
    provider =
        json['provider'] != null ? Provider.fromJson(json['provider']) : null;
    url = json['url'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['type'] = type;
    if (provider != null) {
      data['provider'] = provider!.toJson();
    }
    if (url != null) {
      data['url'] = url;
    }
    return data;
  }
}

// class Services {
//   int? id;
//   String? name;
//   int? pricingOptionId; //? 1 -> Hour, 2 -> Meter
//   List<Service>? services;
//
//   Services({this.id, this.name, this.services, this.pricingOptionId});
//
//   Services.fromJson(Map<String, dynamic> json) {
//     id = json['id'];
//     name = json['name'];
//     pricingOptionId =
//         json['pricing_option'] != null ? json['pricing_option']['id'] : null;
//
//     log('asfsasafas $pricingOptionId');
//
//     if (json['services'] != null) {
//       services = <Service>[];
//       json['services'].forEach((v) {
//         services!.add(Service.fromJson(v));
//       });
//     }
//   }
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = <String, dynamic>{};
//     data['id'] = id;
//     data['name'] = name;
//     if (services != null) {
//       data['services'] = services!.map((v) => v.toJson()).toList();
//     }
//     return data;
//   }
// }

// class Service {
//   int? id;
//   String? name;
//   String? image;
//
//   Service({this.id, this.name, this.image});
//
//   Service.fromJson(Map<String, dynamic> json) {
//     id = json['id'];
//     name = json['name'];
//     image = json['image'];
//   }
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = <String, dynamic>{};
//     data['id'] = id;
//     data['name'] = name;
//     data['image'] = image;
//     return data;
//   }
// }

class Offers {
  int? id;
  String? name;
  String? description;
  Address? address;
  int? price;
  int? duration;
  String? startDate;
  String? endDate;
  bool? isActive;
  String? image;
  Provider? provider;

  Offers(
      {this.id,
      this.name,
      this.description,
      this.address,
      this.price,
      this.duration,
      this.startDate,
      this.endDate,
      this.isActive,
      this.image,
      this.provider});

  Offers.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    description = json['description'];
    address =
        json['address'] != null ? Address.fromJson(json['address']) : null;
    price = json['price'];
    duration = json['duration'];
    startDate = json['start_date'];
    endDate = json['end_date'];
    isActive = json['is_active'];
    image = json['image'];
    provider =
        json['provider'] != null ? Provider.fromJson(json['provider']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['description'] = description;
    if (address != null) {
      data['address'] = address!.toJson();
    }
    data['price'] = price;
    data['duration'] = duration;
    data['start_date'] = startDate;
    data['end_date'] = endDate;
    data['is_active'] = isActive;
    data['image'] = image;
    if (provider != null) {
      data['provider'] = provider!.toJson();
    }
    return data;
  }
}

class Address {
  int? id;
  String? name;
  City? area;

  Address({this.id, this.name, this.area});

  Address.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    area = json['area'] != null ? City.fromJson(json['area']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    if (area != null) {
      data['area'] = area!.toJson();
    }
    return data;
  }
}

class PopularServices {
  ProviderServices? service;
  List<Provider>? providers;

  PopularServices({this.service, this.providers});

  PopularServices.fromJson(Map<String, dynamic> json) {
    log('asfsafsafokdsg ${json}');

    service = json['service'] != null
        ? ProviderServices.fromJson(json['service'])
        : null;
    if (json['providers'] != null) {
      providers = <Provider>[];
      json['providers'].forEach((v) {
        providers!.add(Provider.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (service != null) {
      data['service'] = service!.toJson();
    }
    if (providers != null) {
      data['providers'] = providers!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}
