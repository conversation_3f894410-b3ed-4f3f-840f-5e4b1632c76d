import 'skills.dart';

class SkillsModel {
  int? code;
  bool? success;
  List<Skills>? data;

  SkillsModel({this.code, this.success, this.data});

  SkillsModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    success = json['success'];
    if (json['data'] != null) {
      data = <Skills>[];
      json['data'].forEach((v) {
        data!.add(Skills.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['success'] = success;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}
