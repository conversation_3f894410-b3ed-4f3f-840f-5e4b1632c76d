class TermsOfServiceModel {
  int? code;
  bool? success;
  TermsOfServiceData? data;

  TermsOfServiceModel({this.code, this.success, this.data});

  TermsOfServiceModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    success = json['success'];
    data =
        json['data'] != null ? TermsOfServiceData.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['success'] = success;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class TermsOfServiceData {
  int? id;
  String? title;
  String? text;

  TermsOfServiceData({this.id, this.title, this.text});

  TermsOfServiceData.fromJson(Map<String, dynamic> json) {
    id = json['id'] ?? '';
    title = json['title'] ?? 'Terms Of Service';
    text = json['text'] ?? 'No Data';
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['title'] = title;
    data['text'] = text;
    return data;
  }
}
