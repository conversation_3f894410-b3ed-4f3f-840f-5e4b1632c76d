import 'package:get_clean/global/models/provider.dart';
import 'package:get_clean/global/models/provider_services.dart';

class ProviderAvilableTimes {
  int? code;
  bool? success;
  Data? data;

  ProviderAvilableTimes({this.code, this.success, this.data});

  ProviderAvilableTimes.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    success = json['success'];
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['success'] = success;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class Data {
  Provider? provider;
  ProviderServices? service;
  List<BookingDays>? bookingDays;

  Data({this.provider, this.service, this.bookingDays});

  Data.fromJson(Map<String, dynamic> json) {
    provider =
        json['provider'] != null ? Provider.fromJson(json['provider']) : null;
    service = json['service'] != null
        ? ProviderServices.fromJson(json['service'])
        : null;
    if (json['booking_days'] != null) {
      bookingDays = <BookingDays>[];
      json['booking_days'].forEach((v) {
        bookingDays!.add(BookingDays.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (provider != null) {
      data['provider'] = provider!.toJson();
    }
    if (service != null) {
      data['service'] = service!.toJson();
    }
    if (bookingDays != null) {
      data['booking_days'] = bookingDays!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

// class Services {
//   int? id;
//   String? name;
//   String? image;
//   PricingOption? pricingOption;
//   int? materialPrice;
//   List<PricingList>? pricingList;
//
//   Services(
//       {this.id,
//       this.name,
//       this.image,
//       this.pricingOption,
//       this.materialPrice,
//       this.pricingList});
//
//   Services.fromJson(Map<String, dynamic> json) {
//     id = json['id'];
//     name = json['name'];
//     image = json['image'];
//     pricingOption = json['pricing_option'] != null
//         ? PricingOption.fromJson(json['pricing_option'])
//         : null;
//     materialPrice = json['material_price'];
//     if (json['pricing_list'] != null) {
//       pricingList = <PricingList>[];
//       json['pricing_list'].forEach((v) {
//         pricingList!.add(PricingList.fromJson(v));
//       });
//     }
//   }
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = <String, dynamic>{};
//     data['id'] = id;
//     data['name'] = name;
//     data['image'] = image;
//     if (pricingOption != null) {
//       data['pricing_option'] = pricingOption!.toJson();
//     }
//     data['material_price'] = materialPrice;
//     if (pricingList != null) {
//       data['pricing_list'] = pricingList!.map((v) => v.toJson()).toList();
//     }
//     return data;
//   }
// }
//
// class PricingOption {
//   int? id;
//   String? name;
//   bool? hasTypes;
//
//   PricingOption({this.id, this.name, this.hasTypes});
//
//   PricingOption.fromJson(Map<String, dynamic> json) {
//     id = json['id'];
//     name = json['name'];
//     hasTypes = json['has_types'];
//   }
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = <String, dynamic>{};
//     data['id'] = id;
//     data['name'] = name;
//     data['has_types'] = hasTypes;
//     return data;
//   }
// }
//
// class PricingList {
//   int? from;
//   int? to;
//   double? price;
//   City? type;
//
//   PricingList({this.from, this.to, this.price, this.type});
//
//   PricingList.fromJson(Map<String, dynamic> json) {
//     from = json['from'];
//     to = json['to'];
//     price = json['price'];
//     type = json['type'] != null ? City.fromJson(json['type']) : null;
//   }
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = <String, dynamic>{};
//     data['from'] = from;
//     data['to'] = to;
//     data['price'] = price;
//     if (type != null) {
//       data['type'] = type!.toJson();
//     }
//     return data;
//   }
// }
//
// class WorkAreas {
//   int? id;
//   String? name;
//   City? area;
//
//   WorkAreas({this.id, this.name, this.area});
//
//   WorkAreas.fromJson(Map<String, dynamic> json) {
//     id = json['id'];
//     name = json['name'];
//     area = json['area'] != null ? City.fromJson(json['area']) : null;
//   }
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = <String, dynamic>{};
//     data['id'] = id;
//     data['name'] = name;
//     if (area != null) {
//       data['area'] = area!.toJson();
//     }
//     return data;
//   }
// }
//
// class WorkingTime {
//   int? id;
//   String? day;
//   String? dayName;
//   String? startsAt;
//   String? endsAt;
//
//   WorkingTime({this.id, this.day, this.dayName, this.startsAt, this.endsAt});
//
//   WorkingTime.fromJson(Map<String, dynamic> json) {
//     id = json['id'];
//     day = json['day'];
//     dayName = json['day_name'];
//     startsAt = json['starts_at'];
//     endsAt = json['ends_at'];
//   }
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = <String, dynamic>{};
//     data['id'] = id;
//     data['day'] = day;
//     data['day_name'] = dayName;
//     data['starts_at'] = startsAt;
//     data['ends_at'] = endsAt;
//     return data;
//   }
// }
//
// class Service {
//   int? id;
//   String? name;
//   String? image;
//   PricingOptions? pricingOption;
//
//   Service({this.id, this.name, this.image, this.pricingOption});
//
//   Service.fromJson(Map<String, dynamic> json) {
//     id = json['id'];
//     name = json['name'];
//     image = json['image'];
//     pricingOption = json['pricing_option'] != null
//         ? PricingOptions.fromJson(json['pricing_option'])
//         : null;
//   }
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = <String, dynamic>{};
//     data['id'] = id;
//     data['name'] = name;
//     data['image'] = image;
//     if (pricingOption != null) {
//       data['pricing_option'] = pricingOption!.toJson();
//     }
//     return data;
//   }
// }
//
// class PricingOptions {
//   int? id;
//   String? name;
//   bool? hasTypes;
//
//   PricingOptions({this.id, this.name, this.hasTypes});
//
//   PricingOptions.fromJson(Map<String, dynamic> json) {
//     id = json['id'];
//     name = json['name'];
//     hasTypes = json['has_types'];
//   }
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = <String, dynamic>{};
//     data['id'] = id;
//     data['name'] = name;
//     data['has_types'] = hasTypes;
//     return data;
//   }
// }

class BookingDays {
  String? date;
  DateDetails? dateDetails;
  List<String>? bookingTimes;

  BookingDays({this.date, this.dateDetails, this.bookingTimes});

  BookingDays.fromJson(Map<String, dynamic> json) {
    date = json['date'];
    dateDetails = json['date_details'] != null
        ? DateDetails.fromJson(json['date_details'])
        : null;
    bookingTimes = json['booking_times'].cast<String>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['date'] = date;
    if (dateDetails != null) {
      data['date_details'] = dateDetails!.toJson();
    }
    data['booking_times'] = bookingTimes;
    return data;
  }
}

class DateDetails {
  String? dayNumber;
  String? dayName;

  DateDetails({this.dayNumber, this.dayName});

  DateDetails.fromJson(Map<String, dynamic> json) {
    dayNumber = json['day_number'];
    dayName = json['day_name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['day_number'] = dayNumber;
    data['day_name'] = dayName;
    return data;
  }
}
