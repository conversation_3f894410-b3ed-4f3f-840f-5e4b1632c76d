import 'package:get_clean/global/models/provider.dart';

class Providers {
  int? code;
  bool? success;
  List<Provider>? data;

  Providers({this.code, this.success, this.data});

  Providers.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    success = json['success'];
    if (json['data'] != null) {
      data = <Provider>[];
      json['data'].forEach((v) {
        data!.add(Provider.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['success'] = success;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}
