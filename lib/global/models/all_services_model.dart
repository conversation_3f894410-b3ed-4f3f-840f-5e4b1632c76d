import 'package:get_clean/global/models/provider_services.dart';

class AllServicesModel {
  int? code;
  bool? success;
  List<ProviderServices>? data;

  AllServicesModel({this.code, this.success, this.data});

  AllServicesModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    success = json['success'];
    if (json['data'] != null) {
      data = <ProviderServices>[];
      json['data'].forEach((v) {
        data!.add(ProviderServices.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['code'] = code;
    data['success'] = success;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

// class ProviderServices {
//   int? id;
//   String? name;
//   String? image;
//   bool? tracking;
//   PricingOption? pricingOption;
//   List<TypesSettings>? typesSettings = [];
//   bool? deliver;
//   num? materialPrice;
//   List<PricingList>? pricingList;
//   num? minHours;
//
//   ProviderServices({
//     this.id,
//     this.name,
//     this.image,
//     this.pricingOption,
//     this.typesSettings,
//     this.tracking,
//     this.deliver,
//   });
//
//   ProviderServices.fromJson(Map<String, dynamic> json) {
//     log('ServicesDATAAAAA ${json}');
//
//     id = json['id'];
//     name = json['name'];
//     image = json['image'];
//     tracking = json['tracking'];
//     deliver = json['deliver'];
//     if (json['types_settings'] != null) {
//       for (var type in json['types_settings']) {
//         typesSettings!.add(TypesSettings.fromJson(type));
//       }
//     }
//     pricingOption = json['pricing_option'] != null
//         ? PricingOption.fromJson(json['pricing_option'])
//         : null;
//   }
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = <String, dynamic>{};
//     data['id'] = id;
//     data['name'] = name;
//     data['image'] = image;
//     data['tracking'] = tracking;
//     data['deliver'] = deliver;
//     if (pricingOption != null) {
//       data['pricing_option'] = pricingOption!.toJson();
//     }
//     return data;
//   }
// }
//
// class PricingOption {
//   int? id;
//   String? name;
//   bool? hasTypes;
//   List<OptionTypes>? optionTypes;
//
//   PricingOption({this.id, this.name, this.hasTypes, this.optionTypes});
//
//   PricingOption.fromJson(Map<String, dynamic> json) {
//     id = json['id'];
//
//     name = json['name'];
//     hasTypes = json['has_types'];
//     if (json['option_types'] != null) {
//       optionTypes = <OptionTypes>[];
//       json['option_types'].forEach((v) {
//         optionTypes!.add(OptionTypes.fromJson(v));
//       });
//     }
//   }
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = <String, dynamic>{};
//     data['id'] = id;
//     data['name'] = name;
//     data['has_types'] = hasTypes;
//     if (optionTypes != null) {
//       data['option_types'] = optionTypes!.map((v) => v.toJson()).toList();
//     }
//     return data;
//   }
// }
