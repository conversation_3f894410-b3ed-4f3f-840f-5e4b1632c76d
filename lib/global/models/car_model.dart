import 'package:flutter/material.dart';

class CarModel {
  int id = 0;
  TextEditingController name = TextEditingController();
  TextEditingController price = TextEditingController();
  List<CarModel> carServices = [];

  CarModel(this.id, this.name, this.carServices);

  CarModel.withPrice(this.id, this.name, this.price);

  CarModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = TextEditingController(text: json['name'] ?? '');
    price = TextEditingController(text: json['price']?.toString() ?? '');
    if (json['carServices'] != null) {
      carServices = <CarModel>[];
      json['carServices'].forEach((v) {
        carServices.add(CarModel.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name.text;
    data['price'] = price.text;
    if (carServices.isNotEmpty) {
      data['carServices'] = carServices.map((v) => v.toJson()).toList();
    }
    return data;
  }

  //? empty
  CarModel.empty({
    this.id = 0,
  });
}
