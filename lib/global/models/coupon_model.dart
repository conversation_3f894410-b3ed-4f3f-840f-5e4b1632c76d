class CouponModel {
  bool? success;
  int? code;
  String? message;
  CouponData? data;

  CouponModel({
    this.success,
    this.code,
    this.message,
    this.data,
  });

  CouponModel.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    code = json['code'];
    message = json['message'];
    data = json['data'] != null ? CouponData.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['success'] = success;
    data['code'] = code;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class CouponData {
  int? id;
  String? code;
  String? discountValue;
  String? discountType;
  int? usageCount;
  int? usageLimit;
  String? startDate;
  String? endDate;

  CouponData({
    this.id,
    this.code,
    this.discountValue,
    this.discountType,
    this.usageCount,
    this.usageLimit,
    this.startDate,
    this.endDate,
  });

  CouponData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    code = json['code'];
    discountValue = json['discount_value'];
    discountType = json['discount_type'];
    usageCount = json['usage_count'];
    usageLimit = json['usage_limit'];
    startDate = json['start_date'];
    endDate = json['end_date'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['code'] = code;
    data['discount_value'] = discountValue;
    data['discount_type'] = discountType;
    data['usage_count'] = usageCount;
    data['usage_limit'] = usageLimit;
    data['start_date'] = startDate;
    data['end_date'] = endDate;
    return data;
  }
}
