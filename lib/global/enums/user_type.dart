enum UserType {
  user,
  company,
  provider,
  none,
}

extension Value on UserType {
  String get getValue {
    switch (this) {
      case UserType.user:
        return 'user';
      case UserType.company:
        return 'company';
      case UserType.provider:
        return 'provider';
      default:
        return '';
    }
  }
}

extension Type on String {
  UserType get userType {
    switch (this) {
      case 'user':
        return UserType.user;
      case 'company':
        return UserType.company;
      case 'provider':
        return UserType.provider;
      default:
        return UserType.none;
    }
  }
}
