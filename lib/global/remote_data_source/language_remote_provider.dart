import 'dart:developer';

import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/dio/dio_helper.dart';
import 'package:get_clean/global/models/keys_model.dart';
import 'package:get_clean/global/models/language_model.dart';

class LanguageRemoteProvider {
  DioHelper helper = DioHelper();

  Future<LanguagesModel?> getLanguages() async {
    try {
      final response = await helper.getData(getLanguagesURL);

      log('LanguageResponse $response');

      if (response['success'] == true) {
        // if (response['success'] == true) {//TODO-Api
        return LanguagesModel.fromJson(response);
      } else {
        return null;
      }
    } catch (e) {
      log(e.toString());
      return null;
    }
  }

  Future<KeysModel?> getKeys(String keysURL) async {
    try {
      final response = await helper.getData(keysURL);
      return KeysModel.fromJson(response);
    } catch (e) {
      log(e.toString());
      return null;
    }
  }
}
