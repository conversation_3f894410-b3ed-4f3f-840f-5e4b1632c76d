import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/signup/controllers/signup_controller.dart';

import 'area_and_city_widget.dart';

class UserSignupWidget extends StatelessWidget {
  const UserSignupWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<SignupController>(builder: (controller) {
      return const Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AreaAndCityWidget(),
        ],
      );
    });
  }
}
