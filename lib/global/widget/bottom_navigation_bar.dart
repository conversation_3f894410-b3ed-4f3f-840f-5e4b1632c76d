import 'package:flutter/material.dart' hide Badge;
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/home/<USER>/home_controller.dart';

import '../../../../global/controllers/global_values_controller.dart';
import '../../../../global/controllers/language_controller.dart';

class BottomNavBarWidget extends GetView<HomeController> {
  const BottomNavBarWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: Get.width,
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey[400]!,
            blurRadius: 6,
          ),
        ],
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              InkWell(
                onTap: controller.onHomeTap,
                child: Column(
                  children: [
                    SvgPicture.asset(
                      'assets/images/home_icon.svg',
                    ),
                    Text(
                      Get.find<LanguageController>().keys.value.home!,
                    ),
                  ],
                ),
              ),
              if (Get.find<GlobalValuesController>().isLoggedIn.value)
                InkWell(
                  onTap: controller.onMyOrdersTap,
                  child: Column(
                    children: [
                      SvgPicture.asset(
                        'assets/images/calendar_icon.svg',
                      ),
                      Text(
                        controller.user.value.type == 'user'
                            ? Get.find<LanguageController>()
                                .keys
                                .value
                                .myBooking!
                            : Get.find<LanguageController>()
                                .keys
                                .value
                                .myOrders!,
                      ),
                    ],
                  ),
                ),
              InkWell(
                onTap: controller.onPackagesTap,
                child: Column(
                  children: [
                    SvgPicture.asset(
                      'assets/images/packages_icon.svg',
                    ),
                    Text(
                      Get.find<LanguageController>().keys.value.offers!,
                    ),
                  ],
                ),
              ),
              InkWell(
                onTap: controller.onSupportTap,
                child: Column(
                  children: [
                    SvgPicture.asset(
                      'assets/images/whatsapp.svg',
                      height: 32,
                      width: 35,
                      color: Colors.green,
                    ),
                    Text(
                      Get.find<LanguageController>().keys.value.support!,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
