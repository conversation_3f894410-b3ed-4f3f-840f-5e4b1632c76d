// ignore_for_file: prefer_typing_uninitialized_variables

import 'package:flutter/material.dart';

import '../constants/constants.dart';
import '../constants/theme.dart';

class SmallButton extends StatelessWidget {
  final label, onTap;
  const SmallButton({
    Key? key,
    this.label,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.all(5),
        padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 6),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(5),
          color: primaryColor,
        ),
        child: Text(
          label,
          style: smallWhiteTextStyle,
        ),
      ),
    );
  }
}
