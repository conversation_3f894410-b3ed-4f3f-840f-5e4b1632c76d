// ignore_for_file: prefer_typing_uninitialized_variables

import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:get_clean/global/constants/theme.dart';

import '../constants/constants.dart';

class CustomDropDownButton extends StatelessWidget {
  final value, hint, onChanged, label, items, buttonHeight, buttonWidth;

  const CustomDropDownButton({
    super.key,
    required this.value,
    this.hint,
    required this.onChanged,
    required this.items,
    required this.buttonHeight,
    required this.buttonWidth,
    this.label,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(10.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (label != null)
            Text(
              label,
              style: const TextStyle(
                color: primaryColor,
                fontSize: 18,
              ),
            ),
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(15),
              boxShadow: [BoxShadow(color: Colors.grey[500]!, blurRadius: 2)],
              color: Colors.white,
            ),
            child: DropdownButtonHideUnderline(
              child: Theme(
                data: Theme.of(context).copyWith(
                  canvasColor: Colors.white,
                ),
                child: DropdownButton2(
                  items: items,
                  value: value,
                  hint: Text(
                    hint,
                    style: regularTextStyle,
                  ),
                  style: regularTextStyle,
                  dropdownStyleData: DropdownStyleData(
                    elevation: 8,
                    padding: const EdgeInsets.all(10),
                    width: buttonWidth,
                    scrollbarTheme: ScrollbarThemeData(
                      radius: const Radius.circular(40),
                      thumbColor: MaterialStateProperty.all(Colors.white),
                    ),
                  ),
                  buttonStyleData: ButtonStyleData(
                    height: buttonHeight,
                    width: buttonWidth,
                    padding: const EdgeInsets.all(10),
                  ),
                  onChanged: onChanged,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
