// ignore_for_file: prefer_typing_uninitialized_variables

import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/favorites/controllers/favorites_controller.dart';
import 'package:get_clean/global/controllers/global_values_controller.dart';
import 'package:get_clean/global/models/provider.dart';
import 'package:get_clean/global/widget/text_with_background.dart';

import '../constants/constants.dart';
import '../constants/theme.dart';
import '../controllers/language_controller.dart';
import 'main_cached_image.dart';

class ProviderWidget extends StatelessWidget {
  final showBanner, showBooking, onTap;
  final Provider provider;
  final bool isMeter;
  final bool isHour;
  final bool isFavorite;
  final int? serviceId;

  const ProviderWidget({
    super.key,
    this.showBanner,
    this.showBooking,
    required this.onTap,
    required this.provider,
    this.isMeter = false,
    this.isHour = false,
    this.isFavorite = false,
    this.serviceId,
  });

  @override
  Widget build(BuildContext context) {
    final perMeter = Get.find<LanguageController>().keys.value.meterPrice;

    final perHour = Get.find<LanguageController>().keys.value.hourPrice;

    final price = provider.services
            ?.firstWhereOrNull((element) => element.service?.id == serviceId)
            ?.pricingList
            ?.firstOrNull
            ?.price ??
        0;

    // final isClothService = isClothesService(provider.services
    //     ?.firstWhereOrNull((element) => element.id == serviceId)
    //     ?.pricingOption
    //     ?.id);

    final commission = (provider.commission! / 100);

    final finalPrice = price * commission;

    final allPrice = price + finalPrice;

    log('asfsaf $serviceId');
    return InkWell(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.all(5),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(15),
          boxShadow: [
            BoxShadow(
              color: Colors.grey[400]!,
              blurRadius: 4,
            ),
          ],
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(15),
              child: MainCachedImage(
                provider.image!,
                fit: BoxFit.fill,
                height: 105.h,
                width: 100.w,
              ),
            ),
            SizedBox(width: 10.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  Text(
                    provider.name!,
                    style:
                        regularTextStyle.copyWith(fontWeight: FontWeight.bold),
                    overflow: TextOverflow.ellipsis,
                  ),

                  if (isHour == true || isMeter == true)
                    // if (!isClothService)
                    Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Text(
                        '${allPrice.toStringAsFixed(2)} ${isMeter ? perMeter : perHour}',
                        style: regularTextStyle,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),

                  // Text(
                  //   provider..
                  //   style: middleTextStyle,
                  //   overflow: TextOverflow.ellipsis,
                  // ),
                  // Text(
                  //   provider.skills?[0].name ?? "",
                  //   style: middleTextStyle,
                  //   overflow: TextOverflow.ellipsis,
                  // ),
                  // Text(
                  //   provider.workingTime[0].,
                  //   style: middleTextStyle,
                  //   overflow: TextOverflow.ellipsis,
                  // ),
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Row(
                  children: [
                    // Share, Favorite Icons
                    _ShareAndFavoriteIcons(
                      provider: provider,
                    ).paddingOnly(top: 5),

                    if (showBanner == true) ...[
                      SizedBox(width: 10.w),
                      SvgPicture.asset(
                        'assets/images/tag_special.svg',
                      ),
                    ]
                  ],
                ),
                SizedBox(height: 5.h),
                Row(
                  children: [
                    const Icon(
                      FontAwesomeIcons.solidStar,
                      color: Colors.yellow,
                      size: 14,
                    ),
                    SizedBox(width: 5.w),
                    Text(
                      provider.rating.toString(),
                      style: smallBlackTextStyle,
                    ),
                  ],
                ),
                if (showBooking == true)
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: TextWithBackground(
                      color: primaryColor,
                      text: 'Booking',
                      fontSize: 13.sp,
                    ),
                  ),
              ],
            ),
            const SizedBox(width: 15),
          ],
        ),
      ),
    );
  }
}

class _ShareAndFavoriteIcons extends GetView<FavoriteController> {
  final Provider provider;

  const _ShareAndFavoriteIcons({required this.provider});

  @override
  Widget build(BuildContext context) {
    Get.put(FavoriteController());

    return Row(
      children: [
        InkWell(
          onTap: () => controller.shareProvider(provider: provider),
          child: const Icon(
            Icons.share,
            color: Colors.grey,
          ),
        ),
        SizedBox(width: 10.w),
        if (Get.find<GlobalValuesController>().isLoggedIn.value)
          Obx(() {
            final isFavorite = controller.isFavorite(provider.id);

            return InkWell(
              onTap: () =>
                  controller.addToFavorite(providerId: provider.userId),
              child: Icon(
                isFavorite ? Icons.favorite : Icons.favorite_border,
                color: isFavorite ? Colors.red : Colors.grey,
              ),
            );
          }),
      ],
    );
  }
}
