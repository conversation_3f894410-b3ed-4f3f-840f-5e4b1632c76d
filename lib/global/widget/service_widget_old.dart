// // ignore_for_file: prefer_typing_uninitialized_variables
//
// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:get/get.dart';
// import 'package:get_clean/app/routes/app_pages.dart';
// import 'package:get_clean/global/models/home_model.dart';
// import 'package:get_clean/global/widget/main_cached_image.dart';
//
// import '../constants/theme.dart';
//
// class ServiceWidget extends StatelessWidget {
//   final Service service;
//   final onTap;
//   final bool isMeter;
//   final bool isHour;
//
//   const ServiceWidget(
//       {Key? key,
//       required this.service,
//       this.onTap,
//       this.isMeter = false,
//       this.isHour = false})
//       : super(key: key);
//
//   @override
//   Widget build(BuildContext context) {
//     return InkWell(
//       onTap: onTap ??
//           () => Get.toNamed(Routes.SERVICE_PROVIDERS, arguments: {
//                 'service': service,
//                 'isMeter': isMeter,
//                 'isHour': isHour
//               }),
//       child: Column(
//         children: [
//           Container(
//             width: context.width * 0.29,
//             height: 105.h,
//             // padding: const EdgeInsets.all(8),
//             margin: const EdgeInsets.only(left: 5, right: 5, top: 5, bottom: 5),
//             // decoration: BoxDecoration(
//             //   borderRadius: BorderRadius.circular(10),
//             //   border: Border.all(
//             //     color: primaryColor,
//             //   ),
//             // ),
//             child: ClipRRect(
//               borderRadius: BorderRadius.circular(12),
//               child: MainCachedImage(
//                 service.image ?? '',
//                 height: 105.h,
//               ),
//             ),
//           ),
//           Container(
//             alignment: Alignment.center,
//             width: Get.width * 0.23,
//             child: Text(
//               service.name ?? '',
//               textAlign: TextAlign.center,
//               style: regularTextStyle.copyWith(fontSize: 18),
//               overflow: TextOverflow.ellipsis,
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }
