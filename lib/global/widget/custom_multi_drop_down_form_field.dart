// ignore_for_file: prefer_typing_uninitialized_variables

import 'package:dropdown_textfield/dropdown_textfield.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../constants/constants.dart';
import '../constants/theme.dart';
import '../controllers/language_controller.dart';

const _radius = Radius.circular(12);

// class BaseSearchDropDown extends StatelessWidget {
//   final dynamic selectedValue;
//   final String? label;
//   final List<dynamic> data;
//   final void Function(dynamic)? onChanged;
//   final Widget? icon;
//   final Widget Function(BuildContext, dynamic, bool)? itemBuilder;
//
//   const BaseSearchDropDown(
//       {Key? key,
//       required this.onChanged,
//       this.itemBuilder,
//       required this.data,
//       required this.label,
//       required this.selectedValue,
//       this.icon})
//       : super(key: key);
//
//   @override
//   Widget build(BuildContext context) {
//     ValueNotifier<bool> isDropdownOpen = ValueNotifier(false);
//
//     return Wrap(
//       children: [
//         Padding(
//           padding: const EdgeInsets.only(
//             left: 4,
//             bottom: 6,
//           ),
//           child: FittedBox(
//             fit: BoxFit.scaleDown,
//             child: Row(
//               children: [
//                 Text(label ?? '',
//                     style: const TextStyle(
//                       fontSize: 16,
//                       fontWeight: FontWeight.w600,
//                     )),
//               ],
//             ),
//           ),
//         ),
//         Material(
//             elevation: 2,
//             borderRadius: isDropdownOpen.value
//                 ? const BorderRadius.only(topLeft: _radius, topRight: _radius)
//                 : BorderRadius.circular(12),
//             color: Colors.white,
//             child: _multiSelect(context, isDropdownOpen: isDropdownOpen)),
//       ],
//     );
//   }
//
//   Widget _multiSelect(BuildContext context,
//       {required ValueNotifier<bool> isDropdownOpen}) {
//     return DropdownSearch.multiSelection(
//       onBeforePopupOpening: (controller) {
//         isDropdownOpen.value = true;
//         return Future.value(true);
//       },
//       onBeforeChange: (val, value) {
//         isDropdownOpen.value = false;
//         return Future.value(true);
//       },
//       autoValidateMode: AutovalidateMode.onUserInteraction,
//       validator: (value) {
//         if (value == null && selectedValue == null) {
//           return 'Please select $label';
//         }
//         return null;
//       },
//       popupProps: PopupPropsMultiSelection.menu(
//         onDismissed: () {
//           isDropdownOpen.value = false;
//         },
//         menuProps: MenuProps(
//           elevation: 2,
//           backgroundColor: Colors.white,
//           borderRadius: !isDropdownOpen.value
//               ? const BorderRadius.only(
//                   bottomLeft: _radius,
//                   bottomRight: _radius,
//                 )
//               : BorderRadius.circular(12),
//         ),
//         itemBuilder: (context, status, isSelected) {
//           return Column(
//             children: [
//               ListTile(
//                 selected: isSelected,
//                 title: Text(status.toString()),
//               ),
//               const Divider(
//                 thickness: .4,
//               ),
//             ],
//           );
//         },
//         isFilterOnline: false,
//         showSelectedItems: false,
//         searchFieldProps: const TextFieldProps(
//           decoration: InputDecoration(
//             border: InputBorder.none,
//             hintText: 'Search',
//           ),
//         ),
//         showSearchBox: true,
//       ),
//       dropdownBuilder: (context, value) {
//         if (selectedValue == null) {
//           return Padding(
//             padding: const EdgeInsets.only(top: 4),
//             child: Text(
//               'Select $label',
//             ),
//           );
//         }
//         return Padding(
//           padding: const EdgeInsets.only(top: 4),
//           child: Text(
//             value.isEmpty ? selectedValue ?? 'Select $label' : value.join(', '),
//           ),
//         );
//       },
//       dropdownDecoratorProps: DropDownDecoratorProps(
//         dropdownSearchDecoration: InputDecoration(
//           contentPadding: EdgeInsets.symmetric(
//             vertical: 12.h,
//             horizontal: icon == null ? 16.w : 0,
//           ),
//           icon: Padding(
//             padding: const EdgeInsets.only(left: 8.0),
//             child: icon,
//           ),
//           border: InputBorder.none,
//           labelText: label,
//           labelStyle: TextStyle(
//             fontSize: 16.sp,
//             fontWeight: FontWeight.w500,
//           ),
//           filled: true,
//           fillColor: Colors.transparent,
//         ),
//       ),
//       items: data,
//       onChanged: onChanged,
//     );
//   }
// }

class CustomMultiDropDownFormField extends StatelessWidget {
  final controller,
      isSecure,
      hint,
      label,
      validator,
      onChanged,
      initialValue,
      maxLines,
      icon,
      suffixIcon,
      onSuffixIconTap,
      minLines,
      onIconTap,
      color,
      enabled,
      customButton,
      dataList;
  const CustomMultiDropDownFormField({
    Key? key,
    required this.dataList,
    this.controller,
    this.icon,
    this.hint,
    this.isSecure = false,
    this.validator,
    this.onChanged,
    this.initialValue,
    this.maxLines = 1,
    this.minLines = 1,
    this.onIconTap,
    this.label,
    this.suffixIcon,
    this.onSuffixIconTap,
    this.color = Colors.white,
    this.enabled = true,
    this.customButton,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(10.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              if (label != null)
                Text(
                  label,
                  style: regularTextStyle,
                ),
              if (customButton != null) customButton
            ],
          ),
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(15),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey[500]!,
                  blurRadius: 2,
                ),
              ],
              color: Colors.white,
            ),
            child: DropDownTextField.multiSelection(
              listSpace: 20,
              listPadding: ListPadding(top: 20),
              dropDownList: dataList,
              submitButtonColor: Colors.white,
              submitButtonTextStyle: const TextStyle(color: primaryColor),
              clearOption: false,
              displayCompleteItem: true,
              textStyle: const TextStyle(color: primaryColor),
              submitButtonText: Get.find<LanguageController>().keys.value.ok!,
              listTextStyle: const TextStyle(color: primaryColor, height: 1),
              dropDownItemCount: dataList.length,
              initialValue: initialValue,
              onChanged: onChanged,
              controller: controller,
              validator: validator,
              textFieldDecoration: InputDecoration(
                hintText: hint,
                filled: true,
                fillColor: color,
                suffixIcon: InkWell(
                  onTap: onSuffixIconTap,
                  child: Padding(
                    padding: const EdgeInsets.only(right: 10.0, left: 10),
                    child: suffixIcon,
                  ),
                ),
                icon: InkWell(
                  onTap: onIconTap,
                  child: Padding(
                    padding: const EdgeInsets.only(left: 10.0, right: 10.0),
                    child: icon,
                  ),
                ),
                iconColor: primaryColor,
                contentPadding: const EdgeInsets.all(5),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide: const BorderSide(
                    style: BorderStyle.none,
                    color: Colors.transparent,
                  ),
                  gapPadding: 5,
                ),
                errorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide: const BorderSide(
                    style: BorderStyle.none,
                    color: Colors.transparent,
                  ),
                  gapPadding: 5,
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide: const BorderSide(
                    style: BorderStyle.none,
                    color: Colors.transparent,
                  ),
                  gapPadding: 5,
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide: const BorderSide(
                    style: BorderStyle.none,
                    color: Colors.transparent,
                  ),
                  gapPadding: 5,
                ),
                disabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide: const BorderSide(
                    style: BorderStyle.none,
                    color: Colors.transparent,
                  ),
                  gapPadding: 5,
                ),
                focusedErrorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide: const BorderSide(
                    style: BorderStyle.none,
                    color: Colors.transparent,
                  ),
                  gapPadding: 5,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
