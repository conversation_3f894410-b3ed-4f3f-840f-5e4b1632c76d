import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/global/widget/area_and_city_for_bottom_sheet.dart';

import '../../app/modules/my_profile/controllers/my_profile_controller.dart';
import '../constants/constants.dart';
import '../controllers/language_controller.dart';
import 'custom_button.dart';

class AddAreaAndCityBottomSheet extends StatelessWidget {
  const AddAreaAndCityBottomSheet({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Get.put(MyProfileController());
    return GetBuilder<MyProfileController>(
      builder: (controller) {
        return StatefulBuilder(builder: (context, setState) {
          return Container(
            height: 322.h,
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(60),
                topRight: Radius.circular(60),
              ),
            ),
            padding: const EdgeInsets.all(10),
            child: Column(
              children: [
                Container(
                  height: 2.h,
                  width: 111.w,
                  decoration: const BoxDecoration(
                    color: primaryColor,
                  ),
                ),
                const AreaAndCityBottomSheet(),
                CustomButton(
                  label: Get.find<LanguageController>().keys.value.add!,
                  onTap: () {
                    controller.onAddNewWorkZone();
                    setState(() {});
                  },
                  height: 43.h,
                  width: 129.w,
                ),
              ],
            ),
          );
        });
      },
    );
  }
}
