import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:get_clean/global/constants/constants.dart';

class CustomImageSlider extends StatefulWidget {
  final List<Widget> images;
  final bool autoPlay;
  final double aspectRatio;
  const CustomImageSlider({
    Key? key,
    required this.images,
    this.autoPlay = true,
    this.aspectRatio = 2.0,
  }) : super(key: key);

  @override
  State<CustomImageSlider> createState() => _CustomImageSliderState();
}

class _CustomImageSliderState extends State<CustomImageSlider> {
  int index = 0;
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        CarouselSlider(
          options: CarouselOptions(
              autoPlay: widget.images.length == 1 ? false : widget.autoPlay,
              aspectRatio: widget.aspectRatio,
              enlargeCenterPage: true,
              onPageChanged: (i, _) {
                setState(() {
                  index = i;
                });
              }),
          items: widget.images,
        ),
        const SizedBox(height: 10),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            for (int i = 0; i < widget.images.length; i++)
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 2),
                width: 7,
                height: 7,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: i == index ? primaryColor : Colors.grey[300],
                ),
              )
          ],
        )
      ],
    );
  }
}
