// ignore_for_file: prefer_typing_uninitialized_variables

import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../constants/theme.dart';
import '../controllers/language_controller.dart';
import 'custom_button.dart';
import 'package:get/get.dart';

class MyOfferWidget extends StatelessWidget {
  final onEditPressed,
      onDeletePressed,
      isActive,
      onActiveChanged,
      imageURL,
      name,
      address;
  const MyOfferWidget({
    Key? key,
    this.onDeletePressed,
    this.onEditPressed,
    this.isActive,
    this.onActiveChanged,
    this.imageURL,
    this.name,
    this.address,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(5),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Colors.grey[400]!,
            blurRadius: 4,
          ),
        ],
      ),
      child: Row(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(15),
            child: Image.network(
              imageURL,
              fit: BoxFit.fill,
              height: 90.h,
              width: 90.w,
              errorBuilder: (context, error, stackTrace) => SizedBox(
                height: 90.h,
                width: 90.w,
              ),
            ),
          ),
          SizedBox(width: 10.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Text(
                  name,
                  style: middleTextStyle,
                  overflow: TextOverflow.ellipsis,
                ),
                Row(
                  children: [
                    const Icon(
                      FontAwesomeIcons.locationPin,
                      size: 17,
                    ),
                    SizedBox(width: 4.w),
                    Text(
                      address,
                      style: middleTextStyle,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
                Row(
                  children: [
                    CustomButton(
                      label: Get.find<LanguageController>().keys.value.edit!,
                      onTap: onEditPressed,
                      height: 25.h,
                      width: 50.w,
                      borderRadius: 5.0,
                      fontSize: 12,
                    ),
                    CustomButton(
                      label: Get.find<LanguageController>().keys.value.delete!,
                      onTap: onDeletePressed,
                      height: 25.h,
                      width: 50.w,
                      borderRadius: 5.0,
                      fontSize: 12,
                    ),
                    Switch(
                      activeTrackColor: Colors.green,
                      thumbColor: MaterialStateProperty.resolveWith(
                          (states) => Colors.white),
                      value: isActive,
                      onChanged: onActiveChanged,
                    ),
                  ],
                )
              ],
            ),
          ),
        ],
      ),
    );
  }
}
