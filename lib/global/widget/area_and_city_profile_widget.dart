import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/my_profile/controllers/my_profile_controller.dart';

import '../controllers/language_controller.dart';
import 'custom_drop_down_button.dart';

class AreaAndCityProfileWidget extends StatelessWidget {
  const AreaAndCityProfileWidget({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<MyProfileController>(
      builder: (profileController) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            CustomDropDownButton(
              buttonHeight: 50.0.h,
              buttonWidth: Get.width * 0.4,
              hint: Get.find<LanguageController>().keys.value.chooseArea!,
              value: profileController.choosedArea.value.name == null
                  ? null
                  : profileController.choosedArea.value,
              label: Get.find<LanguageController>().keys.value.area!,
              onChanged: profileController.onChangeAreaProfile,
              items: profileController.areas.value.data
                  ?.map(
                    (data) => DropdownMenuItem(
                      value: data,
                      child: Text(
                        data.name!,
                      ),
                    ),
                  )
                  .toList(),
            ),
            CustomDropDownButton(
              buttonHeight: 50.0.h,
              buttonWidth: Get.width * 0.4,
              hint: Get.find<LanguageController>().keys.value.chooseCity!,
              value: profileController.choosedCity.value.name == null
                  ? null
                  : profileController.choosedCity.value,
              label: Get.find<LanguageController>().keys.value.city!,
              onChanged: profileController.onChangeCityProfile,
              items: profileController.cities.value.data
                  ?.map(
                    (data) => DropdownMenuItem(
                      value: data,
                      child: Text(
                        data.name!,
                      ),
                    ),
                  )
                  .toList(),
            ),
          ],
        );
      },
    );
  }
}
