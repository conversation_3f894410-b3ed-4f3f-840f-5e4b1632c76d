// ignore_for_file: prefer_typing_uninitialized_variables

import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../constants/constants.dart';
import '../constants/theme.dart';

class LanguageWidget extends StatelessWidget {
  final languageName, onPressed;
  const LanguageWidget({
    Key? key,
    this.languageName,
    this.onPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPressed,
      child: Container(
        width: Get.width,
        decoration: BoxDecoration(
          border: Border.all(
            color: primaryColor,
            width: 0.5,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.grey[500]!,
              blurRadius: 2,
            ),
          ],
          color: Colors.white,
          borderRadius: BorderRadius.circular(15),
        ),
        padding: const EdgeInsets.all(5),
        margin: const EdgeInsets.all(10),
        child: Text(
          languageName,
          style: bigTextStyle,
        ),
      ),
    );
  }
}
