// // ignore_for_file: prefer_typing_uninitialized_variables
//
// import 'package:flutter/material.dart';
// import 'package:intl_phone_number_input/intl_phone_number_input.dart';
//
// import '../constants/constants.dart';
// import '../constants/theme.dart';
//
// class CustomIntlPhoneField extends StatelessWidget {
//   final controller,
//       isSecure,
//       hint,
//       label,
//       validator,
//       onChanged,
//       initialValue,
//       maxLines,
//       icon,
//       suffixIcon,
//       onSuffixIconTap,
//       minLines,
//       onIconTap,
//       color,
//       enabled;
//
//   const CustomIntlPhoneField({
//     Key? key,
//     this.controller,
//     this.icon,
//     this.hint,
//     this.isSecure = false,
//     this.validator,
//     this.onChanged,
//     this.initialValue,
//     this.maxLines = 1,
//     this.minLines = 1,
//     this.onIconTap,
//     this.label,
//     this.suffixIcon,
//     this.onSuffixIconTap,
//     this.color = Colors.white,
//     this.enabled = true,
//   }) : super(key: key);
//
//   @override
//   Widget build(BuildContext context) {
//     return Padding(
//       padding: const EdgeInsets.all(10.0),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           if (label != null)
//             Text(
//               label,
//               style: regularTextStyle,
//             ),
//           Container(
//             decoration: BoxDecoration(
//               borderRadius: BorderRadius.circular(15),
//               boxShadow: [
//                 BoxShadow(
//                   color: Colors.grey[500]!,
//                   blurRadius: 2,
//                 ),
//               ],
//               color: Colors.white,
//             ),
//             child: InternationalPhoneNumberInput(
//               spaceBetweenSelectorAndTextField: 0,
//               selectorConfig: const SelectorConfig(
//                 useEmoji: true,
//                 selectorType: PhoneInputSelectorType.DIALOG,
//               ),
//               isEnabled: enabled,
//               initialValue: initialValue,
//               onInputChanged: onChanged,
//               textFieldController: controller,
//               validator: validator,
//               inputDecoration: InputDecoration(
//                 hintText: hint,
//                 filled: true,
//                 fillColor: color,
//                 suffixIcon: InkWell(
//                   onTap: onSuffixIconTap,
//                   child: Padding(
//                     padding: const EdgeInsets.only(right: 10.0, left: 10),
//                     child: suffixIcon,
//                   ),
//                 ),
//                 icon: InkWell(
//                   onTap: onIconTap,
//                   child: Padding(
//                     padding: const EdgeInsets.only(left: 10.0, right: 10.0),
//                     child: icon,
//                   ),
//                 ),
//                 iconColor: primaryColor,
//                 contentPadding: const EdgeInsets.all(5),
//                 border: OutlineInputBorder(
//                   borderRadius: BorderRadius.circular(10),
//                   borderSide: const BorderSide(
//                     style: BorderStyle.none,
//                     color: Colors.transparent,
//                   ),
//                   gapPadding: 5,
//                 ),
//                 errorBorder: OutlineInputBorder(
//                   borderRadius: BorderRadius.circular(10),
//                   borderSide: const BorderSide(
//                     style: BorderStyle.none,
//                     color: Colors.transparent,
//                   ),
//                   gapPadding: 5,
//                 ),
//                 enabledBorder: OutlineInputBorder(
//                   borderRadius: BorderRadius.circular(10),
//                   borderSide: const BorderSide(
//                     style: BorderStyle.none,
//                     color: Colors.transparent,
//                   ),
//                   gapPadding: 5,
//                 ),
//                 focusedBorder: OutlineInputBorder(
//                   borderRadius: BorderRadius.circular(10),
//                   borderSide: const BorderSide(
//                     style: BorderStyle.none,
//                     color: Colors.transparent,
//                   ),
//                   gapPadding: 5,
//                 ),
//                 disabledBorder: OutlineInputBorder(
//                   borderRadius: BorderRadius.circular(10),
//                   borderSide: const BorderSide(
//                     style: BorderStyle.none,
//                     color: Colors.transparent,
//                   ),
//                   gapPadding: 5,
//                 ),
//                 focusedErrorBorder: OutlineInputBorder(
//                   borderRadius: BorderRadius.circular(10),
//                   borderSide: const BorderSide(
//                     style: BorderStyle.none,
//                     color: Colors.transparent,
//                   ),
//                   gapPadding: 5,
//                 ),
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }
