import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../constants/theme.dart';
import '../models/reviews.dart';
import 'custom_rating_bar.dart';

class ReviewWidget extends StatelessWidget {
  final Reviews review;
  const ReviewWidget({
    Key? key,
    required this.review,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(10),
      margin: const EdgeInsets.all(5),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey[400]!,
            blurRadius: 4,
          ),
        ],
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CircleAvatar(
            backgroundImage: NetworkImage(
              review.user?.image ?? '',
            ),
          ),
          SizedBox(width: 10.w),
          Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                review.user?.name ?? 'User',
                style: middleTextStyle,
              ),
              CustomRatingBar(
                iconSize: 10,
                starsColor: Colors.black,
                padding: 1.0,
                canChangeRate: false,
                initialRating: review.rating!.toDouble(),
              ),
            ],
          ),
          SizedBox(width: 10.w),
          Expanded(
            child: Text(
              review.comment!,
              style: middleTextStyle,
            ),
          ),
        ],
      ),
    );
  }
}
