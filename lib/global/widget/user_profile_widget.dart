import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/my_profile/controllers/my_profile_controller.dart';

import '../controllers/language_controller.dart';
import 'area_and_city_profile_widget.dart';
import 'custom_button.dart';

class UserProfileWidget extends StatelessWidget {
  const UserProfileWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<MyProfileController>(builder: (controller) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const AreaAndCityProfileWidget(),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              CustomButton(
                label: Get.find<LanguageController>().keys.value.save!,
                onTap: () => controller.editData(context),
                height: 42.h,
                width: 135.w,
              ),
            ],
          ),
        ],
      );
    });
  }
}
