// ignore_for_file: prefer_typing_uninitialized_variables

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/signup/controllers/signup_controller.dart';

import '../constants/constants.dart';

class SignupPicture extends StatelessWidget {
  final label;
  const SignupPicture({Key? key, this.label}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<SignupController>(builder: (controller) {
      return Padding(
        padding: const EdgeInsets.all(10.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: const TextStyle(
                color: primaryColor,
                fontSize: 16,
              ),
            ),
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(15),
                border: Border.all(
                  color: primaryColor.withOpacity(0.6),
                ),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.add,
                    size: 40,
                  ),
                  Expanded(
                    child: Icon(
                      Icons.image,
                      color: primaryColor.withOpacity(0.6),
                      size: Get.width * 0.4,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    });
  }
}
