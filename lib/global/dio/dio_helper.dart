import 'dart:convert';
import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:get_clean/global/extensions/string_extensions.dart';
import 'package:get_clean/global/help_functions/help_functions.dart';
import 'package:get_storage/get_storage.dart';

import '../constants/constants.dart';

class DioHelper {
  Dio dio = Dio(
    BaseOptions(
      baseUrl: baseURL,
      headers: {
        'lang': GetStorage().read(slugKey) ?? 'en',
        // 'Accept-Language': GetStorage().read(slugKey) ?? 'en',
        'x-api-key':
            'jsOQy0433SA56XNRNcajFghTO380iDKEZN3U7iMX0WTp4V8dH6fFLaaMDIUOVAPGo+4prLeAwMJ+DQXePY09LA==',
        "Content-Type": "application/json",
        "Accept": "application/json",
        if (GetStorage().hasData(tokenKey))
          'Authorization': 'Bearer ${GetStorage().read(tokenKey)}',

        // 'Bearer *********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
      },
    ),
  );

  Future<dynamic> getData(url, {params = const <String, dynamic>{}}) async {
    late Response response;
    // (dio.httpClientAdapter as DefaultHttpClientAdapter).onHttpClientCreate =
    //     (HttpClient client) {
    //   client.badCertificateCallback =
    //       (X509Certificate cert, String host, int port) => true;
    //   return client;
    // };

    dio.options.headers['Accept-Language'] = GetStorage().read(slugKey) ?? 'en';

    try {
      response = await dio.get(url, queryParameters: params);

      log('ResponseFromDio $url${params != null ? ' - URL_Params:$params' : ''}\n$url Response:${response.data}');
    } on DioError catch (e) {
      log("Error In Dio With URL $url ${e.message} \n ${e.response?.statusCode}");
      log("The Body Is ${e.response}");
      // if (e.response != null) {
      //   showErrorToast(e.response!.data['message'].toString());
      // }
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
    }

    return Future.value(jsonDecode(jsonEncode(response.data)));
  }

  Future<dynamic> postData(
    url,
    data, {
    String? image,
    String? fileName,
  }) async {
    final uploadedFileName = fileName ?? 'image';

    late Response response;
    dio.options.headers['Accept-Language'] = GetStorage().read(slugKey) ?? 'en';

    try {
      log(" url $url\n postData $data \nToken ${GetStorage().read(tokenKey)}");
      FormData formData = FormData.fromMap(data);

      if (image != null && image.isNotEmpty) {
        formData.files.add(MapEntry(
          uploadedFileName,
          await MultipartFile.fromFile(image, filename: 'image.jpg'),
        ));
      }

      response = await dio.post(url, data: formData);

      log('ResponsePostFromDio $url${data != null ? ' - DATA_Params:$data' : ''}\n-> $url --> Response:${response.data}');

      // response = await dio.post(url, data: FormData.fromMap(data));
    } on DioError catch (e) {
      log("Error In Dio With URL $url ${e.message}");
      log("The Body Is ${e.response}");
      if (e.response != null) {
        showErrorToast(e.response!.data.toString().translateErrorMessage);
      }
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }

      rethrow;
    }
    return Future.value(jsonDecode(jsonEncode(response.data)));
  }

  Future<dynamic> patchData(url, data) async {
    late Response response;
    dio.options.headers['Accept-Language'] = GetStorage().read(slugKey) ?? 'en';

    try {
      response = await dio.patch(url, data: FormData.fromMap(data));
    } on DioError catch (e) {
      log("Error In Dio With URL $url ${e.message}");
      log("The Body Is ${e.response}");
      // if (e.response != null) {
      //   showErrorToast(e.response!.data['message'].toString());
      // }
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
    }

    return Future.value(jsonDecode(jsonEncode(response.data)));
  }

  Future<dynamic> deleteData(url, data) async {
    late Response response;
    dio.options.headers['Accept-Language'] = GetStorage().read(slugKey) ?? 'en';

    try {
      response = await dio.delete(url, data: FormData.fromMap(data));
    } on DioError catch (e) {
      log("Error In Dio With URL $url ${e.message}");
      log("The Body Is ${e.response}");
      // if (e.response != null) {
      //   showErrorToast(e.response!.data['message'].toString());
      // }
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
    }

    return Future.value(jsonDecode(jsonEncode(response.data)));
  }
}
