// ignore_for_file: use_build_context_synchronously

import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/routes/app_pages.dart';
import 'package:get_clean/global/models/keys_model.dart';
import 'package:get_clean/global/remote_data_source/language_remote_provider.dart';
import 'package:get_storage/get_storage.dart';

import '../constants/constants.dart';
import '../local_data_sources/language_local_data_source.dart';
import '../models/language_model.dart';

class LanguageController extends GetxController {
  final selectedLanguage =
      LanguageData(slug: GetStorage().read(slugKey) ?? 'en').obs;

  final languagesModel = LanguagesModel().obs;
  final keys = KeysModel().obs;

  LanguageLocalDataSource localDataSource = LanguageLocalDataSource();
  LanguageRemoteProvider languageRemoteProvider = LanguageRemoteProvider();

  // get the saved language model
  Future<LanguageData> getLanguage(LanguagesModel langModel) async {
    return await localDataSource.getSavedLanguage(langModel);
  }

  Future<bool> getData() async {
    final response = await languageRemoteProvider.getLanguages();

    log('LANG_RESPONSE $response');

    if (response != null) {
      languagesModel.value = response;

      selectedLanguage.value = await getLanguage(languagesModel.value);

      log('SelectedLangKeys ${selectedLanguage.value.keys!} SelectedLangValue ${selectedLanguage.value.toJson()} ');

      keys.value =
          (await languageRemoteProvider.getKeys(selectedLanguage.value.keys!))!;

      update();
      return true;
    } else {
      return false;
    }
  }

  Future<void> changeLanguageAsWeNeed(
    LanguageData languageModel,
    BuildContext context,
  ) async {
    // save language as we choosed
    await localDataSource.saveLanguage(languageModel);

    // set the current selected language as language we choosed
    selectedLanguage.value = languageModel;

    // keys.value =
    //     (await languageRemoteProvider.getKeys(selectedLanguage.value.keys!))!;

    // update locale with the new language code
    // Get.updateLocale(Locale(languageModel.slug!));

    // if (GetPlatform.isIOS) {
    //   Get.defaultDialog(
    //     barrierDismissible: false,
    //     textConfirm: keys.value.ok,
    //     title: keys.value.restartApp!,
    //     middleText: keys.value.restartAppContent!,
    //     onConfirm: () {
    //       SystemNavigator.pop();
    //       exit(0);
    //     },
    //   );
    // } else {
    //   Restart.restartApp();
    // }

    // Phoenix.rebirth(context);

    // Restart.restartApp();

    Get.offAllNamed(Routes.SPLASH_SCREEN, arguments: true);

    // update the ui
    update();
  }

  bool get isArabic => selectedLanguage.value.slug == 'ar';
}
