import 'dart:developer';

import 'package:get/get.dart';
import 'package:get_clean/global/global_states/all_offers_state.dart';
import 'package:get_clean/global/global_states/faq_states.dart';
import 'package:get_clean/global/global_states/home_states.dart';
import 'package:get_clean/global/global_states/services_state.dart';
import 'package:get_clean/global/global_states/skills_state.dart';
import 'package:get_clean/global/models/all_offers_model.dart';
import 'package:get_clean/global/models/faq_model.dart';
import 'package:get_clean/global/models/home_model.dart';
import 'package:get_clean/global/models/provider_services.dart';
import 'package:get_clean/global/models/skills_model.dart';
import 'package:get_clean/global/utils/dynamic_links.dart';

import '../global_states/about_us_state.dart';
import '../global_states/contact_info_state.dart';
import '../global_states/privacy_policy_state.dart';
import '../global_states/terms_of_payment_state.dart';
import '../global_states/terms_of_service_state.dart';
import '../models/about_us_model.dart';
import '../models/all_services_model.dart';
import '../models/contact_info_model.dart';
import '../models/privacy_policy_model.dart';
import '../models/terms_of_payment.dart';
import '../models/terms_of_service.dart';
import '../remote_data_source/global_values_remote_data_source.dart';

class GlobalValuesController extends GetxController {
  final aboutUsState = AboutUsState().obs;
  final contactInfoState = ContactInfoState().obs;
  final privacyPolicyState = PrivacyPolicyState().obs;
  final termsOfServiceState = TermsOfServiceState().obs;
  final termsOfPaymentState = TermsOfPaymentState().obs;
  final allServicesState = ServicesState().obs;
  final skillsState = SkillsState().obs;
  final faqState = FAQState().obs;
  final homeState = HomeState().obs;
  final allOffersState = AllOffersState().obs;

  final aboutUsModel = AboutUsModel().obs;
  final contactInfoModel = ContactInfoModel().obs;
  final privacyPolicyModel = PrivacyPolicyModel().obs;
  final termsOfServiceModel = TermsOfServiceModel().obs;
  final termsOfPaymentModel = TermsOfPaymentModel().obs;
  final allServices = AllServicesModel().obs;
  final skills = SkillsModel().obs;
  final faq = FAQModel().obs;
  final home = HomeModel().obs;
  final allOffers = AllOffersModel().obs;

  final provider = GlobalValuesRemoteDataSource();

  // check if user is logged in
  final isLoggedIn = false.obs;

  @override
  void onInit() async {
    super.onInit();

    DynamicLinkHandler.initDynamicLink();

    await getAllData();
  }

  Future<void> getAllData() async {
    log('From Global Values Controller');
    await getHome();
    await getAllServices();
    await getAllSkills();
    // TODO-Api
    getAboutUsData();
    getContactInfoData();
    getPrivacyPolicyData();
    getTermsOfServiceData();
    getTermsOfPayment();
    getFAQ();
    getAllOffers();
  }

  Future<void> getHome() async {
    home.value = HomeModel();
    homeState.value = await provider.getHome();

    if (homeState.value is HomeSuccessState) {
      home.value = homeState.value.homeModel!;
    } else {
      log('Not OK');
    }
    update();
  }

  Future<void> getAllOffers() async {
    allOffersState.value = await provider.getAllOffers();

    if (allOffersState.value is AllOffersSuccessState) {
      allOffers.value = allOffersState.value.allOffers!;
    } else {}
    update();
  }

  // void getAllProviders() async {
  //   providersState.value = await provider.getAllProviders();

  //   if (providersState.value is ProvidersSuccessState) {
  //     providers.value = providersState.value.providers!;
  //   } else {}
  //   update();
  // }

  Future<void> getFAQ() async {
    faqState.value = await provider.getFAQ();

    if (faqState.value is FAQSuccessState) {
      faq.value = faqState.value.faqModel!;
    } else {}
    update();
  }

  Future<void> getAllSkills() async {
    skillsState.value = await provider.getAllSkills();

    if (skillsState.value is SkillsSuccessState) {
      skills.value = skillsState.value.model!;
    } else {}
    update();
  }

  Future<List<ProviderServices>> getAllServices({int? providerId}) async {
    allServicesState.value = await provider.getAllServices(
      providerId: providerId,
    );

    if (allServicesState.value is ServicesSuccessState) {
      allServices.value = allServicesState.value.allServicesModel!;

      update();

      return allServices.value.data ?? [];
    } else {}

    return [];
  }

  Future<void> getAboutUsData() async {
    aboutUsState.value = await provider.getAboutUsData();

    if (aboutUsState.value is AboutUsSuccessState) {
      aboutUsModel.value = aboutUsState.value.aboutUs!;
    } else {
      aboutUsModel.value.data =
          AboutUsData(id: 2, title: 'About Us', text: 'No Data');
    }
    update();
  }

  Future<void> getContactInfoData() async {
    contactInfoState.value = await provider.getContactInfoData();

    if (contactInfoState.value is ContactInfoSuccessState) {
      contactInfoModel.value = contactInfoState.value.contactInfo!;
    } else {
      contactInfoModel.value.data = ContactInfoData(email: '', phone: '');
    }
    update();
  }

  Future<void> getPrivacyPolicyData() async {
    privacyPolicyState.value = await provider.getPrivacyPolicyData();

    if (privacyPolicyState.value is PrivacyPolicySuccessState) {
      privacyPolicyModel.value = privacyPolicyState.value.privacyPolicy!;
    } else {
      privacyPolicyModel.value.data =
          PrivacyPolicyData(id: 3, title: 'Privacy Policy', text: 'No Data');
    }
    update();
  }

  Future<void> getTermsOfServiceData() async {
    termsOfServiceState.value = await provider.getTermsOfServiceData();

    if (termsOfServiceState.value is TermsOfServiceSuccessState) {
      termsOfServiceModel.value = termsOfServiceState.value.termsOfService!;
    } else {
      termsOfServiceModel.value.data =
          TermsOfServiceData(id: 1, title: 'Terms Of Service', text: 'No Data');
    }
    update();
  }

  Future<void> getTermsOfPayment() async {
    termsOfPaymentState.value = await provider.getTermsOfPayment();

    if (termsOfPaymentState.value is TermsOfPaymentSuccessState) {
      termsOfPaymentModel.value = termsOfPaymentState.value.termsOfPayment!;
    } else {
      termsOfPaymentModel.value.data =
          TermsOfPaymentData(id: 1, title: 'Terms Of Payment', text: 'No Data');
    }
    update();
  }

  void setUserLoggedIn() {
    isLoggedIn.value = true;
    update();
  }

  void setUserLoggedOut() {
    isLoggedIn.value = false;
    update();
  }
}
