import 'dart:developer';

import 'package:get/get.dart';
import 'package:get_clean/app/modules/signup/controllers/signup_controller.dart';
import 'package:get_clean/global/models/areas.dart';
import 'package:get_clean/global/models/cities.dart';
import 'package:get_clean/global/models/city.dart';
import 'package:get_clean/global/remote_data_source/remote_data_source.dart';

class CityAndAreaController extends GetxController {
  CitiesAndAreasRemoteDataSource remoteDataSource =
      CitiesAndAreasRemoteDataSource();

  final cities = Cities().obs;
  final areas = Areas().obs;

  @override
  void onInit() async {
    super.onInit();
    getAreas();
  }

  void getAreas() async {
    final response = await remoteDataSource.getAllAreas();

    if (response != null) {
      areas.value = Areas.fromJson(response);
      update();
    }
  }

  void onChangeArea(value) async {
    cities.value = await getCities(value.id!);
    Get.find<SignupController>().setChoosedArea(value);

    if (cities.value.data?.isNotEmpty == true) {
      Get.find<SignupController>()
          .setChoosedCity(cities.value.data?.firstOrNull ?? City());
    }
  }

  Future<Cities> getCities(int cityId) async {
    final response = await remoteDataSource.getAllCities(cityId);

    log('ffffsfssfsf ${response}');

    if (response != null) {
      cities.value.data = [];
      for (var element in response) {
        log('fggggeeee ${element}');
        cities.value.data?.add(City.fromJson(element));
      }
      update();
    }

    log('asfasfasfsffsasf ${cities.value.data}');

    return cities.value;
  }

  void onChangeCity(value) {
    Get.find<SignupController>().setChoosedCity(value);
  }
}
