import '../models/all_offers_model.dart';

class AllOffersState {
  AllOffersModel? allOffers;
  String? errorMessage;
}

class AllOffersSuccessState extends AllOffersState {
  AllOffersSuccessState(AllOffersModel allOffers) {
    this.allOffers = allOffers;
  }
}

class AllOffersErrorState extends AllOffersState {
  AllOffersErrorState(String errorMessage) {
    this.errorMessage = errorMessage;
  }
}
