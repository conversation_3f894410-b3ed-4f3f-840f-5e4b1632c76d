import '../models/terms_of_service.dart';

class TermsOfServiceState {
  TermsOfServiceModel? termsOfService;
  String? errorMessage;
}

class TermsOfServiceSuccessState extends TermsOfServiceState {
  TermsOfServiceSuccessState(TermsOfServiceModel termsOfService) {
    this.termsOfService = termsOfService;
  }
}

class TermsOfServiceErrorState extends TermsOfServiceState {
  TermsOfServiceErrorState(String errorMessage) {
    this.errorMessage = errorMessage;
  }
}
