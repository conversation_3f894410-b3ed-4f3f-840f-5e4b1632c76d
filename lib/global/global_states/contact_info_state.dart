import 'package:get_clean/global/models/contact_info_model.dart';

class ContactInfoState {
  ContactInfoModel? contactInfo;
  String? errorMessage;
}

class ContactInfoSuccessState extends ContactInfoState {
  ContactInfoSuccessState(ContactInfoModel contactInfo) {
    this.contactInfo = contactInfo;
  }
}

class ContactInfoErrorState extends ContactInfoState {
  ContactInfoErrorState(String errorMessage) {
    this.errorMessage = errorMessage;
  }
}
