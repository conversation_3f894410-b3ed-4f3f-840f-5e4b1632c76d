import 'package:get_clean/global/models/all_services_model.dart';

class ServicesState {
  AllServicesModel? allServicesModel;
  String? errorMessage;
}

class ServicesSuccessState extends ServicesState {
  ServicesSuccessState(AllServicesModel allServicesModel) {
    this.allServicesModel = allServicesModel;
  }
}

class ServicesErrorState extends ServicesState {
  ServicesErrorState(String errorMessage) {
    this.errorMessage = errorMessage;
  }
}
