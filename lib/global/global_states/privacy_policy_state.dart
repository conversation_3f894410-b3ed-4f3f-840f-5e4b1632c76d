import '../models/privacy_policy_model.dart';

class PrivacyPolicyState {
  PrivacyPolicyModel? privacyPolicy;
  String? errorMessage;
}

class PrivacyPolicySuccessState extends PrivacyPolicyState {
  PrivacyPolicySuccessState(PrivacyPolicyModel privacyPolicy) {
    this.privacyPolicy = privacyPolicy;
  }
}

class PrivacyPolicyErrorState extends PrivacyPolicyState {
  PrivacyPolicyErrorState(String errorMessage) {
    this.errorMessage = errorMessage;
  }
}
