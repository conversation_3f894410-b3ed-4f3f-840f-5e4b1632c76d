import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:get_clean/global/widget/loading_widget.dart';

Future<File> pickFile({
  FileType type = FileType.image,
}) async {
  FilePickerResult? result = await FilePicker.platform.pickFiles(
    type: type,
    allowCompression: true,
  );

  if (result != null) {
    File file = File(result.files.single.path!);
    return file;
  } else {
    return File('');
  }
}

Future<List<File>> pickMultiFiles({
  FileType type = FileType.image,
}) async {
  FilePickerResult? result = await FilePicker.platform.pickFiles(
    type: type,
    allowCompression: true,
    allowMultiple: true,
  );

  if (result != null) {
    return result.paths.map((path) => File(path ?? '')).toList();
  } else {
    return [];
  }
}

void showWaitingIndicator() {
  Get.dialog(
    const LoadingWidget(),
  );
}

void hideWaitingIndicator() {
  Get.back();
}

void showErrorToast(String message) {
  Fluttertoast.showToast(
    msg: message,
    toastLength: Toast.LENGTH_LONG,
    gravity: ToastGravity.TOP,
    timeInSecForIosWeb: 3,
    backgroundColor: Colors.red,
    textColor: Colors.white,
    fontSize: 16.0,
  );
}

void showSuccessToast(String message) {
  Fluttertoast.showToast(
    msg: message,
    toastLength: Toast.LENGTH_LONG,
    gravity: ToastGravity.TOP,
    timeInSecForIosWeb: 3,
    backgroundColor: Colors.green,
    textColor: Colors.white,
    fontSize: 16.0,
  );
}

void showWarningToast(String message) {
  Fluttertoast.showToast(
    msg: message,
    toastLength: Toast.LENGTH_LONG,
    gravity: ToastGravity.TOP,
    timeInSecForIosWeb: 3,
    backgroundColor: Colors.yellow,
    textColor: Colors.black,
    fontSize: 16.0,
  );
}
