import 'dart:developer';

import 'package:firebase_dynamic_links/firebase_dynamic_links.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:get_clean/app/routes/app_pages.dart';
import 'package:get_clean/global/models/provider.dart';
import 'package:get_clean/global/models/provider_services.dart';

const mainDynamicLink = 'https://vishvish.page.link';
const _androidPackageName = 'com.vishvish.vishvishapp';
const _iosBundleId = 'com.vishvish.vishvishuserapp';

class DynamicLinkHandler {
  static final FirebaseDynamicLinks _dynamicLinks =
      FirebaseDynamicLinks.instance;

  static void _navigate(Uri deepLink) async {
    final providerQuery = deepLink.queryParameters;

    final provider = Provider.fromDynamicLink(providerQuery);

    //? navigate to provider page
    Get.toNamed(Routes.PROVIDER_PAGE_FROM_DYNAMIC_LINK, arguments: {
      'provider': provider,
      'service': ProviderServices.fromJson(
          provider.services?.firstOrNull?.toJson() ?? {}
          // controller
          // .choosedPopularService.value.service!
          // .toJson()
          ),
    });
  }

  static Future<void> initDynamicLink() async {
    FirebaseDynamicLinks.instance.onLink.listen((dynamicLink) async {
      if (dynamicLink.link != Uri.parse("uri")) {
        log(dynamicLink.link.queryParameters.toString());
        final Uri deepLink = dynamicLink.link;

        _navigate(deepLink);

        // you should handle the navigation or you logic here
      }
    }).onError((error) {
      debugPrint("error from dynamic link Stream : : :: ${error.toString()}");
    });
    final PendingDynamicLinkData? initialLink =
        await FirebaseDynamicLinks.instance.getInitialLink();
    try {
      if (initialLink != null) {
        log(initialLink.link.toString());
        final Uri deepLink = initialLink.link;

        log('asdkjaskdjl ${deepLink.queryParameters}');

        _navigate(deepLink);

        // you should handle the navigation or you logic here
      }
    } catch (e) {
      debugPrint('No deepLink found $e');
    }
  }

  static Future<String> createDynamicLink(String path,
      {bool isShort = true}) async {
    try {
      final DynamicLinkParameters parameters = DynamicLinkParameters(
        uriPrefix: mainDynamicLink,
        link: Uri.parse('$mainDynamicLink/$path'),
        // longDynamicLink: Uri.parse('$mainDynamicLink/$path'),
        androidParameters: const AndroidParameters(
          packageName: _androidPackageName,
          minimumVersion: 0,
        ),
        iosParameters: const IOSParameters(
          bundleId: _iosBundleId,
          minimumVersion: '0',
          appStoreId: '0',
        ),
        socialMetaTagParameters: const SocialMetaTagParameters(
          title: 'Vish Vish',
          description: 'Welcome To Vish Vish App',
        ),
      );

      Uri url;

      if (isShort) {
        final ShortDynamicLink shortLink =
            await _dynamicLinks.buildShortLink(parameters);
        url = shortLink.shortUrl;

        log('akhjksdjasdsdsfabf $url');
      } else {
        url = await _dynamicLinks.buildLink(parameters);
      }

      return url.toString();
    } catch (e) {
      debugPrint('Error while creating dynamic link $e');
      return '';
    }
  }
}

//import 'dart:developer';
//
// import 'package:firebase_dynamic_links/firebase_dynamic_links.dart';
// import 'package:flutter/cupertino.dart';
// import 'package:get/get_core/src/get_main.dart';
// import 'package:get/get_navigation/get_navigation.dart';
// import 'package:page/src/features/views/property_details/property_details.dart';
//
// import '../../features/views/ad_details/ads_details.dart';
// import '../../features/views/story/one_story_details.dart';
//
// const mainDynamicLink = 'https://dubaiuser.page.link';
// const _androidPackageName = 'ae.dubaipage.app_user';
// const _iosBundleId = 'ae.dubaipage.dubaiUserDeveloper';
//
// class DynamicLinkHandler {
//   static final FirebaseDynamicLinks _dynamicLinks =
//       FirebaseDynamicLinks.instance;
//
//   static void _navigate(Uri deepLink) {
//     if (deepLink.queryParameters['isReel'] == 'true') {
//       Get.to(() => OneStoryScreen(
//             id: int.tryParse(deepLink.queryParameters['id'] ?? '0'),
//           ));
//       return;
//     }
//
//     if (deepLink.queryParameters['type'] == 'properties') {
//       Get.to(() => PropertyDetails(
//           int.tryParse(deepLink.queryParameters['id'] ?? '0')!));
//       return;
//     } else if (deepLink.queryParameters['type'] == 'car-rental') {
//       Get.to(() => PropertyDetails(
//           int.tryParse(deepLink.queryParameters['id'] ?? '0')!));
//       return;
//     }else if (deepLink.queryParameters['type'] == 'reels') {
//       Get.to(() => PropertyDetails(
//           int.tryParse(deepLink.queryParameters['id'] ?? '0')!));
//       return;
//     } else {
//       Get.to(() => VideoViewWidget(
//             id: int.tryParse(deepLink.queryParameters['id'] ?? '0'),
//             planId: int.tryParse(deepLink.queryParameters['planId'] ?? '0'),
//             type: deepLink.queryParameters['type'] ?? '',
//             date: deepLink.queryParameters['date'] ?? '0',
//           ));
//       return;
//     }
//   }
//
//   static Future<void> initDynamicLink() async {
//     FirebaseDynamicLinks.instance.onLink.listen((dynamicLink) async {
//       if (dynamicLink.link != Uri.parse("uri")) {
//         log(dynamicLink.link.queryParameters.toString());
//         final Uri deepLink = dynamicLink.link;
//
//         log('asdkdjl2232 $deepLink');
//
//         _navigate(deepLink);
//
//         // you should handle the navigation or you logic here
//       }
//     }).onError((error) {
//       debugPrint("error from dynamic link Stream : : :: ${error.toString()}");
//     });
//     final PendingDynamicLinkData? initialLink =
//         await FirebaseDynamicLinks.instance.getInitialLink();
//     try {
//       if (initialLink != null) {
//         log(initialLink.link.toString());
//         final Uri deepLink = initialLink.link;
//
//         log('asdkjaskdjl ${deepLink.queryParameters}');
//
//         _navigate(deepLink);
//
//         // you should handle the navigation or you logic here
//       }
//     } catch (e) {
//       debugPrint('No deepLink found $e');
//     }
//   }
//
//   static Future<String> createDynamicLink(String path,
//       {bool isShort = true}) async {
//     try {
//       final DynamicLinkParameters parameters = DynamicLinkParameters(
//         uriPrefix: mainDynamicLink,
//         // 'http://dubaiuser.page.link',
//         link: Uri.parse('$mainDynamicLink/$path'),
//         // longDynamicLink: Uri.parse('$mainDynamicLink/$path'),
//         androidParameters: const AndroidParameters(
//           packageName: _androidPackageName,
//           minimumVersion: 0,
//         ),
//         iosParameters: const IOSParameters(
//           bundleId: _iosBundleId,
//           minimumVersion: '0',
//           appStoreId: '0',
//         ),
//         socialMetaTagParameters: const SocialMetaTagParameters(
//           title: 'Dubai Page',
//           description: 'Welcome To Dubai Page App',
//         ),
//       );
//
//       Uri url;
//
//       if (isShort) {
//         final ShortDynamicLink shortLink =
//             await _dynamicLinks.buildShortLink(parameters);
//         url = shortLink.shortUrl;
//
//         log('akhjksdjasdsdsfabf $url');
//       } else {
//         url = await _dynamicLinks.buildLink(parameters);
//       }
//
//       return url.toString();
//     } catch (e) {
//       debugPrint('Error while creating dynamic link $e');
//       return '';
//     }
//   }
// }
