// DO NOT EDIT. This is code generated via package:get_cli/get_cli.dart

// ignore_for_file: lines_longer_than_80_chars
// ignore: avoid_classes_with_only_static_members
class AppTranslation {
  static Map<String, Map<String, String>> translations = {
    'en': Locales.en,
    'he': Locales.he,
    'ar': Locales.ar,
  };
}

class LocaleKeys {
  LocaleKeys._();
  static const name = 'name';
  static const login = 'login';
  static const yourPhoneNumberOrEmail = 'yourPhoneNumberOrEmail';
  static const password = 'password';
  static const rememberMe = 'rememberMe';
  static const forgetPassword = 'forgetPassword';
  static const signup = 'signup';
  static const selectedLanguage = 'selectedLanguage';
  static const skip = 'skip';
  static const aboutUs = 'aboutUs';
  static const forMoreInfoContactUs = 'forMoreInfoContactUs';
  static const email = 'email';
  static const phone = 'phone';
  static const popularServices = 'popularServices';
  static const services = 'services';
  static const congratulation = 'congratulation';
  static const welcomeToHomePage = 'welcomeToHomePage';
  static const colsultationRequest = 'colsultationRequest';
  static const askForYourAdvice = 'askForYourAdvice';
  static const financialConsulting = 'financialConsulting';
  static const legalAdvice = 'legalAdvice';
  static const faq = 'faq';
  static const viewAll = 'viewAll';
  static const home = 'home';
  static const myOrders = 'myOrders';
  static const packages = 'packages';
  static const languages = 'languages';
  static const pending = 'pending';
  static const accepted = 'accepted';
  static const oldOrders = 'oldOrders';
  static const notifications = 'notifications';
  static const privacyPolicy = 'privacyPolicy';
  static const search = 'search';
  static const createAnAccount = 'createAnAccount';
  static const fullName = 'fullName';
  static const yourEmail = 'yourEmail';
  static const yourPhoneNumber = 'yourPhoneNumber';
  static const type = 'type';
  static const user = 'user';
  static const provider = 'provider';
  static const company = 'company';
  static const termsOfService = 'termsOfService';
  static const verificationCode = 'verificationCode';
  static const pleaseEnterVerificationCode = 'pleaseEnterVerificationCode';
  static const resendCode = 'resendCode';
  static const submit = 'submit';
  static const companyName = 'companyName';
  static const chooseArea = 'chooseArea';
  static const area = 'area';
  static const chooseCity = 'chooseCity';
  static const city = 'city';
  static const companyAddress = 'companyAddress';
  static const companyNumber = 'companyNumber';
  static const companyId = 'companyId';
  static const idFile = 'idFile';
  static const companyPicture = 'companyPicture';
  static const myProfile = 'myProfile';
  static const myBooking = 'myBooking';
  static const wallet = 'wallet';
  static const language = 'language';
  static const support = 'support';
  static const logout = 'logout';
  static const termsAndConditions = 'termsAndConditions';
  static const details = 'details';
  static const confirm = 'confirm';
  static const providerName = 'providerName';
  static const providerAddress = 'providerAddress';
  static const providerNumber = 'providerNumber';
  static const providerId = 'providerId';
  static const providerPicture = 'providerPicture';
  static const selectedDay = 'selectedDay';
  static const done = 'done';
  static const address = 'address';
  static const requestSentSuccessfully = 'requestSentSuccessfully';
  static const agreeWithTermsAndPrivacy = 'agreeWithTermsAndPrivacy';
  static const service = 'service';
  static const howManyHours = 'howManyHours';
  static const hour = 'hour';
  static const whatTime = 'whatTime';
  static const pay = 'pay';
  static const cancelAllAppointments = 'cancelAllAppointments';
  static const serviceType = 'serviceType';
  static const frequency = 'frequency';
  static const duration = 'duration';
  static const cleaningMaterial = 'cleaningMaterial';
  static const paymentDetails = 'paymentDetails';
  static const finalPrice = 'finalPrice';
  static const deposit20 = 'deposit20';
  static const status = 'status';
  static const payment = 'payment';
  static const payTheRestOfTheAmount = 'payTheRestOfTheAmount';
  static const next = 'next';
  static const cancel = 'cancel';
  static const addCard = 'addCard';
  static const weAccept = 'weAccept';
  static const rememberThisCard = 'rememberThisCard';
  static const rememberThisCardText = 'rememberThisCardText';
  static const paymentCompeletedSuccessfully = 'paymentCompeletedSuccessfully';
  static const rateYourProvider = 'rateYourProvider';
  static const writeComment = 'writeComment';
  static const rate = 'rate';
  static const yes = 'yes';
  static const no = 'no';
  static const cancelRequestMessage = 'cancelRequestMessage';
  static const ifYouCancelYouLoseMoney = 'ifYouCancelYouLoseMoney';
  static const orderCanceledSuccessfully = 'orderCanceledSuccessfully';
  static const cancelThisAppointment = 'cancelThisAppointment';
  static const orderDetails = 'orderDetails';
  static const paymentMethod = 'paymentMethod';
  static const cash = 'cash';
  static const deposit = 'deposit';
  static const total = 'total';
  static const pleaseEnterPhoneNumber = 'pleaseEnterPhoneNumber';
  static const phoneNumber = 'phoneNumber';
  static const didntreceiverotp = 'didntreceiverotp';
  static const newPassword = 'newPassword';
  static const passwordChangedSuccessfully = 'passwordChangedSuccessfully';
  static const workingTimes = 'workingTimes';
  static const holidays = 'holidays';
  static const makeRequest = 'makeRequest';
  static const skills = 'skills';
  static const workAreas = 'workAreas';
  static const salary = 'salary';
  static const days = 'days';
  static const open = 'open';
  static const close = 'close';
  static const from = 'from';
  static const to = 'to';
  static const reviews = 'reviews';
  static const changePassword = 'changePassword';
  static const gender = 'gender';
  static const age = 'age';
  static const save = 'save';
  static const providerPackages = 'providerPackages';
  static const urgentRequest = 'urgentRequest';
  static const today = 'today';
  static const iNeedMaterial = 'iNeedMaterial';
  static const howManyTimes = 'howManyTimes';
  static const onece = 'onece';
  static const weekly = 'weekly';
  static const monthly = 'monthly';
  static const note = 'note';
  static const selectDays = 'selectDays';
  static const mon = 'mon';
  static const tue = 'tue';
  static const wed = 'wed';
  static const thu = 'thu';
  static const fri = 'fri';
  static const sat = 'sat';
  static const sun = 'sun';
  static const providerWillContactYou = 'providerWillContactYou';
  static const gotoOrderDetialsPage = 'gotoOrderDetialsPage';
  static const pleaseEnterFullName = 'pleaseEnterFullName';
  static const pleaseEnterEmail = 'pleaseEnterEmail';
  static const pleaseEnterPhone = 'pleaseEnterPhone';
  static const pleaseEnterPassword = 'pleaseEnterPassword';
  static const pleaseEnterYourAddress = 'pleaseEnterYourAddress';
  static const pleaseChooseCity = 'pleaseChooseCity';
  static const pleaseChooseArea = 'pleaseChooseArea';
  static const pleaseEnterProviderName = 'pleaseEnterProviderName';
  static const pleaseEnterProviderAddress = 'pleaseEnterProviderAddress';
  static const pleaseEnterProviderPhoneNumber =
      'pleaseEnterProviderPhoneNumber';
  static const pleaseEnterProviderIdNumber = 'pleaseEnterProviderIdNumber';
  static const pleaseEnterChooseProviderImage =
      'pleaseEnterChooseProviderImage';
  static const pleaseEnterCompanyName = 'pleaseEnterCompanyName';
  static const pleaseEnterCompanyAddress = 'pleaseEnterCompanyAddress';
  static const pleaseEnterCompanyPhoneNumber = 'pleaseEnterCompanyPhoneNumber';
  static const pleaseEnterCompanyIdNumber = 'pleaseEnterCompanyIdNumber';
  static const pleaseEnterChooseCompanyImage = 'pleaseEnterChooseCompanyImage';
  static const pleaseEnterEmailOrPassword = 'pleaseEnterEmailOrPassword';
  static const pleaseInsertCode = 'pleaseInsertCode';
  static const pleaseInsertCorrectCode = 'pleaseInsertCorrectCode';
  static const newPasswordConfirmation = 'newPasswordConfirmation';
  static const pleaseInsertPassword = 'pleaseInsertPassword';
  static const pleaseInsertPasswordConfirmation =
      'pleaseInsertPasswordConfirmation';
  static const yourBirthDate = 'yourBirthDate';
  static const pleaseInsertYourBirthDate = 'pleaseInsertYourBirthDate';
  static const ok = 'ok';
  static const add = 'add';
  static const editSchduel = 'editSchduel';
  static const schduelYourOrders = 'schduelYourOrders';
  static const failedToLoad = 'failedToLoad';
  static const deleteWorkingTime = 'deleteWorkingTime';
  static const deleteWorkingTimeMessage = 'deleteWorkingTimeMessage';
  static const fromDateHoliday = 'fromDateHoliday';
  static const toDateHoliday = 'toDateHoliday';
  static const deleteHoliday = 'deleteHoliday';
  static const deleteHolidayMessage = 'deleteHolidayMessage';
  static const pleaseChooseDaysFirst = 'pleaseChooseDaysFirst';
  static const pricing = 'pricing';
  static const addYourPriceForEachCategory = 'addYourPriceForEachCategory';
  static const news = 'news';
  static const addNewService = 'addNewService';
  static const materialPrice = 'materialPrice';
  static const price = 'price';
  static const sofaType = 'sofaType';
  static const withTax = 'withTax';
  static const withoutTax = 'withoutTax';
  static const deleteService = 'deleteService';
  static const deleteServiceMessage = 'deleteServiceMessage';
  static const offers = 'offers';
  static const myOffers = 'myOffers';
  static const bookNow = 'bookNow';
  static const addOffer = 'addOffer';
  static const offerDetails = 'offerDetails';
  static const averageTime = 'averageTime';
  static const description = 'description';
  static const deleteOffer = 'deleteOffer';
  static const deleteOfferMessage = 'deleteOfferMessage';
  static const edit = 'edit';
  static const delete = 'delete';
  static const isActive = 'isActive';
  static const male = 'male';
  static const female = 'female';
  static const editService = 'editService';
  static const newOffers = 'newOffers';
  static const onlyFor = 'onlyFor';
  static const ils = 'ils';
  static const providerAvilableTimes = 'providerAvilableTimes';
  static const howManyMeters = 'howManyMeters';
  static const pleaseWait = 'pleaseWait';
  static const calculatePrice = 'calculatePrice';
  static const approveThisOrder = 'approveThisOrder';
  static const orderApprovedSuccessfully = 'orderApprovedSuccessfully';
  static const compeleted = 'compeleted';
  static const orderCompeletedSuccessfully = 'orderCompeletedSuccessfully';
  static const meter = 'meter';
  static const sendToAll = 'sendToAll';
  static const message = 'message';
  static const sofas = 'sofas';
  static const everyOne = 'everyOne';
  static const noAvilableTimes = 'noAvilableTimes';
  static const submitNewOffer = 'submitNewOffer';
  static const trackYourOrder = 'trackYourOrder';
  static const uploadInvoice = 'uploadInvoice';
  static const orderSchedule = 'orderSchedule';
  static const startsAt = 'startsAt';
  static const endsAt = 'endsAt';
  static const addTip = 'addTip';
  static const payDeposit = 'payDeposit';
  static const requestInvoice = 'requestInvoice';
  static const amount = 'amount';
  static const requestExtraTime = 'requestExtraTime';
  static const theRequired = 'theRequired';
  static const requestedExtraTime = 'requestedExtraTime';
  static const areYouAccept = 'areYouAccept';
  static const accept = 'accept';
  static const reject = 'reject';
  static const showInvoice = 'showInvoice';
  static const date = 'date';
  static const count = 'count';
  static const bookingTime = 'bookingTime';
  static const noAvilableProviders = 'noAvilableProviders';
  static const tips = 'tips';
}

class Locales {
  static const en = {
    'name': 'Name',
    'login': 'Login',
    'yourPhoneNumberOrEmail': 'Your Phone Number Or E-Mail',
    'password': 'Password',
    'rememberMe': 'Remember Me',
    'forgetPassword': 'Forgert Password ?',
    'signup': 'Sign Up',
    'selectedLanguage': 'Selected Language',
    'skip': 'Skip',
    'aboutUs': 'About Us',
    'forMoreInfoContactUs': 'For More Info Contact Us : ',
    'email': 'E-Mail',
    'phone': 'Phone',
    'popularServices': 'Popular Services',
    'services': 'Services',
    'congratulation': 'Congratulation',
    'welcomeToHomePage': 'welcome to Vish Vish',
    'colsultationRequest': 'Colsultation Request',
    'askForYourAdvice': 'Easily Ask For Your Advice',
    'financialConsulting': 'Finacial Consultig',
    'legalAdvice': 'Legal Advice',
    'faq': 'FAQ',
    'viewAll': 'View All',
    'home': 'Home',
    'myOrders': 'My Orders',
    'packages': 'Packages',
    'languages': 'Languages',
    'pending': 'Pending',
    'accepted': 'Accepted',
    'oldOrders': 'Old Orders',
    'notifications': 'Notifications',
    'privacyPolicy': 'Privacy Policy',
    'search': 'Search',
    'createAnAccount': 'Create An Account',
    'fullName': 'Full Name',
    'yourEmail': 'Your E-Mail',
    'yourPhoneNumber': 'Your Phone Number',
    'type': 'Type',
    'user': 'User',
    'provider': 'Provider',
    'company': 'Company',
    'termsOfService': 'Terms Of Service',
    'verificationCode': 'Verification Code',
    'pleaseEnterVerificationCode':
        'Please Enter The Verification Code Sent To Your Phone',
    'resendCode': 'Resend Code ?',
    'submit': 'Submit',
    'companyName': 'Company Name',
    'chooseArea': 'Choose Area',
    'area': 'Area',
    'chooseCity': 'Choose City',
    'city': 'City',
    'companyAddress': 'Company Address',
    'companyNumber': 'Company Number',
    'companyId': 'Company Id',
    'idFile': 'Id File',
    'companyPicture': 'Company Picture',
    'myProfile': 'My Profile',
    'myBooking': 'My Booking',
    'wallet': 'Wallet',
    'language': 'Language',
    'support': 'Support',
    'logout': 'Logout',
    'termsAndConditions': 'Terms And Conditions',
    'details': 'Details',
    'confirm': 'Confirm',
    'providerName': 'Provider Name',
    'providerAddress': 'Provider Address',
    'providerNumber': 'Provider Number',
    'providerId': 'Provider Id',
    'providerPicture': 'Provider Picture',
    'selectedDay': 'Select Day',
    'done': 'Done',
    'address': 'Address',
    'requestSentSuccessfully': 'Your Request Sent Successfully',
    'agreeWithTermsAndPrivacy': 'I Agreee With Privacy Policy And Terms',
    'service': 'Service',
    'howManyHours': 'How Many Hours ?',
    'hour': 'Hr',
    'whatTime': 'What Time',
    'pay': 'Pay',
    'cancelAllAppointments': 'Cancel All Appointments Of The Booking',
    'serviceType': 'Service Type',
    'frequency': 'Frequency',
    'duration': 'Duration',
    'cleaningMaterial': 'Cleaning Material',
    'paymentDetails': 'Payment Details',
    'finalPrice': 'Final Price',
    'deposit20': 'Deposit (20%)',
    'status': 'Status : ',
    'payment': 'Payment',
    'payTheRestOfTheAmount': 'Pay The Rest Of The Amount : ',
    'next': 'Next',
    'cancel': 'Cancel',
    'addCard': 'Add Card',
    'weAccept': 'We Accept',
    'rememberThisCard': 'Remember This Card',
    'rememberThisCardText':
        'Getclean Will Securely App This Card For A Faster Payment Experience Your Cvv Number Will Not Be Stored',
    'paymentCompeletedSuccessfully': 'Payment Compeleted Successfully',
    'rateYourProvider': 'Rate Your Provider',
    'writeComment': 'Write A Comment',
    'rate': 'Rate',
    'yes': 'Yes',
    'no': 'No',
    'cancelRequestMessage': 'Are You Sure You Want To Cancel Request ?',
    'ifYouCancelYouLoseMoney':
        'If You Canceled, You Will Lose Your Deposit Money',
    'orderCanceledSuccessfully': 'your order has been cancel successfully',
    'cancelThisAppointment': 'Cancel This Appointment',
    'orderDetails': 'Order Details',
    'paymentMethod': 'Payment Method',
    'cash': 'Cash',
    'deposit': 'Deposit',
    'total': 'Total',
    'pleaseEnterPhoneNumber':
        'Please Enter Your phone number To Receive A Verification Code .',
    'phoneNumber': 'Phone Number',
    'didntreceiverotp': 'Didn\'t Receive An Otp ?',
    'newPassword': 'New Password',
    'passwordChangedSuccessfully': 'you changes your password successfully',
    'workingTimes': 'Working Times',
    'holidays': 'Holidays',
    'makeRequest': 'Make A Request',
    'skills': 'Skills : ',
    'workAreas': 'Work Areas : ',
    'salary': 'Salary : ',
    'days': 'Days',
    'open': 'Open',
    'close': 'Close',
    'from': 'From',
    'to': 'To',
    'reviews': 'Reviews',
    'changePassword': 'Change Password',
    'gender': 'Gender',
    'age': 'Age',
    'save': 'Save',
    'providerPackages': 'Provider Packages',
    'urgentRequest': 'Urgent Request',
    'today': 'Today',
    'iNeedMaterial': 'I Need Materials',
    'howManyTimes': 'How Many Times Do You Need This Service ?',
    'onece': 'Once',
    'weekly': 'Weekly',
    'monthly': 'Monthly',
    'note': 'Note',
    'selectDays': 'Select One Or More Days For Your Cleaning ?',
    'mon': 'Mon',
    'tue': 'Tue',
    'wed': 'Wed',
    'thu': 'Thu',
    'fri': 'Fri',
    'sat': 'Sat',
    'sun': 'Sun',
    'providerWillContactYou':
        'provider will contact you to confirm your request',
    'gotoOrderDetialsPage': 'Go to your order details page',
    'pleaseEnterFullName': 'Please Enter Your Full Name',
    'pleaseEnterEmail': 'Please Enter Your Email',
    'pleaseEnterPhone': 'Please Enter Your Phone Number',
    'pleaseEnterPassword': 'Please Enter Password',
    'pleaseEnterYourAddress': 'Please Enter Your Address',
    'pleaseChooseCity': 'Please Choose City',
    'pleaseChooseArea': 'Please Choose Area',
    'pleaseEnterProviderName': 'Please Enter Provider Name',
    'pleaseEnterProviderAddress': 'Please Enter Provider Address',
    'pleaseEnterProviderPhoneNumber': 'Please Enter Provider Phone Number',
    'pleaseEnterProviderIdNumber': 'Please Enter Provider Id Number',
    'pleaseEnterChooseProviderImage': 'Please Choose Provider Image',
    'pleaseEnterCompanyName': 'Please Enter Company Name',
    'pleaseEnterCompanyAddress': 'Please Enter Company Address',
    'pleaseEnterCompanyPhoneNumber': 'Please Enter Company Phone Number',
    'pleaseEnterCompanyIdNumber': 'Please Enter Company Id Number',
    'pleaseEnterChooseCompanyImage': 'Please Choose Company Image',
    'pleaseEnterEmailOrPassword': 'Please Enter Your Email Or Password',
    'pleaseInsertCode': 'Please Enter OTP Code First',
    'pleaseInsertCorrectCode': 'Please Enter Correct OTP Code',
    'newPasswordConfirmation': 'Confirm New Password',
    'pleaseInsertPassword': 'Please Insert New Password',
    'pleaseInsertPasswordConfirmation': 'Please Confirm New Password',
    'yourBirthDate': 'Birth Date',
    'pleaseInsertYourBirthDate': 'Please Choose Your Birth Date',
    'ok': 'OK',
    'add': 'Add',
    'editSchduel': 'Edit Schduel',
    'schduelYourOrders': 'Schduel Your Orders',
    'failedToLoad': 'Failed To Load Data Please Try Again Later',
    'deleteWorkingTime': 'Delete Working Time',
    'deleteWorkingTimeMessage': 'Are You Sure To Delete This Working Time',
    'fromDateHoliday': 'Choose The Begining Date Of Holiday',
    'toDateHoliday': 'Choose The End Date Of Holiday',
    'deleteHoliday': 'Delete Holiday',
    'deleteHolidayMessage': 'Are You Sure You Need To Delete This Holiday',
    'pleaseChooseDaysFirst': 'Please Choose Days First',
    'pricing': 'Pricing',
    'addYourPriceForEachCategory': 'Add Your Price For Each Category',
    'news': 'New +',
    'addNewService': 'Add New Service',
    'materialPrice': 'Material Price',
    'price': 'Price',
    'sofaType': 'Sofa Type',
    'withTax': 'With Tax',
    'withoutTax': 'Without Tax',
    'deleteService': 'Delete Service',
    'deleteServiceMessage': 'Are You Sure To Delete This Service ?',
    'offers': 'Offers',
    'myOffers': 'My Offers',
    'bookNow': 'Book Now',
    'addOffer': 'Add Offer',
    'offerDetails': 'Offer Details',
    'averageTime': 'Average Time',
    'description': 'Description',
    'deleteOffer': 'Delete Offer',
    'deleteOfferMessage': 'Are You Sure To Delete This Offer ?',
    'edit': 'Edit',
    'delete': 'Delete',
    'isActive': 'Is Active',
    'male': 'Male',
    'female': 'Female',
    'editService': 'Edit Service',
    'newOffers': 'New Offers',
    'onlyFor': 'Only For',
    'ils': 'ILS',
    'providerAvilableTimes': 'Provider Avalible Time',
    'howManyMeters': 'How Many Meters ?',
    'pleaseWait': 'Please Wait',
    'calculatePrice': 'Please Wait While Calculate Price',
    'approveThisOrder': 'Approve This Order',
    'orderApprovedSuccessfully': 'Order Approved Successfully',
    'compeleted': 'Compeleted',
    'orderCompeletedSuccessfully': 'Order Compeleted Successfully',
    'meter': 'Meter',
    'sendToAll': 'Send To All',
    'message': 'Message',
    'sofas': 'Please choose the number of each type',
    'everyOne': 'Every One',
    'noAvilableTimes': 'No Avalible Times',
    'submitNewOffer': 'Submit New Offer',
    'trackYourOrder': 'Track Your Order',
    'uploadInvoice': 'Upload Invoice',
    'orderSchedule': 'Order Schedule',
    'startsAt': 'Starts At',
    'endsAt': 'Ends At',
    'addTip': 'Add Tip',
    'payDeposit': 'Pay Deposit',
    'requestInvoice': 'Request Invoice',
    'amount': 'Amount',
    'requestExtraTime': 'Request Extra Time',
    'theRequired': 'The Required',
    'requestedExtraTime': 'Requests Extra Time',
    'areYouAccept': 'Are You Accept ?',
    'accept': 'Accept',
    'reject': 'Reject',
    'showInvoice': 'Show Invoice',
    'date': 'Date',
    'count': 'The Count',
    'bookingTime': 'Booking Time',
    'noAvilableProviders': 'No Avilable Providers',
    'tips': 'Tips',
  };
  static const he = {
    'name': 'Name',
    'login': 'Login',
    'yourPhoneNumberOrEmail': 'Your Phone Number Or E-Mail',
    'password': 'Password',
    'rememberMe': 'Remember Me',
    'forgetPassword': 'Forgert Password ?',
    'signup': 'Sign Up',
    'selectedLanguage': 'Selected Language',
    'skip': 'Skip',
    'aboutUs': 'About Us',
    'forMoreInfoContactUs': 'For More Info Contact Us : ',
    'email': 'E-Mail',
    'phone': 'Phone',
    'popularServices': 'Popular Services',
    'services': 'Services',
    'congratulation': 'Congratulation',
    'welcomeToHomePage': 'welcome to Vish Vish',
    'colsultationRequest': 'Colsultation Request',
    'askForYourAdvice': 'Easily Ask For Your Advice',
    'financialConsulting': 'Finacial Consultig',
    'legalAdvice': 'Legal Advice',
    'faq': 'FAQ',
    'viewAll': 'View All',
    'home': 'Home',
    'myOrders': 'My Orders',
    'packages': 'Packages',
    'languages': 'Languages',
    'pending': 'Pending',
    'accepted': 'Accepted',
    'oldOrders': 'Old Orders',
    'notifications': 'Notifications',
    'privacyPolicy': 'Privacy Policy',
    'search': 'Search',
    'createAnAccount': 'Create An Account',
    'fullName': 'Full Name',
    'yourEmail': 'Your E-Mail',
    'yourPhoneNumber': 'Your Phone Number',
    'type': 'Type',
    'user': 'User',
    'provider': 'Provider',
    'company': 'Company',
    'termsOfService': 'Terms Of Service',
    'verificationCode': 'Verification Code',
    'pleaseEnterVerificationCode':
        'Please Enter The Verification Code Sent To Your Phone',
    'resendCode': 'Resend Code ?',
    'submit': 'Submit',
    'companyName': 'Company Name',
    'chooseArea': 'Choose Area',
    'area': 'Area',
    'chooseCity': 'Choose City',
    'city': 'City',
    'companyAddress': 'Company Address',
    'companyNumber': 'Company Number',
    'companyId': 'Company Id',
    'idFile': 'Id File',
    'companyPicture': 'Company Picture',
    'myProfile': 'My Profile',
    'myBooking': 'My Booking',
    'wallet': 'Wallet',
    'language': 'Language',
    'support': 'Support',
    'logout': 'Logout',
    'termsAndConditions': 'Terms And Conditions',
    'details': 'Details',
    'confirm': 'Confirm',
    'providerName': 'Provider Name',
    'providerAddress': 'Provider Address',
    'providerNumber': 'Provider Number',
    'providerId': 'Provider Id',
    'providerPicture': 'Provider Picture',
    'selectedDay': 'Select Day',
    'done': 'Done',
    'address': 'Address',
    'requestSentSuccessfully': 'Your Request Sent Successfully',
    'agreeWithTermsAndPrivacy': 'I Agreee With Privacy Policy And Terms',
    'service': 'Service',
    'howManyHours': 'How Many Hours ?',
    'hour': 'Hr',
    'whatTime': 'What Time',
    'pay': 'Pay',
    'cancelAllAppointments': 'Cancel All Appointments Of The Booking',
    'serviceType': 'Service Type',
    'frequency': 'Frequency',
    'duration': 'Duration',
    'cleaningMaterial': 'Cleaning Material',
    'paymentDetails': 'Payment Details',
    'finalPrice': 'Final Price',
    'deposit20': 'Deposit (20%)',
    'status': 'Status : ',
    'payment': 'Payment',
    'payTheRestOfTheAmount': 'Pay The Rest Of The Amount : ',
    'next': 'Next',
    'cancel': 'Cancel',
    'addCard': 'Add Card',
    'weAccept': 'We Accept',
    'rememberThisCard': 'Remember This Card',
    'rememberThisCardText':
        'Getclean Will Securely App This Card For A Faster Payment Experience Your Cvv Number Will Not Be Stored',
    'paymentCompeletedSuccessfully': 'Payment Compeleted Successfully',
    'rateYourProvider': 'Rate Your Provider',
    'writeComment': 'Write A Comment',
    'rate': 'Rate',
    'yes': 'Yes',
    'no': 'No',
    'cancelRequestMessage': 'Are You Sure You Want To Cancel Request ?',
    'ifYouCancelYouLoseMoney':
        'If You Canceled, You Will Lose Your Deposit Money',
    'orderCanceledSuccessfully': 'your order has been cancel successfully',
    'cancelThisAppointment': 'Cancel This Appointment',
    'orderDetails': 'Order Details',
    'paymentMethod': 'Payment Method',
    'cash': 'Cash',
    'deposit': 'Deposit',
    'total': 'Total',
    'pleaseEnterPhoneNumber':
        'Please Enter Your phone number To Receive A Verification Code .',
    'phoneNumber': 'Phone Number',
    'didntreceiverotp': 'Didn\'t Receive An Otp ?',
    'newPassword': 'New Password',
    'passwordChangedSuccessfully': 'you changes your password successfully',
    'workingTimes': 'Working Times',
    'holidays': 'Holidays',
    'makeRequest': 'Make A Request',
    'skills': 'Skills : ',
    'workAreas': 'Work Areas : ',
    'salary': 'Salary : ',
    'days': 'Days',
    'open': 'Open',
    'close': 'Close',
    'from': 'From',
    'to': 'To',
    'reviews': 'Reviews',
    'changePassword': 'Change Password',
    'gender': 'Gender',
    'age': 'Age',
    'save': 'Save',
    'providerPackages': 'Provider Packages',
    'urgentRequest': 'Urgent Request',
    'today': 'Today',
    'iNeedMaterial': 'I Need Materials',
    'howManyTimes': 'How Many Times Do You Need This Service ?',
    'onece': 'Once',
    'weekly': 'Weekly',
    'monthly': 'Monthly',
    'note': 'Note',
    'selectDays': 'Select One Or More Days For Your Cleaning ?',
    'mon': 'Mon',
    'tue': 'Tue',
    'wed': 'Wed',
    'thu': 'Thu',
    'fri': 'Fri',
    'sat': 'Sat',
    'sun': 'Sun',
    'providerWillContactYou':
        'provider will contact you to confirm your request',
    'gotoOrderDetialsPage': 'Go to your order details page',
    'pleaseEnterFullName': 'Please Enter Your Full Name',
    'pleaseEnterEmail': 'Please Enter Your Email',
    'pleaseEnterPhone': 'Please Enter Your Phone Number',
    'pleaseEnterPassword': 'Please Enter Password',
    'pleaseEnterYourAddress': 'Please Enter Your Address',
    'pleaseChooseCity': 'Please Choose City',
    'pleaseChooseArea': 'Please Choose Area',
    'pleaseEnterProviderName': 'Please Enter Provider Name',
    'pleaseEnterProviderAddress': 'Please Enter Provider Address',
    'pleaseEnterProviderPhoneNumber': 'Please Enter Provider Phone Number',
    'pleaseEnterProviderIdNumber': 'Please Enter Provider Id Number',
    'pleaseEnterChooseProviderImage': 'Please Choose Provider Image',
    'pleaseEnterCompanyName': 'Please Enter Company Name',
    'pleaseEnterCompanyAddress': 'Please Enter Company Address',
    'pleaseEnterCompanyPhoneNumber': 'Please Enter Company Phone Number',
    'pleaseEnterCompanyIdNumber': 'Please Enter Company Id Number',
    'pleaseEnterChooseCompanyImage': 'Please Choose Company Image',
    'pleaseEnterEmailOrPassword': 'Please Enter Your Email Or Password',
    'pleaseInsertCode': 'Please Enter OTP Code First',
    'pleaseInsertCorrectCode': 'Please Enter Correct OTP Code',
    'newPasswordConfirmation': 'Confirm New Password',
    'pleaseInsertPassword': 'Please Insert New Password',
    'pleaseInsertPasswordConfirmation': 'Please Confirm New Password',
    'yourBirthDate': 'Birth Date',
    'pleaseInsertYourBirthDate': 'Please Choose Your Birth Date',
    'ok': 'OK',
    'add': 'Add',
    'editSchduel': 'Edit Schduel',
    'schduelYourOrders': 'Schduel Your Orders',
    'failedToLoad': 'Failed To Load Data Please Try Again Later',
    'deleteWorkingTime': 'Delete Working Time',
    'deleteWorkingTimeMessage': 'Are You Sure To Delete This Working Time',
    'fromDateHoliday': 'Choose The Begining Date Of Holiday',
    'toDateHoliday': 'Choose The End Date Of Holiday',
    'deleteHoliday': 'Delete Holiday',
    'deleteHolidayMessage': 'Are You Sure You Need To Delete This Holiday',
    'pleaseChooseDaysFirst': 'Please Choose Days First',
    'pricing': 'Pricing',
    'addYourPriceForEachCategory': 'Add Your Price For Each Category',
    'news': 'New +',
    'addNewService': 'Add New Service',
    'materialPrice': 'Material Price',
    'price': 'Price',
    'sofaType': 'Sofa Type',
    'withTax': 'With Tax',
    'withoutTax': 'Without Tax',
    'deleteService': 'Delete Service',
    'deleteServiceMessage': 'Are You Sure To Delete This Service ?',
    'offers': 'Offers',
    'myOffers': 'My Offers',
    'bookNow': 'Book Now',
    'addOffer': 'Add Offer',
    'offerDetails': 'Offer Details',
    'averageTime': 'Average Time',
    'description': 'Description',
    'deleteOffer': 'Delete Offer',
    'deleteOfferMessage': 'Are You Sure To Delete This Offer ?',
    'edit': 'Edit',
    'delete': 'Delete',
    'isActive': 'Is Active',
    'male': 'Male',
    'female': 'Female',
    'editService': 'Edit Service',
    'newOffers': 'New Offers',
    'onlyFor': 'Only For',
    'ils': 'ILS',
    'providerAvilableTimes': 'Provider Avalible Time',
    'howManyMeters': 'How Many Meters ?',
    'pleaseWait': 'Please Wait',
    'calculatePrice': 'Please Wait While Calculate Price',
    'approveThisOrder': 'Approve This Order',
    'orderApprovedSuccessfully': 'Order Approved Successfully',
    'compeleted': 'Compeleted',
    'orderCompeletedSuccessfully': 'Order Compeleted Successfully',
    'meter': 'Meter',
    'sendToAll': 'Send To All',
    'message': 'Message',
    'sofas': 'Please choose the number of each type',
    'everyOne': 'Every One',
    'noAvilableTimes': 'No Avalible Times',
    'submitNewOffer': 'Submit New Offer',
    'trackYourOrder': 'Track Your Order',
    'uploadInvoice': 'Upload Invoice',
    'orderSchedule': 'Order Schedule',
    'startsAt': 'Starts At',
    'endsAt': 'Ends At',
    'addTip': 'Add Tip',
    'payDeposit': 'Pay Deposit',
    'requestInvoice': 'Request Invoice',
    'amount': 'Amount',
    'requestExtraTime': 'Request Extra Time',
    'theRequired': 'The Required',
    'requestedExtraTime': 'Requests Extra Time',
    'areYouAccept': 'Are You Accept ?',
    'accept': 'Accept',
    'reject': 'Reject',
    'showInvoice': 'Show Invoice',
    'date': 'Date',
    'count': 'The Count',
    'bookingTime': 'Booking Time',
    'noAvilableProviders': 'No Avilable Providers',
    'tips': 'Tips',
  };
  static const ar = {
    'name': 'الإسم',
    'login': 'تسجيل الدخول',
    'yourPhoneNumberOrEmail': 'رقم الهاتف أو البريد الإلكرتوني',
    'password': 'كلمة المرور',
    'rememberMe': 'تذكرني',
    'forgetPassword': 'نسيت كلمة المرور ؟',
    'signup': 'مستخدم جديد',
    'selectedLanguage': 'اللغة المستخدمة',
    'skip': 'تخطي',
    'aboutUs': 'عن التطبيق',
    'forMoreInfoContactUs': 'للمزيد من المعلومات تواصل معنا :',
    'email': 'البريد الإلكرتوني',
    'phone': 'الهاتف',
    'popularServices': 'خدمة شعبية',
    'services': 'الخدمات',
    'congratulation': 'تهانينا',
    'welcomeToHomePage': 'مرحبا بك في  Vish Vish ',
    'colsultationRequest': 'طلب إستشارة',
    'askForYourAdvice': 'أطلب نصيحتك بسهولة',
    'financialConsulting': 'الإٍستشارات المالية',
    'legalAdvice': 'الإٍستشارات القانونية',
    'faq': 'التعليمات',
    'viewAll': 'عرض الكل',
    'home': 'الرئيسية',
    'myOrders': 'طلباتي',
    'packages': 'المجموعات',
    'languages': 'اللغات',
    'pending': 'معلق',
    'accepted': 'مقبول',
    'oldOrders': 'القديمة',
    'notifications': 'الإشعارات',
    'privacyPolicy': 'سياسة الخصوصية',
    'search': 'بحث',
    'createAnAccount': 'إنشاء حساب',
    'fullName': 'الإسم بالكامل',
    'yourEmail': 'البريد الإلكرتوني',
    'yourPhoneNumber': 'رقم الهاتف',
    'type': 'النوع',
    'user': 'مستخدم',
    'provider': 'مقدم خدمة',
    'company': 'شركة',
    'termsOfService': 'شروط الخدمة',
    'verificationCode': 'تحقق من الكود',
    'pleaseEnterVerificationCode': 'من فضلك أدخل الكود المرسل لهاتفك',
    'resendCode': 'إعادة إرسال الكود ؟',
    'submit': 'إرسال',
    'companyName': 'إسم الشركة',
    'chooseArea': 'إختر المنطقة',
    'area': 'المنطقة',
    'chooseCity': 'إختر المدينة',
    'city': 'المدينة',
    'companyAddress': 'عنوان الشركة',
    'companyNumber': 'رقم الشركة',
    'companyId': 'هوية الشركة',
    'idFile': 'ملف الهوية',
    'companyPicture': 'صورة الشركة',
    'myProfile': 'الصفحة الشخصية',
    'myBooking': 'حجوزاتي',
    'wallet': 'المحفظة',
    'language': 'اللغة',
    'support': 'الدعم',
    'logout': 'تسجيل خروج',
    'termsAndConditions': 'الشروظ والأحكام',
    'details': 'التفاصيل',
    'confirm': 'تأكيد',
    'providerName': 'إسم مقدم الخدمة',
    'providerAddress': 'عنوان مقدم الخدمة',
    'providerNumber': 'رقم الهاتف',
    'providerId': 'هوية مقدم الخدمة',
    'providerPicture': 'صورة مقدم الخدمة',
    'selectedDay': 'اليوم الذي تم إختياره',
    'done': 'تم',
    'address': 'العنوان',
    'requestSentSuccessfully': 'تم إرسال طلبك بنجاح',
    'agreeWithTermsAndPrivacy': 'أوافق على سياسة الخصوصية وشروط الإستخدام',
    'service': 'الخدمة',
    'howManyHours': 'كم عدد الساعات ؟',
    'hour': 'ساعة',
    'whatTime': 'إختر الساعة المناسبة',
    'pay': 'دفع',
    'cancelAllAppointments': 'إلغاء جميع مواعيد الحجز',
    'serviceType': 'نوع الخدمة',
    'frequency': 'التكرار',
    'duration': 'المدة',
    'cleaningMaterial': 'أدوات التنظيف',
    'paymentDetails': 'تفاصيل الدفع',
    'finalPrice': 'الثمن النهائي',
    'deposit20': 'إيداع (٢٠٪)',
    'status': 'الحالة : ',
    'payment': 'الدفع',
    'payTheRestOfTheAmount': 'إدفع باقي المبلغ : ',
    'next': 'التالي',
    'cancel': 'إلغاء',
    'addCard': 'إضافة بطاقة',
    'weAccept': 'نحن نقبل',
    'rememberThisCard': 'تذكر هذه البطاقة',
    'rememberThisCardText':
        'سيقوم Getclean بتطبيق هذه البطاقة بأمان للحصول على تجربة دفع أسرع لن يتم تخزين رقم السيرة الذاتية الخاص بك',
    'paymentCompeletedSuccessfully': 'تم الدفع بنجاح',
    'rateYourProvider': 'قيم مقدم الخدمة',
    'writeComment': 'أكتب تعليق',
    'rate': 'تقييم',
    'yes': 'نعم',
    'no': 'لا',
    'cancelRequestMessage': 'هل أنت متأكد من إلغاء الطلب ؟',
    'ifYouCancelYouLoseMoney': 'إذا ألغيت ، فسوف تخسر أموال الإيداع الخاصة بك',
    'orderCanceledSuccessfully': 'تم إلغاء طلبك بنجاح',
    'cancelThisAppointment': 'إلغاء هذا الموعد',
    'orderDetails': 'تفاصيل الطلب',
    'paymentMethod': 'تفاصيل الدفع',
    'cash': 'نقدي',
    'deposit': 'إيداع',
    'total': 'الإجمالي',
    'pleaseEnterPhoneNumber': 'الرجاء إدخال رقم هاتفك للحصول على رمز التحقق.',
    'phoneNumber': 'رقم الهاتف',
    'didntreceiverotp': 'لم تستلم OTP؟',
    'newPassword': 'كلمة المرور الجديدة',
    'passwordChangedSuccessfully': 'قمت بتغيير كلمة المرور الخاصة بك بنجاح',
    'workingTimes': 'أوقات العمل',
    'holidays': 'الإجازات',
    'makeRequest': 'إرسال طلب',
    'skills': 'المهارات : ',
    'workAreas': 'مناطق العمل : ',
    'salary': 'المرتب : ',
    'days': 'الأيام',
    'open': 'مفتوح',
    'close': 'مغلق',
    'from': 'من',
    'to': 'إلى',
    'reviews': 'التقييمات',
    'changePassword': 'تغيير كلمة المرور',
    'gender': 'الجنس',
    'age': 'العمر',
    'save': 'حفظ',
    'providerPackages': 'حزم مقدم الخدمة',
    'urgentRequest': 'طلب عاجل',
    'today': 'اليوم',
    'iNeedMaterial': 'أحتاج الخامات',
    'howManyTimes': 'كم مرة تحتاج هذه الخدمة؟',
    'onece': 'مرة واحدة',
    'weekly': 'أسبوعي',
    'monthly': 'شهري',
    'note': 'ملاحظات',
    'selectDays': 'حدد يومًا أو أكثر للتنظيف؟',
    'mon': 'الإثنين',
    'tue': 'الثلاثاء',
    'wed': 'الأربعاء',
    'thu': 'الخميس',
    'fri': 'الجمعة',
    'sat': 'السبت',
    'sun': 'الأحد',
    'providerWillContactYou': 'سيتصل بك المزود لتأكيد طلبك',
    'gotoOrderDetialsPage': 'اذهب إلى صفحة تفاصيل طلبك',
    'pleaseEnterFullName': 'من فضلك ادخل اسمك الكامل',
    'pleaseEnterEmail': 'رجاءا أدخل بريدك الإلكتروني',
    'pleaseEnterPhone': 'يرجى إدخال رقم الهاتف الخاص بك',
    'pleaseEnterPassword': 'الرجاء إدخال كلمة المرور',
    'pleaseEnterYourAddress': 'الرجاء إدخال عنوانك',
    'pleaseChooseCity': 'الرجاء اختيار المدينة',
    'pleaseChooseArea': 'الرجاء اختيار المنطقة',
    'pleaseEnterProviderName': 'الرجاء إدخال اسم المزود',
    'pleaseEnterProviderAddress': 'الرجاء إدخال عنوان المزود',
    'pleaseEnterProviderPhoneNumber': 'الرجاء إدخال رقم هاتف المزود',
    'pleaseEnterProviderIdNumber': 'الرجاء إدخال رقم معرف المزود',
    'pleaseEnterChooseProviderImage': 'الرجاء اختيار صورة المزود',
    'pleaseEnterCompanyName': 'الرجاء إدخال اسم الشركة',
    'pleaseEnterCompanyAddress': 'الرجاء إدخال عنوان الشركة',
    'pleaseEnterCompanyPhoneNumber': 'الرجاء إدخال رقم هاتف الشركة',
    'pleaseEnterCompanyIdNumber': 'الرجاء إدخال رقم معرف الشركة',
    'pleaseEnterChooseCompanyImage': 'الرجاء اختيار صورة الشركة',
    'pleaseEnterEmailOrPassword':
        'الرجاء إدخال بريدك الإلكتروني أو كلمة المرور',
    'pleaseInsertCode': 'الرجاء إدخال رمز OTP أولاً',
    'pleaseInsertCorrectCode': 'الرجاء إدخال رمز OTP الصحيح',
    'newPasswordConfirmation': 'تأكيد كلمة المرور الجديدة',
    'pleaseInsertPassword': 'الرجاء إدخال كلمة مرور جديدة',
    'pleaseInsertPasswordConfirmation': 'الرجاء تأكيد كلمة المرور الجديدة',
    'yourBirthDate': 'تاريخ الميلاد',
    'pleaseInsertYourBirthDate': 'الرجاء اختيار تاريخ ميلادك',
    'ok': 'نعم',
    'add': 'إضافة',
    'editSchduel': 'تحرير الجدول',
    'schduelYourOrders': 'رتب جدول مواعيدك',
    'failedToLoad': 'فشل تحميل البيانات يرجى المحاولة مرة أخرى في وقت لاحق',
    'deleteWorkingTime': 'حذف وقت العمل',
    'deleteWorkingTimeMessage': 'هل أنت متأكد من حذف وقت العمل هذا',
    'fromDateHoliday': 'اختر تاريخ بداية العطلة',
    'toDateHoliday': 'اختر تاريخ انتهاء العطلة',
    'deleteHoliday': 'حذف عطلة',
    'deleteHolidayMessage': 'هل أنت متأكد أنك بحاجة إلى حذف هذه العطلة',
    'pleaseChooseDaysFirst': 'الرجاء اختيار الأيام أولاً',
    'pricing': 'التسعير',
    'addYourPriceForEachCategory': 'أضف سعرك لكل فئة',
    'news': 'جديد +',
    'addNewService': 'إضافة خدمة جديدة',
    'materialPrice': 'سعر الخامات',
    'price': 'السعر',
    'sofaType': 'نوع الأريكة',
    'withTax': 'مع الضرائب',
    'withoutTax': 'بدون ضريبة',
    'deleteService': 'حذف الخدمة',
    'deleteServiceMessage': 'هل أنت متأكد من حذف هذه الخدمة؟',
    'offers': 'العروض',
    'myOffers': 'عروضي',
    'bookNow': 'احجز الآن',
    'addOffer': 'إضافة عرض',
    'offerDetails': 'تفاصيل العرض',
    'averageTime': 'متوسط ​​الوقت',
    'description': 'الوصف',
    'deleteOffer': 'حذف العرض',
    'deleteOfferMessage': 'هل أنت متأكد من حذف هذا العرض؟',
    'edit': 'تعديل',
    'delete': 'حذف',
    'isActive': 'نشط',
    'male': 'ذكر',
    'female': 'أنثى',
    'editService': 'تعديل الخدمة',
    'newOffers': 'عروض جديدة',
    'onlyFor': 'فقط بـ ',
    'ils': 'شيكل',
    'providerAvilableTimes': 'الوقت المتاح للمزود',
    'howManyMeters': 'كم متر؟',
    'pleaseWait': 'من فضلك أنتظر',
    'calculatePrice': 'من فضلك انتظر بينما تحسب السعر',
    'approveThisOrder': 'الموافقة على هذا الطلب',
    'orderApprovedSuccessfully': 'تمت الموافقة على الطلب بنجاح',
    'compeleted': 'مكتمل',
    'orderCompeletedSuccessfully': 'تم تنفيذ الطلب بنجاح',
    'meter': 'متر',
    'sendToAll': 'إرسال للجميع',
    'message': 'الرسالة',
    'sofas': 'يرجي اختيار العدد من كل نوع',
    'everyOne': 'الجميع',
    'noAvilableTimes': 'لا توجد أوقات متاحة',
    'submitNewOffer': 'تقديم عرض جديد',
    'trackYourOrder': 'تتبع طلبك',
    'uploadInvoice': 'تحميل الفاتورة',
    'orderSchedule': 'مواعيد الطلب',
    'startsAt': 'يبدأ في',
    'endsAt': 'ينتهي عند',
    'addTip': 'أضف عمولة',
    'payDeposit': 'إيداع الدفع',
    'requestInvoice': 'طلب الفاتورة',
    'amount': 'المبلغ',
    'requestExtraTime': 'اطلب وقتًا إضافيًا',
    'theRequired': 'المطلوب',
    'requestedExtraTime': 'يطلب وقتًا إضافيًا',
    'areYouAccept': 'هل تقبل ؟',
    'accept': 'قبول',
    'reject': 'رفض',
    'showInvoice': 'عرض الفاتورة',
    'date': 'التاريخ',
    'count': 'العدد',
    'bookingTime': 'وقت الححز',
    'noAvilableProviders': 'غير متاح أي مقدم خدمة',
    'tips': 'الإكرامية',
  };
}
