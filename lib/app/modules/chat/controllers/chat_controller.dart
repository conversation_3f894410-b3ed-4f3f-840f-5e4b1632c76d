import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/chat/controllers/states/chat_state.dart';
import 'package:get_clean/global/models/messages_model.dart';
import 'package:get_clean/global/models/user_booking.dart';

import '../../home/<USER>/home_controller.dart';
import '../provider/chat_remote_provider.dart';

class ChatController extends GetxController {
  final BookingData order = BookingData.fromJson(Get.arguments['order']);
  final user = Get.find<HomeController>().user;

  TextEditingController messageController = TextEditingController();

  final chatRemoteProvider = ChatRemoteProvider();
  final chatState = ChatState().obs;
  final chatModel = ChatModel().obs;

  @override
  void onInit() {
    super.onInit();
    getChat();
  }

  void getChat() async {
    chatState.value = ChatLoading();
    update();

    chatState.value = await chatRemoteProvider.getOrderChat(order.id!.toInt());
    update();

    if (chatState.value is GetChatSuccess) {
      chatModel.value = chatState.value.chat!;
      update();
    }
  }

  void sendMessage() async {
    if (messageController.text.isEmpty) {
      return;
    }

    chatState.value = SendMessageLoading();
    update();

    chatState.value = await chatRemoteProvider.sendMessage(
        order.id!.toInt(), messageController.text);
    update();

    if (chatState.value is MessageSentSuccessfully) {
      chatModel.value = chatState.value.chat!;
      messageController.clear();
      update();
    }
  }

  bool isUser() {
    return order.user!.id! == user.value.id!;
  }
}
