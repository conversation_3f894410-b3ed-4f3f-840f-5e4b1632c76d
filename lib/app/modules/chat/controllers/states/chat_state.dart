import 'package:get_clean/global/models/messages_model.dart';

class ChatState {
  ChatModel? chat;
  String? errorMessage;
}

class ChatLoading extends ChatState {}

class SendMessageLoading extends ChatState {}

class GetChatSuccess extends ChatState {
  GetChatSuccess(ChatModel chat) {
    this.chat = chat;
  }
}

class MessageSentSuccessfully extends ChatState {
  MessageSentSuccessfully(ChatModel chat) {
    this.chat = chat;
  }
}

class ChatError extends ChatState {
  ChatError(String errorMessage) {
    this.errorMessage = errorMessage;
  }
}
