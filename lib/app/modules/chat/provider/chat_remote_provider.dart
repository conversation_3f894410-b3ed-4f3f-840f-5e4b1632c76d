import 'dart:developer';

import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/dio/dio_helper.dart';
import 'package:get_clean/global/help_functions/help_functions.dart';
import 'package:get_clean/global/models/messages_model.dart';

import '../controllers/states/chat_state.dart';

class ChatRemoteProvider {
  DioHelper helper = DioHelper();

  Future<ChatState> getOrderChat(int orderId) async {
    try {
      final result = await helper.getData(
        getOrderMessages,
        params: {"order_id": orderId},
      );

      if (result['success'] == true) {
        return GetChatSuccess(ChatModel.fromJson(result));
      } else {
        showErrorToast(result['message']);
        return ChatError(result['message']);
      }
    } catch (e) {
      log(e.toString());
      return ChatError(e.toString());
    }
  }

  Future<ChatState> sendMessage(int orderId, String message) async {
    try {
      final result = await helper.postData(
        sendMessageURL,
        {
          "order_id": orderId,
          "message": message,
        },
      );

      if (result['success'] == true) {
        return MessageSentSuccessfully(ChatModel.fromJson(result));
      } else {
        showErrorToast(result['message']);
        return ChatError(result['message']);
      }
    } catch (e) {
      log(e.toString());
      return ChatError(e.toString());
    }
  }
}
