import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/add_new_service/controllers/add_new_service_controller.dart';
import 'package:get_clean/app/modules/service_providers/controllers/service_providers_controller.dart';
import 'package:get_clean/global/controllers/global_values_controller.dart';
import 'package:get_clean/global/help_functions/help_functions.dart';
import 'package:get_clean/global/models/car_model.dart';
import 'package:get_clean/global/models/coupon_model.dart';
import 'package:get_clean/global/models/price_model.dart';
import 'package:get_clean/global/models/provider_services.dart';
import 'package:get_storage/get_storage.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:intl/intl.dart';

import '../../../../global/controllers/language_controller.dart';
import '../../../../global/models/provider.dart';
import '../../../../global/models/provider_avilable_times.dart';
import '../../../routes/app_pages.dart';
import '../../provider_page/controllers/provider_page_controller.dart';
import '../provider/provider_page_filtered_remote_provider.dart';
import 'states/provider_page_filtered_states.dart';

enum SelectedTimes { once, weekly, monthly }

final selectedTypesSettings = ValueNotifier<List<TypesSettings>>(
  [],
);

class ProviderPageFilteredController extends GetxController {
  final ProviderServices service = (Get.arguments != null
      ? Get.arguments['service'] as ProviderServices
      : ProviderServices());

  final Provider provider = Get.arguments != null
      ? Get.arguments['provider'] as Provider
      : Provider();

  final howManyCloth = Get.arguments != null
      ? (Get.arguments['clothManyCloth'] as Map<int, int>).obs
      : <int, int>{}.obs;

  final selectedDate = Get.arguments != null
      ? (Get.arguments['date'] as DateTime).obs
      : DateTime.now().obs;

  final int duration =
      Get.arguments != null ? Get.arguments['duration'] as int : 2;

  final remoteProvider = ProviderPageFilteredRemoteProvider();
  final state = ProviderPageFilteredState().obs;
  final avilableTimes = ProviderAvilableTimes().obs;
  final price = PriceModel().obs;

  // Coupon related properties
  final appliedCoupon = Rxn<CouponData>();
  final isCouponValidating = false.obs;

  TextEditingController addressController = TextEditingController();
  TextEditingController notesController = TextEditingController();
  TextEditingController howManyMetersController =
      TextEditingController(text: "2");
  TextEditingController couponController = TextEditingController();

  TextEditingController howManyCars = TextEditingController(text: "1");
  GlobalKey<FormState> formKey = GlobalKey<FormState>();

  final howManyHours = 2.obs;
  final howManyTimes = SelectedTimes.once.obs;
  final selectedDays = <int>[0, 0, 0, 0, 0, 0, 0].obs;

  final iNeedMaterials = false.obs;

  var marker = <Marker>{}.obs;

  final ScrollController scrollController = ScrollController();

  void setMarker(Set<Marker> markerData) {
    marker.clear();
    marker.addAll(markerData);

    update();
  }

  void changeSelectedDate(DateTime date) {
    selectedDate.value = date;
    getProviderTimes();
    update();
  }

  final optionsList = <OptionTypes>[].obs;
  List<TextEditingController> pricesList = <TextEditingController>[];

  final weekDays = <String>[
    Get.find<LanguageController>().keys.value.sun!,
    Get.find<LanguageController>().keys.value.mon!,
    Get.find<LanguageController>().keys.value.tue!,
    Get.find<LanguageController>().keys.value.wed!,
    Get.find<LanguageController>().keys.value.thu!,
    Get.find<LanguageController>().keys.value.fri!,
    Get.find<LanguageController>().keys.value.sat!,
  ].obs;

  final choosedWeekDays = <String>[
    'sunday',
    'monday',
    'tuesday',
    'wednesday',
    'thursday',
    'friday',
    'saturday',
  ].obs;

  final selectedTime = "".obs;

  final selectedCarService = CarModel.empty().obs;
  final mainCarModel = CarModel.empty().obs;

  void onChangeSelectedCarService(CarModel car,
      {required CarModel mainCarModel}) {
    selectedCarService.value = car;

    this.mainCarModel.value = mainCarModel;

    howManyCars.text = "1";

    //? request price
    requestPrice();

    update();
  }

  setSelectedTime() {
    selectedTime.value = Get.arguments['selectedTime']?.toString() ?? "";

    update();
  }

  LatLng? getCachedLocalLatLong() {
    final lat = GetStorage().read('lat');
    final long = GetStorage().read('long');

    if (lat == null || long == null) return null;

    return LatLng(lat, long);
  }

  @override
  void onInit() {
    super.onInit();

    if (getCachedLocalLatLong() != null) {
      setMarker({
        Marker(
          markerId: MarkerId(getCachedLocalLatLong().toString()),
          position: getCachedLocalLatLong()!,
          icon: BitmapDescriptor.defaultMarker,
        )
      });
    }

    if (Get.arguments != null) {
      getProviderTimes();

      // if the service has pricing options we generate the list
      if ((service.pricingOption?.hasTypes ?? false) &&
          !isCarService(service.pricingOption!.id!) &&
          !isClothesService(service.pricingOption!.id!)) {
        optionsList.value = service.pricingOption!.optionTypes!;
        pricesList = List.generate(
          optionsList.length,
          (index) => TextEditingController(),
        );
        for (var i = 0; i < pricesList.length; i++) {
          pricesList[i] = TextEditingController(
              text: selectedTypesSettings.value[i].hours?.toString());
        }
        update();
      }

      log('Fasfsa ${duration}');

      howManyMetersController =
          TextEditingController(text: duration.toString());
      howManyHours.value = duration;

      //? navigate scrollController to selected day
      WidgetsBinding.instance.addPostFrameCallback((_) {
        final thisWeekDateTimes = List.generate(
          7,
          (index) => DateTime.now().add(Duration(days: index)),
        );

        final firstDayEqualsToSelectedDay = thisWeekDateTimes.firstWhereOrNull(
          (element) =>
              DateFormat.EEEE().format(element).toLowerCase() ==
              DateFormat.EEEE().format(selectedDate.value).toLowerCase(),
        );

        final indexOfSelectedDay = thisWeekDateTimes.indexOf(
          firstDayEqualsToSelectedDay!,
        );

        scrollController.animateTo(
          indexOfSelectedDay * 120.0,
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeIn,
        );
      });
    }
  }

  @override
  void dispose() {
    super.dispose();

    howManyHours.value = 1;
    howManyTimes.value = SelectedTimes.once;
  }

  void onFormFieldChange(int value, int index) {
    selectedTypesSettings.value[index].hours = value;

    requestPrice();

    update();
  }

  void onSelectedHoursTap(int hours) {
    howManyHours.value = hours;

    update();
  }

  void onChangeSelectedTime(SelectedTimes time) {
    howManyTimes.value = time;
    requestPrice();
    update();
  }

  void changeSelecteTime(String time) {
    selectedTime.value = time;
    requestPrice();
    update();
  }

  void changeSelectedDay(int index) {
    if (selectedDays[index] == 0) {
      selectedDays[index] = 1;
    } else {
      selectedDays[index] = 0;
    }
    requestPrice();
    update();
  }

  void onChangeINeedMaterials(value) {
    iNeedMaterials.value = value;
    requestPrice();
    update();
  }

  void getProviderTimes() async {
    state.value = ProviderPageFilteredTimesLoadingState();
    update();

    state.value = await remoteProvider.getProviderTimes({
      "provider_id": provider.userId,
      'date':
          '${selectedDate.value.year}-${selectedDate.value.month.toString().length == 1 ? '0${selectedDate.value.month}' : selectedDate.value.month}-${selectedDate.value.day.toString().length == 1 ? '0${selectedDate.value.day}' : selectedDate.value.day}',
      "duration": duration,
      "service_id": service?.service?.id,
    });

    update();

    if (state.value is ProviderPageFilteredTimesState) {
      final s = state.value as ProviderPageFilteredTimesState;
      avilableTimes.value = s.times!;
      selectedTime.value = avilableTimes.value.data!.bookingDays!.isEmpty
          ? ""
          : avilableTimes.value.data!.bookingDays![0].bookingTimes![0];

      if (!isCarService(service.pricingOption!.id!)) {
        requestPrice();
      }

      update();

      setSelectedTime();
    } else if (state.value is ProviderPageFilteredFailedState) {
      showErrorToast(state.value.errorMessage!);
    }
  }

  void requestPrice() async {
    state.value = ProviderPageFilteredPriceLoadingState();
    update();

    Map<String, dynamic> data = generateData();

    state.value = await remoteProvider.requestPrice(data);

    update();
    if (state.value is ProviderPageFilteredPriceState) {
      final s = state.value as ProviderPageFilteredPriceState;
      price.value = s.price!;
    } else if (state.value is ProviderPageFilteredFailedState) {
      showErrorToast(state.value.errorMessage!);
    }
  }

  void onChangedHowManyCloth(int value, int index) {
    howManyCloth[index] = value;

    //? request price
    requestPrice();

    update();
  }

  void onSubmitButtonPressed() async {
    if (formKey.currentState!.validate()) {
      state.value = ProviderPageFilteredLoadingState();
      update();

      Map<String, dynamic> data = generateData();

      state.value = await remoteProvider.sendBookingRequest(data);
      update();
      if (state.value is ProviderPageFilteredPriceSuccessState) {
        // final s = state.value as ProviderPageFilteredPriceSuccessState;
        Get.offNamed(
          Routes.REQUEST_SENT_SUCCESSFULLY,

          // arguments: {'order': s.booking!.data!.toJson()},
        );
      } else if (state.value is ProviderPageFilteredFailedState) {
        showErrorToast(state.value.errorMessage!);
      }
    }
  }

  //? on submit offer
  void onSubmitOfferButtonPressed() async {
    if (formKey.currentState?.validate() ?? false) {
      state.value = ProviderPageFilteredLoadingState();
      update();

      Map<String, dynamic> data = {
        "service_id": Get.find<ProviderPageController>().service.service?.id,
        "provider_id": Get.find<ProviderPageController>().provider.userId,
        "user_note": notesController.text,
        "address": addressController.text,
        "user_lat": marker.firstOrNull?.position.latitude,
        "user_long": marker.firstOrNull?.position.longitude,
        "sub_category":
            Get.find<ProviderPageController>().service.id, //TODO-Check-Valid
        "city_id":
            Get.find<ServiceProvidersController>().choosedCity.value.id ??
                Get.find<ProviderPageController>().choosedCity.value.id,
        "district_id":
            Get.find<ServiceProvidersController>().choosedArea.value.id ??
                Get.find<ProviderPageController>().choosedArea.value.id,
        "booked_in": DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now()),
      };

      state.value = await remoteProvider.sendOfferRequest(data);

      update();

      if (state.value is ProviderPageFilteredPriceSuccessState) {
        final s = state.value as ProviderPageFilteredPriceSuccessState;

        log('afsafaffas');
        Get.back();
        Get.offNamed(
          Routes.REQUEST_SENT_SUCCESSFULLY,
        );
      } else if (state.value is ProviderPageFilteredFailedState) {
        showErrorToast(state.value.errorMessage!);
      }
    }
  }

  final isHoursAndMulti =
      Get.find<ProviderPageController>().tabIndex.value == 1 &&
          Get.find<ProviderPageController>().isHours;

  final multiList = Get.find<ProviderPageController>().multiList;

  Map<String, dynamic> generateData() {
    Map<String, dynamic> data = {};
    log('afasfasfsaf ${service.toJson()}');

    _extractAddressData(data);
    _extractDateTimeData(data);
    _extractServiceData(data);
    _extractFrequencyData(data);

    return data;
  }

  void _extractAddressData(Map<String, dynamic> data) {
    data['address'] =
        addressController.text.isEmpty ? 'Address' : addressController.text;

    if (marker.isNotEmpty) {
      final position = marker.firstOrNull?.position;
      data['user_lat'] = position?.latitude;
      data['user_long'] = position?.longitude;

      GetStorage().write('lat', position?.latitude);
      GetStorage().write('long', position?.longitude);
    }
  }

  void _extractDateTimeData(Map<String, dynamic> data) {
    if (isHoursAndMulti) {
      for (var i = 0; i < multiList.length; i++) {
        final date = multiList[i].selectedDate?.value;
        data['date[$i]'] =
            '${date?.year}-${date?.month.toString().padLeft(2, '0')}-${date?.day.toString().padLeft(2, '0')}';
        data['time[$i]'] = multiList[i].selectedTime?.value ?? '';
      }
    } else {
      final date = selectedDate.value;
      data['date[]'] =
          '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
      data['time[]'] = selectedTime.value;
      data['duration'] = duration;
    }
  }

  void _extractDefaultServiceData(Map<String, dynamic> data) {
    if (isHoursAndMulti) {
      for (var i = 0; i < multiList.length; i++) {
        data['quantity[$i]'] = multiList[i].howManyHours?.value;
      }
    } else {
      data['quantity[]'] = service?.pricingOption?.name == 'Meter' ||
              service?.pricingOption!.name == 'متر'
          ? int.tryParse(howManyMetersController.text) ?? 1
          : howManyHours.value;
    }
  }

  void _extractServiceData(Map<String, dynamic> data) {
    _extractBasicServiceData(data);

    if (service.pricingOption?.hasTypes ?? false) {
      data['frequency'] = 'once';

      if (isCarService(service?.pricingOption!.id!)) {
        _extractCarServiceData(data);
      } else {
        _extractOtherServiceData(data);
      }

      if (isClothesService(service?.pricingOption!.id!)) {
        _extractClothesServiceData(data);
      }
    } else {
      _extractDefaultServiceData(data);
    }
  }

  void _extractBasicServiceData(Map<String, dynamic> data) {
    data['provider_id'] = provider.userId;
    data['service_id'] = service.service?.id;
    data['city_id'] =
        Get.find<ServiceProvidersController>().choosedCity.value.id ??
            Get.find<ProviderPageController>().choosedCity.value.id;
    data['need_materials'] = iNeedMaterials.value ? 1 : 0;
    data['note'] = notesController.text;

    // Add coupon code if applied
    if (appliedCoupon.value != null) {
      data['coupon_code'] = appliedCoupon.value!.code;
      data['coupon_id'] = appliedCoupon.value!.id;
    }
  }

  void _extractCarServiceData(Map<String, dynamic> data) {
    data['quantity[]'] = howManyCars.text;
    data['sub_category[]'] = selectedCarService.value.id;
  }

  void _extractOtherServiceData(Map<String, dynamic> data) {
    final globalController = Get.find<GlobalValuesController>();

    final filteredOptions =
        selectedTypesSettings.value.map((option) => option.hours ?? 0).toList();

    data['quantity[]'] = filteredOptions;
    data['sub_category[]'] = optionsList.map((option) => option.id).toList();
  }

  void _extractClothesServiceData(Map<String, dynamic> data) {
    data['quantity[]'] = howManyCloth.values.toList();
    data['sub_category[]'] = howManyCloth.keys.toList();
    data['deliver'] = iNeedMaterials.value ? 1 : 0;
  }

  void _extractFrequencyData(Map<String, dynamic> data) {
    data['frequency'] = howManyTimes.value.toString().split('.').last;

    if (howManyTimes.value != SelectedTimes.once) {
      data['days'] = selectedDays
          .asMap()
          .entries
          .where((entry) => entry.value == 1)
          .map((entry) => choosedWeekDays[entry.key])
          .join(',');
    }
  }

  // Coupon validation method
  void validateAndApplyCoupon() async {
    if (couponController.text.trim().isEmpty) {
      showErrorToast('Please enter coupon code');
      return;
    }

    isCouponValidating.value = true;
    update();

    try {
      final couponResult =
          await remoteProvider.validateCoupon(couponController.text.trim());

      if (couponResult?.success == true && couponResult?.data != null) {
        appliedCoupon.value = couponResult!.data;
        showSuccessToast(couponResult.message ?? 'Coupon applied successfully');

        // Request new pricing with coupon
        requestPrice();
      } else {
        showErrorToast(couponResult?.message ?? 'Invalid coupon code');
      }
    } catch (e) {
      showErrorToast('Error validating coupon');
    } finally {
      isCouponValidating.value = false;
      update();
    }
  }

  // Remove applied coupon
  void removeCoupon() {
    appliedCoupon.value = null;
    couponController.clear();
    requestPrice();
    update();
  }
}
