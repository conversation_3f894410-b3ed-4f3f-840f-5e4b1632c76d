import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/provider_page/controllers/provider_page_controller.dart';
import 'package:get_clean/app/modules/provider_page_filtered/controllers/provider_page_filtered_controller.dart';
import 'package:get_clean/app/modules/provider_page_filtered/controllers/states/provider_page_filtered_states.dart';
import 'package:intl/intl.dart';

import '../../../../../global/constants/constants.dart';
import '../../../../../global/constants/theme.dart';
import '../../../../../global/controllers/language_controller.dart';

class ChooseTime extends StatelessWidget {
  const ChooseTime({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ProviderPageFilteredController>(builder: (controller) {
      //! Get working hours
      final currentWorkingDay = controller.provider.workingTime
          ?.firstWhereOrNull((element) =>
              element.day ==
              DateFormat('EEEE').format(DateTime.now()).toLowerCase());

      //! Get End Time
      final endsAt = currentWorkingDay?.endsAt;

      final bookingDays = controller.avilableTimes.value.data?.bookingDays;

      //! Filter booking times
      final filteredBookingTimes =
          bookedTimes(bookingDays: bookingDays, endsAt: endsAt);

      log('FilterTimes ${filteredBookingTimes} EndsAt ${endsAt}');

      if (controller.state.value is ProviderPageFilteredTimesLoadingState) {
        return Center(
          child: Text(
            Get.find<LanguageController>().keys.value.pleaseWait!,
            style: regularBlackTextStyle,
          ),
        );
      }
      return SizedBox(
        height: 100,
        child: controller.avilableTimes.value.data == null
            ? Center(
                child: Text(
                  Get.find<LanguageController>().keys.value.noAvilableTimes!,
                  style: big2TextStyle,
                ),
              )
            : controller.avilableTimes.value.data!.bookingDays!.isEmpty
                ? Center(
                    child: Text(
                      Get.find<LanguageController>()
                          .keys
                          .value
                          .noAvilableTimes!,
                      style: big2TextStyle,
                    ),
                  )
                : ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemBuilder: (context, index) {
                      return InkWell(
                        onTap: () => controller.changeSelecteTime(
                          controller.avilableTimes.value.data!.bookingDays![0]
                              .bookingTimes![index],
                        ),
                        child: Container(
                          width: 120,
                          alignment: Alignment.center,
                          padding: const EdgeInsets.all(10),
                          margin: const EdgeInsets.all(5),
                          decoration: BoxDecoration(
                            border: Border.all(color: primaryColor),
                            borderRadius: BorderRadius.circular(10),
                            color: controller.selectedTime.value ==
                                    controller.avilableTimes.value.data!
                                        .bookingDays![0].bookingTimes![index]
                                ? primaryColor
                                : Colors.white,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                filteredBookingTimes![index],
                                style: controller.selectedTime.value ==
                                        filteredBookingTimes![index]
                                    ? regularWhiteTextStyle
                                    : regularTextStyle,
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                    itemCount: controller
                            .avilableTimes.value.data!.bookingDays!.isEmpty
                        ? 0
                        : filteredBookingTimes.length,
                  ),
      );
    });
  }
}
