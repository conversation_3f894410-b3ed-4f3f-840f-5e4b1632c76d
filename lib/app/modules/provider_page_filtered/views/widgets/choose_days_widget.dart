import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/provider_page_filtered/controllers/provider_page_filtered_controller.dart';
import '../../../../../global/constants/constants.dart';
import '../../../../../global/constants/theme.dart';
import '../../../../../global/controllers/language_controller.dart';

class HowManyDaysWidget extends StatelessWidget {
  const HowManyDaysWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ProviderPageFilteredController>(builder: (controller) {
      return Container(
        decoration: BoxDecoration(
          border: Border.all(
            color: primaryColor,
            width: 0.5,
          ),
          borderRadius: BorderRadius.circular(15),
          color: Colors.white,
        ),
        height: 50,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            for (int i = 0; i < 7; i++)
              Expanded(
                child: InkWell(
                  onTap: () => controller.changeSelectedDay(i),
                  child: Container(
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      borderRadius: i == 6
                          ? BorderRadius.only(
                              topRight: Get.find<LanguageController>().isArabic
                                  ? const Radius.circular(0)
                                  : const Radius.circular(15),
                              topLeft: Get.find<LanguageController>().isArabic
                                  ? const Radius.circular(15)
                                  : const Radius.circular(0),
                              bottomLeft:
                                  Get.find<LanguageController>().isArabic
                                      ? const Radius.circular(15)
                                      : const Radius.circular(0),
                              bottomRight:
                                  Get.find<LanguageController>().isArabic
                                      ? const Radius.circular(0)
                                      : const Radius.circular(15),
                            )
                          : i == 0
                              ? BorderRadius.only(
                                  bottomLeft:
                                      Get.find<LanguageController>().isArabic
                                          ? const Radius.circular(0)
                                          : const Radius.circular(15),
                                  topLeft:
                                      Get.find<LanguageController>().isArabic
                                          ? const Radius.circular(0)
                                          : const Radius.circular(15),
                                  topRight:
                                      Get.find<LanguageController>().isArabic
                                          ? const Radius.circular(15)
                                          : const Radius.circular(0),
                                  bottomRight:
                                      Get.find<LanguageController>().isArabic
                                          ? const Radius.circular(15)
                                          : const Radius.circular(0),
                                )
                              : BorderRadius.circular(0),
                      color: controller.selectedDays[i] == 1
                          ? primaryColor
                          : Colors.white,
                    ),
                    padding: const EdgeInsets.all(6),
                    child: Text(
                      controller.weekDays[i],
                      style: controller.selectedDays[i] == 1
                          ? middleWhiteTextStyle
                          : middleTextStyle,
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              ),
          ],
        ),
      );
    });
  }
}
