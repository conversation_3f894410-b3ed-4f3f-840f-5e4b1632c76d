import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/provider_page_filtered/controllers/provider_page_filtered_controller.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/controllers/global_values_controller.dart';
import 'package:get_clean/global/controllers/language_controller.dart';
import 'package:get_clean/global/models/provider_services.dart';
import 'package:get_clean/global/widget/text_with_background.dart';

import '../../../add_new_service/views/widgets/add_service_form_field.dart';

List<OptionTypes> allServicesTypes(int? serviceId) {
  final globalController = Get.find<GlobalValuesController>();

  final allServices = globalController.allServices.value;

  final allServicesWithService = allServices.data!
          .firstWhereOrNull((element) => element.service?.id == serviceId)
          ?.service
          ?.pricingOption
          ?.optionTypes ??
      [];

  // log('afdaff ${allServices.data!.firstWhereOrNull((element) => element.service?.id == serviceId)?.service?.pricingOption}');

  return allServicesWithService;
}

class OrderSofaWidget extends GetView<ProviderPageFilteredController> {
  const OrderSofaWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final services = controller.provider.services
            ?.firstWhereOrNull(
                (element) => element.id == controller?.service?.id)!
            .pricingList
            ?.where((element) => element.price != null && element.price != 0)
            .toList() ??
        [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(10),
          margin: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(15),
            color: const Color(0xffF3F3F3),
          ),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  children: [
                    TextWithBackground(
                        color: primaryColor,
                        text: Get.find<LanguageController>().keys.value.type!
                        // .sofaType!,//todo-sofa
                        ),
                    // for (int i = 0;
                    //     i < filteredAllServicesWithOnlyPrices.length;
                    //     i++)
                    //   AddServiceFormField(
                    //     keyboardType: TextInputType.text,
                    //     controller: TextEditingController(
                    //         text: filteredAllServicesWithOnlyPrices[i]?.name),
                    //     active: false,
                    //   ),
                    for (int i = 0; i < services.length; i++)
                      AddServiceFormField(
                        keyboardType: TextInputType.text,
                        controller: services[i].typeModel?.name,
                        active: false,
                      ),
                    //
                  ],
                ),
              ),
              SizedBox(width: 10.w),
              Expanded(
                child: Column(
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: TextWithBackground(
                            color: primaryColor,
                            text: Get.find<LanguageController>()
                                .keys
                                .value
                                .count!,
                          ),
                        ),
                      ],
                    ),
                    for (int i = 0;
                        i < allServicesTypes(controller.service.id).length;
                        i++)
                      if (services.any((element) =>
                          element.typeModel?.id ==
                          allServicesTypes(controller.service.id)[i].id))
                        Row(
                          children: [
                            Expanded(
                              child: AddServiceFormField(
                                  keyboardType: TextInputType.text,
                                  controller: controller.pricesList[i].text ==
                                              '0' ||
                                          controller.pricesList[i].text == ''
                                      ? TextEditingController()
                                      : controller.pricesList[i],
                                  active: true,
                                  onChanged: (value) =>
                                      controller.onFormFieldChange(
                                          int.tryParse(value) ?? 0, i)),
                            ),
                          ],
                        ),
                    for (int i = 0; i < services.length; i++)
                      Row(
                        children: [
                          Expanded(
                            child: AddServiceFormField(
                                keyboardType: TextInputType.text,
                                controller: controller.pricesList[i],
                                active: true,
                                onChanged: (value) =>
                                    controller.onFormFieldChange(
                                        int.tryParse(value) ?? 1, i)),
                          ),
                        ],
                      )
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
