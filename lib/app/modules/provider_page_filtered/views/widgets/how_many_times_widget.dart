import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../../global/constants/constants.dart';
import '../../../../../global/constants/theme.dart';
import '../../../../../global/controllers/language_controller.dart';
import '../../controllers/provider_page_filtered_controller.dart';

class HowManyTimesWidget extends StatelessWidget {
  const HowManyTimesWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ProviderPageFilteredController>(builder: (controller) {
      return Container(
        decoration: BoxDecoration(
          border: Border.all(
            color: primaryColor,
            width: 0.5,
          ),
          borderRadius: BorderRadius.circular(25),
          color: Colors.white,
        ),
        child: Column(
          children: [
            Container(
              decoration: BoxDecoration(
                border: Border.all(
                  color: primaryColor,
                  width: 0.5,
                ),
                borderRadius: const BorderRadius.only(
                  topRight: Radius.circular(25),
                  topLeft: Radius.circular(25),
                ),
                color: primaryColor,
              ),
              alignment: Alignment.center,
              child: Text(
                Get.find<LanguageController>().keys.value.howManyHours!,
                style: regularWhiteTextStyle,
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Expanded(
                  child: InkWell(
                    onTap: () =>
                        controller.onChangeSelectedTime(SelectedTimes.once),
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.only(
                          bottomLeft: Get.find<LanguageController>().isArabic
                              ? const Radius.circular(0)
                              : const Radius.circular(25),
                          bottomRight: Get.find<LanguageController>().isArabic
                              ? const Radius.circular(25)
                              : const Radius.circular(0),
                        ),
                        color: Colors.white,
                      ),
                      padding: const EdgeInsets.all(6),
                      child: Column(
                        children: [
                          Opacity(
                            opacity: controller.howManyTimes.value ==
                                    SelectedTimes.once
                                ? 1
                                : 0.5,
                            child: Image.asset(
                              'assets/images/once.png',
                            ),
                          ),
                          Text(
                            Get.find<LanguageController>().keys.value.onece!,
                            style: controller.howManyTimes.value ==
                                    SelectedTimes.once
                                ? big2TextStyle
                                : TextStyle(
                                    color: primaryColor.withOpacity(0.5),
                                    fontSize: 20,
                                  ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: InkWell(
                    onTap: () =>
                        controller.onChangeSelectedTime(SelectedTimes.weekly),
                    child: Container(
                      decoration: const BoxDecoration(
                        color: Colors.white,
                      ),
                      padding: const EdgeInsets.all(6),
                      child: Column(
                        children: [
                          Opacity(
                            opacity: controller.howManyTimes.value ==
                                    SelectedTimes.weekly
                                ? 1
                                : 0.5,
                            child: Image.asset(
                              'assets/images/week.png',
                            ),
                          ),
                          Text(
                            Get.find<LanguageController>().keys.value.weekly!,
                            style: controller.howManyTimes.value ==
                                    SelectedTimes.weekly
                                ? big2TextStyle
                                : TextStyle(
                                    color: primaryColor.withOpacity(0.5),
                                    fontSize: 20,
                                  ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: InkWell(
                    onTap: () =>
                        controller.onChangeSelectedTime(SelectedTimes.monthly),
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.only(
                          bottomLeft: Get.find<LanguageController>().isArabic
                              ? const Radius.circular(25)
                              : const Radius.circular(0),
                          bottomRight: Get.find<LanguageController>().isArabic
                              ? const Radius.circular(0)
                              : const Radius.circular(25),
                        ),
                        color: Colors.white,
                      ),
                      padding: const EdgeInsets.all(6),
                      child: Column(
                        children: [
                          Opacity(
                            opacity: controller.howManyTimes.value ==
                                    SelectedTimes.monthly
                                ? 1
                                : 0.5,
                            child: Image.asset(
                              'assets/images/month.png',
                            ),
                          ),
                          Text(
                            Get.find<LanguageController>().keys.value.monthly!,
                            style: controller.howManyTimes.value ==
                                    SelectedTimes.monthly
                                ? big2TextStyle
                                : TextStyle(
                                    color: primaryColor.withOpacity(0.5),
                                    fontSize: 20,
                                  ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    });
  }
}
