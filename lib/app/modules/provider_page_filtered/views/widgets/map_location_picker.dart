import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/controllers/language_controller.dart';
import 'package:get_clean/global/help_functions/help_functions.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class MapLocationPicker extends StatefulWidget {
  final Function onSave;
  final Set<Marker>? selectedMarkers;
  final bool viewOnly;
  final bool hideControls;

  const MapLocationPicker({
    super.key,
    required this.onSave,
    this.selectedMarkers,
    this.viewOnly = false,
    this.hideControls = false,
  });

  @override
  _OpenMap createState() => _OpenMap();
}

class _OpenMap extends State<MapLocationPicker> with TickerProviderStateMixin {
  final markers = <Marker>{};
  GoogleMapController? myMapController;
  final Set<Marker> _markers = {};
  static const defaultLocation = LatLng(31.9522, 35.2332);

  LatLng _mainLocation = defaultLocation;

  Set<Marker> myMarker() {
    setState(() {
      _markers.add(Marker(
        markerId: MarkerId(_mainLocation.toString()),
        position: _mainLocation,
        icon: BitmapDescriptor.defaultMarker,
      ));
    });

    return _markers;
  }

  // Future<bool> getPermission() async {
  //   return await Permission.location.request().isGranted;
  // }

  @override
  void initState() {
    super.initState();

    // getPermission().then((value) =>
    //     value ? Navigator.of(context).pop() : Navigator.of(context).pop());

    setState(() {
      _mainLocation = widget.selectedMarkers != null
          ? (widget.selectedMarkers?.firstOrNull?.position ?? defaultLocation)
          : defaultLocation;
    });

    _markers.add(
      Marker(
        markerId: MarkerId(_mainLocation.toString()),
        position: _mainLocation,
        icon: BitmapDescriptor.defaultMarker,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Stack(
          children: [
            SizedBox(
              height: MediaQuery.of(context).size.height,
              width: MediaQuery.of(context).size.width,
              child: GoogleMap(
                zoomControlsEnabled: !widget.viewOnly,
                initialCameraPosition: CameraPosition(
                  target: _mainLocation,
                  zoom: 15.0,
                ),
                scrollGesturesEnabled: !widget.hideControls,
                zoomGesturesEnabled: !widget.hideControls,
                markers: Set<Marker>.of(_markers),
                mapType: MapType.normal,
                onMapCreated: (controller) {
                  setState(() {
                    myMapController = controller;
                  });
                },
                onTap: (LatLng latLng) async {
                  if (widget.viewOnly) return;
                  _markers.clear();
                  _markers.add(Marker(
                      markerId: const MarkerId('mark'), position: latLng));
                  setState(() {});
                },
              ),
            ),
            if (!widget.hideControls)
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  IconButton(
                    onPressed: Get.back,
                    icon: const Icon(
                      CupertinoIcons.back,
                      size: 30,
                    ),
                  ),
                ],
              ),

            //? My location floating button
            if (!widget.hideControls)
              Container(
                alignment: Alignment.topRight,
                padding: const EdgeInsets.only(
                    right: 10, left: 10, bottom: 15, top: 10),
                child: GestureDetector(
                  onTap: () async {
                    //! Ask for permission
                    await Geolocator.requestPermission();

                    //! Get current location
                    final position = await Geolocator.getCurrentPosition(
                        desiredAccuracy: LocationAccuracy.medium);

                    //! Move camera to current location
                    myMapController!.animateCamera(
                        CameraUpdate.newCameraPosition(CameraPosition(
                            target:
                                LatLng(position.latitude, position.longitude),
                            zoom: 15.0)));

                    //! Set marker
                    setState(() {
                      _markers.clear();
                      _markers.add(Marker(
                          markerId: const MarkerId('mark'),
                          position:
                              LatLng(position.latitude, position.longitude)));
                    });
                  },
                  child: Container(
                    height: 50,
                    width: 50,
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(5)),
                    child: const Icon(
                      Icons.my_location,
                      color: primaryColor,
                    ),
                  ),
                ),
              ),

            if (!widget.viewOnly)
              Container(
                alignment: Alignment.bottomCenter,
                padding: const EdgeInsets.only(
                    right: 10, left: 10, bottom: 15, top: 10),
                child: GestureDetector(
                  onTap: () async {
                    Navigator.pop(context);

                    widget.onSave(_markers);

                    showSuccessToast(Get.find<LanguageController>()
                            .keys
                            .value
                            .locationSavedSuccessfully ??
                        'Location saved successfully');
                  },
                  child: Container(
                    height: 50,
                    width: MediaQuery.of(context).size.width,
                    decoration: BoxDecoration(
                        color: primaryColor,
                        borderRadius: BorderRadius.circular(5)),
                    child: Container(
                      padding: const EdgeInsets.all(10),
                      child: Center(
                        child: Text(
                          Get.find<LanguageController>().keys.value.save!,
                          style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 18),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
