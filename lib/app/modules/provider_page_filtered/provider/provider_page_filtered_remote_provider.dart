import 'dart:convert';
import 'dart:developer';

import 'package:get_clean/app/modules/provider_page_filtered/controllers/states/provider_page_filtered_states.dart';
import 'package:get_clean/global/dio/dio_helper.dart';
import 'package:get_clean/global/models/coupon_model.dart';
import 'package:get_clean/global/models/price_model.dart';
import 'package:get_clean/global/models/provider_avilable_times.dart';

import '../../../../global/constants/constants.dart';
import '../../../../global/models/booking_response.dart';

class ProviderPageFilteredRemoteProvider {
  DioHelper helper = DioHelper();

  Future<ProviderPageFilteredState> sendBookingRequest(data) async {
    try {
      final response = await helper.postData(bookNewServiceURL, data);
      log(jsonEncode(response));
      if (response['success'] == true) {
        return ProviderPageFilteredPriceSuccessState(
          BookingResponse.fromJson({}),
        );
      } else {
        return ProviderPageFilteredFailedState(response['message']);
      }
    } catch (e) {
      return ProviderPageFilteredFailedState(e.toString());
    }
  }

  Future<ProviderPageFilteredState> sendOfferRequest(data) async {
    try {
      final response = await helper.postData(requestOfferURL, data);
      log('DDDADAD ${jsonEncode(response)}');
      if (response['success'] == true) {
        return ProviderPageFilteredPriceSuccessState(
          BookingResponse.fromJson({}),
        );
      } else {
        return ProviderPageFilteredFailedState(response['message']);
      }
    } catch (e) {
      return ProviderPageFilteredFailedState(e.toString());
    }
  }

  Future<ProviderPageFilteredState> getProviderTimes(data) async {
    try {
      log('Params ${data}');

      final response = await helper.postData(getProviderTimesURL, data);

      log("Responseeeee ${response}");
      if (response['success'] == true) {
        return ProviderPageFilteredTimesState(
          ProviderAvilableTimes.fromJson(response),
        );
      } else {
        return ProviderPageFilteredFailedState(response['message']);
      }
    } catch (e, s) {
      log('ErrorWhileRequestPrice222 ${e.toString()} $s');
      return ProviderPageFilteredFailedState(e.toString());
    }
  }

  Future<ProviderPageFilteredState> requestPrice(data) async {
    try {
      final response = await helper.postData(requestPriceURL, data);
      log("Params: $data");
      log("Responseeeerree $response");
      if (response['data'] != null) {
        return ProviderPageFilteredPriceState(
          PriceModel.fromJson(response),
        );
      } else {
        log('Message: ${response['message']}}');
        return ProviderPageFilteredFailedState(response['message']);
      }
    } catch (e, s) {
      log('ErrorWhileRequestPrice ${e.toString()} $s');

      return ProviderPageFilteredFailedState(e.toString());
    }
  }

  Future<CouponModel?> validateCoupon(String code) async {
    try {
      final response = await helper.postData(validateCouponURL, {"code": code});
      log("Coupon validation response: $response");

      if (response['success'] == true) {
        return CouponModel.fromJson(response);
      } else {
        return CouponModel(
          success: false,
          message: response['message'] ?? 'Invalid coupon code',
        );
      }
    } catch (e) {
      log('Error validating coupon: ${e.toString()}');
      return CouponModel(
        success: false,
        message: 'Error validating coupon',
      );
    }
  }
}
