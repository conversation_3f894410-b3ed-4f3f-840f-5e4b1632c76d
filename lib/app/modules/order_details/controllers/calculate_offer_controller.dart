import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_clean/global/models/user_booking.dart';

class CalculateOfferController extends GetxController {
  BookingData bookingData =
      BookingData.fromJson(Get.arguments != null ? Get.arguments['order'] : {});

  // * Text Controllers (Price, App Commission, Tax, Total)
  final priceController = TextEditingController();
  final appCommissionController = TextEditingController();
  final taxController = TextEditingController();
  final totalController = TextEditingController();
  final startDateTimeController = TextEditingController();
  final endDateTimeController = TextEditingController();

  @override
  void onInit() {
    super.onInit();

    appCommissionController.text =
        bookingData.provider?.commission?.toString() ?? '0';

    taxController.text = bookingData.provider?.tax?.toString() ?? '0';
  }

  onChangedPrice() {
    if (priceController.text.isEmpty) {
      appCommissionController.text = '0';
      taxController.text = '0';
      totalController.text = '0';
      return;
    }

    // * Calculate App Commission ========================
    num price = num.parse(priceController.text);
    final commission = bookingData.provider?.commission ?? 0;

    final appCommissionValue = price * (commission / 100);

    appCommissionController.text = appCommissionValue.toStringAsFixed(2);

    // * Calculate Tax ===================================
    final tax = bookingData.provider?.tax ?? 0;
    final taxValue = (price + appCommissionValue) * (tax / 100);

    taxController.text = taxValue.toStringAsFixed(2);

    // * Calculate Total =================================
    final totalValue = price + appCommissionValue + taxValue;

    totalController.text = totalValue.toStringAsFixed(2);
  }

  onChangedTotal() {
    if (totalController.text.isEmpty) {
      priceController.text = '0';
      appCommissionController.text = '0';
      taxController.text = '0';
      return;
    }

    // * Calculate Tax ===================================
    num total = num.parse(totalController.text);
    final tax = bookingData.provider?.tax ?? 0;

    final taxValue = total - (total / (1 + (tax / 100)));

    taxController.text = taxValue.toStringAsFixed(2);

    // * Calculate App Commission ========================
    final commission = bookingData.provider?.commission ?? 0;

    final appCommissionValue =
        (total - taxValue) - (total - taxValue) / (1 + (commission / 100));

    appCommissionController.text = appCommissionValue.toStringAsFixed(2);

    // * Calculate Price =================================
    final priceValue = total - taxValue - appCommissionValue;

    priceController.text = priceValue.toStringAsFixed(2);
  }
}
