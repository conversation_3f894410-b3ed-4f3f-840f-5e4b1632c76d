import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/order_details/controllers/calculate_offer_controller.dart';
import 'package:get_clean/app/modules/order_details/controllers/order_details_controller.dart';
import 'package:get_clean/global/controllers/language_controller.dart';
import 'package:get_clean/global/widget/custom_button.dart';
import 'package:get_clean/global/widget/custom_form_field.dart';
import 'package:get_clean/global/widget/date_time_picker_widget.dart';

class CalculateOfferPage extends GetView<CalculateOfferController> {
  const CalculateOfferPage({super.key});

  @override
  Widget build(BuildContext context) {
    // log('assasad ${controller.bookingData.toJson()}');
    return Scaffold(
      appBar: AppBar(
        title: Text(Get.find<LanguageController>().keys.value.calculateOffer!),
        centerTitle: true,
      ),
      body: ListView(
        children: [
          Column(
            children: [
              CustomFormField(
                hint: Get.find<LanguageController>().keys.value.price!,
                label: Get.find<LanguageController>().keys.value.price!,
                controller: controller.priceController,
                keyboardType: TextInputType.number,
                onChanged: (val) => controller.onChangedPrice(),
              ),
              CustomFormField(
                hint: Get.find<LanguageController>().keys.value.appCommission!,
                enabled: false,
                label: Get.find<LanguageController>().keys.value.appCommission!,
                controller: controller.appCommissionController,
                keyboardType: TextInputType.number,
              ),
              CustomFormField(
                hint: Get.find<LanguageController>().keys.value.tax,
                label: Get.find<LanguageController>().keys.value.tax,
                controller: controller.taxController,
                keyboardType: TextInputType.number,
                enabled: false,
              ),

              CustomFormField(
                hint: Get.find<LanguageController>().keys.value.total!,
                label: Get.find<LanguageController>().keys.value.total!,
                controller: controller.totalController,
                onChanged: (val) => controller.onChangedTotal(),
                keyboardType: TextInputType.number,
              ),

              const SizedBox(
                height: 5,
              ),

              //? Start Date Time & End Date Time
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Expanded(
                      child: DateTimePickerWidget(
                    controller: controller.startDateTimeController,
                    hint: Get.find<LanguageController>().keys.value.startDate!,
                    label: Get.find<LanguageController>().keys.value.startDate!,
                  )),
                  Expanded(
                    child: DateTimePickerWidget(
                      controller: controller.endDateTimeController,
                      hint: Get.find<LanguageController>().keys.value.endDate!,
                      label: Get.find<LanguageController>().keys.value.endDate!,
                    ),
                  ),
                ],
              ),

              const SizedBox(
                height: 25,
              ),

              //! Approve Button
              CustomButton(
                label: Get.find<LanguageController>().keys.value.submit!,
                onTap: () {
                  Get.find<OrderDetailsController>().approveOfferAsProvider(
                      controller.bookingData.id!,
                      tax: controller.taxController.text,
                      commission: controller.appCommissionController.text,
                      totalPrice: controller.totalController.text,
                      commissionPercentage: controller
                              .bookingData.provider?.commission
                              ?.toString() ??
                          '',
                      startDate: controller.startDateTimeController.text,
                      endDate: controller.endDateTimeController.text);
                },
                height: 45.h,
                width: 300.w,
              ),
            ],
          )
        ],
      ),
    );
  }
}
