import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/order_details/controllers/order_details_controller.dart';
import 'package:get_clean/global/widget/custom_button.dart';

import '../../../../../global/constants/constants.dart';
import '../../../../../global/constants/theme.dart';
import '../../../../../global/controllers/language_controller.dart';
import '../../../add_new_service/views/widgets/add_service_form_field.dart';

class EditOrdersHowManyClothes extends StatelessWidget {
  const EditOrdersHowManyClothes({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<OrderDetailsController>(builder: (controller) {
      return HookBuilder(builder: (context) {
        //! To Get Only The Services That Have Price From The Provider
        // final services = useState(controller.bookingData.orderData!.types
        //         ?.where(
        //             (element) => element.amount != null && element.amount != 0)
        //         .toList() ??
        //     []);

        return StatefulBuilder(builder: (context, setState) {
          return Container(
            decoration: const BoxDecoration(
              borderRadius: BorderRadius.only(
                topRight: Radius.circular(25),
                topLeft: Radius.circular(25),
              ),
              color: Colors.white,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Container(
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: primaryColor,
                      width: 0.5,
                    ),
                    borderRadius: const BorderRadius.only(
                      topRight: Radius.circular(25),
                      topLeft: Radius.circular(25),
                    ),
                    color: primaryColor,
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    Get.find<LanguageController>().keys.value.sofas!,
                    style: regularWhiteTextStyle,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: Row(
                    children: [
                      Expanded(
                        child: Column(
                          children: [
                            for (int i = 0; i < controller.services.length; i++)
                              AddServiceFormField(
                                keyboardType: TextInputType.text,
                                initialValue: controller.services[i].$2 ?? '',
                                active: false,
                              )
                          ],
                        ),
                      ),
                      SizedBox(width: 10.w),
                      // Expanded(
                      //   child: Column(
                      //     children: [
                      //       for (int i = 0; i < controller.services.length; i++)
                      //         AddServiceFormField(
                      //           initialValue: controller
                      //               .howManyCloth[services[i].type?.id]
                      //               .toString(),
                      //           keyboardType: TextInputType.number,
                      //           onChanged: (value) =>
                      //               controller.onChangedHowManyCloth(
                      //                   int.tryParse(value) ?? 1,
                      //                   services[i].type?.id ?? 0),
                      //           active: true,
                      //         ),
                      //     ],
                      //   ),
                      // ),
                      Expanded(
                        child: Column(
                          children:
                              controller.services.value.indexed.map((service) {
                            log('afasfasfa ${service.$2.$1}');
                            return Row(
                              children: [
                                Expanded(
                                  child: AddServiceFormField(
                                    initialValue: controller
                                        .howManyCloth[service.$2.$1 ?? 0]
                                        ?.toString(),
                                    keyboardType: TextInputType.number,
                                    onChanged: (value) =>
                                        controller.onChangedHowManyCloth(
                                            int.tryParse(value) ?? 1,
                                            service.$2.$1),
                                    active: true,
                                  ),
                                ),
                                if (service.$1 !=
                                    0) // Do not show remove button for the first row
                                  IconButton(
                                    icon: const Icon(Icons.remove),
                                    onPressed: () {
                                      controller.services.value
                                          .removeAt(service.$1);

                                      setState(() {});
                                    },
                                  ),
                              ],
                            );
                          }).toList(),
                        ),
                      ),
                    ],
                  ),
                ),

                TextButton(
                  onPressed: () {
                    final providerServices = controller
                            .bookingData.provider?.services!
                            .firstWhereOrNull((element) => element.id == 24)!
                            .pricingList!
                            .where((element) =>
                                element.price != null && element.price != 0)
                            .toList() ??
                        [];

                    showDialog(
                      context: context,
                      builder: (BuildContext context) {
                        return AlertDialog(
                          title: Text(
                              Get.find<LanguageController>().keys.value.add!),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(25),
                          ),
                          content: SizedBox(
                            height: 200.h,
                            width: double.maxFinite,
                            child: ListView.builder(
                              shrinkWrap: true,
                              itemCount: providerServices.length,
                              itemBuilder: (BuildContext context, int index) {
                                // controller.services.value[i].type?.name

                                return ListTile(
                                  enabled: !controller.services.value
                                      .map((e) => e.$1)
                                      .contains(providerServices[index]
                                          .typeModel
                                          ?.id),
                                  title: Text(providerServices[index]
                                          .typeModel
                                          ?.name
                                          .text ??
                                      ''),
                                  onTap: () {
                                    // if (controller.services.value.length <
                                    //     providerServices.length) {
                                    log('asfassaf ${controller.services.value.map((e) => e.$1)}');

                                    // controller.services.value =
                                    // [
                                    //   ...controller.services.value,
                                    // final type = Types(
                                    //     id: 8,
                                    //     // providerServices[index]
                                    //     //     .typeModel
                                    //     //     ?.id,
                                    //     amount: 0,
                                    //     type: City(
                                    //       id: 8,
                                    //       // providerServices[index]
                                    //       //     .typeModel
                                    //       //     ?.id,
                                    //       name: providerServices[index]
                                    //           .typeModel
                                    //           ?.name
                                    //           .text,
                                    //     ));
                                    // ];
                                    // }

                                    controller.onAddService(
                                        id: providerServices[index]
                                                .typeModel
                                                ?.id ??
                                            0,
                                        name: providerServices[index]
                                                .typeModel
                                                ?.name
                                                .text ??
                                            '');

                                    log('afasfsa ${providerServices[index].typeModel?.name.text} asfID: ${providerServices[index].typeModel?.id}');

                                    log('asfassaf ${controller.services.value.map((e) => e.$1)}');

                                    Navigator.of(context).pop();

                                    setState(() {});
                                  },
                                );
                              },
                            ),
                          ),
                        );
                      },
                    );
                  },
                  child: Text(Get.find<LanguageController>().keys.value.add!),
                ),

                const Spacer(),

                //? Edit Button
                Center(
                  child: CustomButton(
                    height: 50.h,
                    width: 200.w,
                    label: Get.find<LanguageController>().keys.value.edit!,
                    onTap: () => controller.editOrderClothes(),
                  ),
                ),
              ],
            ),
          );
        });
      });
    });
  }
}
