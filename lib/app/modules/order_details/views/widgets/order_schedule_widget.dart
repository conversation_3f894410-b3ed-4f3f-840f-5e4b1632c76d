import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/order_details/controllers/order_details_controller.dart';
import '../../../../../global/constants/constants.dart';
import '../../../../../global/constants/theme.dart';
import '../../../../../global/controllers/language_controller.dart';

class OrderScheduleWidget extends StatelessWidget {
  const OrderScheduleWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<OrderDetailsController>(builder: (controller) {
      return Container(
        padding: const EdgeInsets.all(10),
        margin: const EdgeInsets.all(10),
        width: Get.width,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(15),
          border: Border.all(color: primaryColor, width: 0.5.w),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              Get.find<LanguageController>().keys.value.orderSchedule!,
              style: bigTextStyle,
            ),
            const Divider(color: primaryColor),
            Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    Text(
                      Get.find<LanguageController>().keys.value.startsAt!,
                      style: regularTextStyle,
                    ),
                    Text(
                      Get.find<LanguageController>().keys.value.endsAt!,
                      style: regularTextStyle,
                    ),
                  ],
                ),
                for (int i = 0;
                    i < controller.bookingData.orderSchedule!.length;
                    i++)
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      Text(
                        controller.bookingData.orderSchedule![i].startsAt!,
                        style: regularTextStyle,
                      ),
                      Text(
                        controller.bookingData.orderSchedule![i].endsAt!,
                        style: regularTextStyle,
                      ),
                    ],
                  ),
              ],
            ),
          ],
        ),
      );
    });
  }
}
