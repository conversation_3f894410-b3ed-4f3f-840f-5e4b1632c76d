import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_clean/global/widget/language_widget.dart';
import '../../../../global/constants/theme.dart';
import '../../../../global/controllers/language_controller.dart';
import '../controllers/languages_controller.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class LanguagesView extends GetView<LanguagesController> {
  const LanguagesView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: Get.width,
        height: Get.height,
        padding: const EdgeInsets.all(10),
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              'assets/images/main_background.png',
            ),
            fit: BoxFit.fill,
          ),
        ),
        child: SafeArea(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  IconButton(
                    onPressed: Get.back,
                    icon: const Icon(
                      CupertinoIcons.back,
                      size: 30,
                    ),
                  ),
                  Expanded(
                    child: Text(
                      Get.find<LanguageController>().keys.value.languages!,
                      style: bigTextStyle,
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 20.h),
              ...Get.find<LanguageController>().languagesModel.value.data!.map(
                    (e) => LanguageWidget(
                      languageName: e.name!,
                      onPressed: () => Get.find<LanguageController>()
                          .changeLanguageAsWeNeed(e, context),
                    ),
                  ),
              // LanguageWidget(
              //   languageName: 'العربية',
              //   onPressed: () => controller.changeToArabic(),
              // ),
              // LanguageWidget(
              //   languageName: 'English',
              //   onPressed: () => controller.changeToEnglish(),
              // ),
              // LanguageWidget(
              //   languageName: 'Hebraic',
              //   onPressed: () => controller.changeToHeb(),
              // ),
            ],
          ),
        ),
      ),
    );
  }
}
