import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/enter_new_password_forget_password/controllers/state/enter_new_password_forget_state.dart';
import 'package:get_clean/global/widget/loading_widget.dart';
import '../../../../global/controllers/language_controller.dart';
import '../../../../global/widget/custom_button.dart';
import '../../../../global/widget/custom_form_field.dart';
import '../controllers/enter_new_password_forget_password_controller.dart';

class EnterNewPasswordForgetPasswordView
    extends GetView<EnterNewPasswordForgetPasswordController> {
  const EnterNewPasswordForgetPasswordView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return GetBuilder<EnterNewPasswordForgetPasswordController>(
      builder: (controller) {
        return Scaffold(
          body: Form(
            key: controller.key,
            child: Container(
              width: Get.width,
              height: Get.height,
              padding: const EdgeInsets.all(10),
              decoration: const BoxDecoration(
                image: DecorationImage(
                  image: AssetImage(
                    'assets/images/main_background.png',
                  ),
                  fit: BoxFit.fill,
                ),
              ),
              child: SafeArea(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.asset(
                      'assets/images/reset_password.png',
                    ),
                    CustomFormField(
                      keyboardType: TextInputType.text,
                      label: Get.find<LanguageController>()
                          .keys
                          .value
                          .newPassword!,
                      controller: controller.password,
                      validator: (value) {
                        if (value.isEmpty) {
                          return Get.find<LanguageController>()
                              .keys
                              .value
                              .pleaseInsertPassword!;
                        }
                        return null;
                      },
                    ),
                    CustomFormField(
                      keyboardType: TextInputType.text,
                      label: Get.find<LanguageController>()
                          .keys
                          .value
                          .newPasswordConfirmation!,
                      controller: controller.passwordConfirmation,
                      validator: (value) {
                        if (value.isEmpty) {
                          return Get.find<LanguageController>()
                              .keys
                              .value
                              .pleaseInsertPasswordConfirmation!;
                        }
                        return null;
                      },
                    ),
                    controller.state.value is EnterNewPasswordForgetLoadingState
                        ? const LoadingWidget()
                        : CustomButton(
                            label:
                                Get.find<LanguageController>().keys.value.next!,
                            onTap: controller.sendPassword,
                            height: 43.h,
                            width: 129.w,
                          ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
