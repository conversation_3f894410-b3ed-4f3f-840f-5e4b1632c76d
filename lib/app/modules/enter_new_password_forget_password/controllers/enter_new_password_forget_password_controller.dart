import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/enter_new_password_forget_password/provider/remote_provider.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_storage/get_storage.dart';

import '../../../routes/app_pages.dart';
import 'state/enter_new_password_forget_state.dart';

class EnterNewPasswordForgetPasswordController extends GetxController {
  TextEditingController password = TextEditingController();
  TextEditingController passwordConfirmation = TextEditingController();

  final provider = EnterNewPasswordForgetRemoteProvider();
  final state = EnterNewPasswordForgetState().obs;

  final key = GlobalKey<FormState>();

  void sendPassword() async {
    state.value = EnterNewPasswordForgetLoadingState();
    update();

    state.value = await provider.sendNewPassword(
        password.text, passwordConfirmation.text);

    update();

    if (state.value is EnterNewPasswordForgetSuccessState) {
      GetStorage().remove(tokenKey);
      Get.toNamed(Routes.PASSWORD_CHANGED_SUCCESSFULLY);
    }
  }
}
