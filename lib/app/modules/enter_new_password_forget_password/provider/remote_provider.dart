import 'dart:developer';

import 'package:get_clean/app/modules/enter_new_password_forget_password/controllers/state/enter_new_password_forget_state.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/dio/dio_helper.dart';
import 'package:get_clean/global/help_functions/help_functions.dart';

class EnterNewPasswordForgetRemoteProvider {
  DioHelper helper = DioHelper();

  Future<EnterNewPasswordForgetState> sendNewPassword(
      String password, String passwordConfirmation) async {
    try {
      final response = await helper.postData(
        changePasswordURL,
        {"password": password, "password_confirmation": passwordConfirmation},
      );

      if (response['success'] == true) {
        showSuccessToast(response['message']);
        return EnterNewPasswordForgetSuccessState(response['message']);
      } else {
        showErrorToast(response['message']);
        return EnterNewPasswordForgetFailedState(response['message']);
      }
    } catch (e) {
      log(e.toString());
      return EnterNewPasswordForgetFailedState(e.toString());
    }
  }
}
