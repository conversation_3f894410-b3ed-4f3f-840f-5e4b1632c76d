import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/constants/theme.dart';
import 'package:get_clean/global/widget/custom_button.dart';
import 'package:get_clean/global/widget/custom_drop_down_button.dart';
import 'package:get_clean/global/widget/custom_form_field.dart';
import 'package:get_clean/global/widget/date_time_picker_widget.dart';

import '../../../../global/controllers/language_controller.dart';
import '../controllers/add_offer_controller.dart';

class AddOfferView extends GetView<AddOfferController> {
  const AddOfferView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<AddOfferController>(
      builder: (controller) {
        return Scaffold(
          appBar: AppBar(
            title:
                Text(Get.find<LanguageController>().keys.value.offerDetails!),
            centerTitle: true,
          ),
          body: SingleChildScrollView(
            padding: const EdgeInsets.all(10),
            child: Column(
              children: [
                // offer image
                InkWell(
                  onTap: controller.onOfferImagePressed,
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10.sp),
                      border: Border.all(color: primaryColor),
                      color: primaryColor,
                    ),
                    width: Get.width,
                    height: 300.h,
                    child: controller.offerImage.value.path.isEmpty
                        ? Icon(
                            FontAwesomeIcons.camera,
                            color: Colors.black12,
                            size: 200.sp,
                          )
                        : ClipRRect(
                            borderRadius: BorderRadius.circular(10.sp),
                            child: Image.file(
                              controller.offerImage.value,
                              fit: BoxFit.fill,
                            ),
                          ),
                  ),
                ),

                Padding(
                  padding: const EdgeInsets.all(10.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        Get.find<LanguageController>().keys.value.isActive!,
                        style: regularTextStyle,
                      ),
                      Switch(
                        activeTrackColor: Colors.green,
                        thumbColor: MaterialStateProperty.resolveWith(
                            (states) => Colors.white),
                        value: controller.isActive.value,
                        onChanged: controller.onchangeIsActive,
                      ),
                    ],
                  ),
                ),

                // offer name
                CustomFormField(
                  hint: Get.find<LanguageController>().keys.value.name!,
                  label: Get.find<LanguageController>().keys.value.name!,
                  controller: controller.nameController,
                  keyboardType: TextInputType.text,
                ),

                // offer address
                CustomDropDownButton(
                  hint: Get.find<LanguageController>().keys.value.address!,
                  label: Get.find<LanguageController>().keys.value.address!,
                  items:
                      controller.homeController.user.value.provider!.workZones!
                          .map(
                            (e) => DropdownMenuItem(
                              value: e,
                              child: Text('${e.name!} , ${e.area?.name ?? ''}'),
                            ),
                          )
                          .toList(),
                  onChanged: controller.onAddressChanged,
                  value: controller.choosedWorkZone.value.id == null
                      ? null
                      : controller.choosedWorkZone.value,
                  buttonHeight: 50.h,
                  buttonWidth: Get.width,
                ),

                // offer price
                CustomFormField(
                  hint: Get.find<LanguageController>().keys.value.price!,
                  label: Get.find<LanguageController>().keys.value.price!,
                  controller: controller.priceController,
                  keyboardType: TextInputType.number,
                ),

                // choose date from and to
                Row(
                  children: [
                    Expanded(
                      child: DateTimePickerWidget(
                        hint: Get.find<LanguageController>().keys.value.from!,
                        label: Get.find<LanguageController>().keys.value.from!,
                        controller: controller.fromDateController,
                      ),
                    ),
                    Expanded(
                      child: DateTimePickerWidget(
                        hint: Get.find<LanguageController>().keys.value.to!,
                        label: Get.find<LanguageController>().keys.value.to!,
                        controller: controller.toDateController,
                      ),
                    ),
                  ],
                ),

                // offer average time
                CustomFormField(
                  hint: Get.find<LanguageController>().keys.value.averageTime!,
                  label: Get.find<LanguageController>().keys.value.averageTime!,
                  controller: controller.averageTimeController,
                  keyboardType: TextInputType.number,
                ),

                // offer description
                CustomFormField(
                  hint: Get.find<LanguageController>().keys.value.description!,
                  label: Get.find<LanguageController>().keys.value.description!,
                  controller: controller.descriptionController,
                  minLines: 5,
                  maxLines: 10,
                  keyboardType: TextInputType.text,
                ),

                // save button
                CustomButton(
                  label: Get.find<LanguageController>().keys.value.save!,
                  onTap: controller.onTapSave,
                  height: 50.h,
                  width: 200.w,
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
