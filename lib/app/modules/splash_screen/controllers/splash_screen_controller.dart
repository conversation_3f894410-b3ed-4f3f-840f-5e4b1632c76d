import 'package:get/get.dart';
import 'package:get_clean/app/routes/app_pages.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/controllers/global_values_controller.dart';
import 'package:get_clean/global/controllers/language_controller.dart';
import 'package:get_storage/get_storage.dart';

class SplashScreenController extends GetxController {
  final fromLanguage = Get.arguments;

  @override
  void onInit() async {
    super.onInit();
    final languageController = Get.find<LanguageController>();

    final data = await languageController.getData(); // TODO-API

    if (data) {
      //TODO-API
      if (fromLanguage != null) {
        await Get.find<GlobalValuesController>().getAllData();
      }

      final isLogged = GetStorage().hasData(tokenKey);

      if (isLogged) {
        Get.find<GlobalValuesController>().setUserLoggedIn();
      }

      Future.delayed(const Duration(seconds: 2),
          () => Get.offAllNamed(isLogged ? Routes.HOME : Routes.LOGIN));
    }
  }
}
