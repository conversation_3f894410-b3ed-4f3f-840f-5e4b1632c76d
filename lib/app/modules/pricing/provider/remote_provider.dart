import 'dart:convert';
import 'dart:developer';

import 'package:get/get.dart';
import 'package:get_clean/app/modules/home/<USER>/home_controller.dart';
import 'package:get_clean/app/modules/pricing/controllers/states/pricing_states.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/dio/dio_helper.dart';
import 'package:get_clean/global/help_functions/help_functions.dart';
import 'package:get_clean/global/models/my_services_model.dart';

class PricingRemoteProvider {
  DioHelper helper = DioHelper();

  Future<PricingState> getMyServices() async {
    final providerId = Get.find<HomeController>().user.value.provider?.userId;
    // final providerId = Get.find<HomeController>().user.value.provider?.id;

    try {
      final response = await helper.getData(
        '$getProviderServicesURL?user_id=$providerId',
      );

      log('asfasffa ${providerId} asfasf ${jsonEncode(response)}');

      if (response['success'] == true) {
        return PricingSuccessState(MyServicesModel.fromJson(response));
      } else {
        showErrorToast(response['message']);
        return PricingFailedState(response['message']);
      }
    } catch (e) {
      return PricingFailedState(e.toString());
    }
  }

  Future<bool> deleteService(int serviceID) async {
    try {
      final response = await helper.postData(
        deleteServiceURL + serviceID.toString(),
        {'': null},
      );

      if (response['success'] == true) {
        showSuccessToast(response['message']);
        return true;
      } else {
        showErrorToast(response['message']);
        return false;
      }
    } catch (e) {
      log(e.toString());
      return false;
    }
  }
}
