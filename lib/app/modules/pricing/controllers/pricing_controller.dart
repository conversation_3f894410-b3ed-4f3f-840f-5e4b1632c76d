import 'dart:developer';

import 'package:get/get.dart';
import 'package:get_clean/app/modules/pricing/controllers/states/pricing_states.dart';
import 'package:get_clean/app/modules/pricing/provider/remote_provider.dart';
import 'package:get_clean/app/routes/app_pages.dart';
import 'package:get_clean/global/help_functions/help_functions.dart';
import 'package:get_clean/global/models/my_services_model.dart';
import 'package:get_clean/global/models/provider_services.dart';

import '../../../../global/controllers/language_controller.dart';

class PricingController extends GetxController {
  PricingRemoteProvider provider = PricingRemoteProvider();

  final pricingState = PricingState().obs;
  final myServices = MyServicesModel().obs;
  final myOffers = MyServicesModel().obs;

  @override
  void onInit() {
    super.onInit();

    getMyServices();
  }

  Future<void> getMyServices() async {
    pricingState.value = PricingLoadingState();
    update();

    pricingState.value = await provider.getMyServices();

    if (pricingState.value is PricingSuccessState) {
      myServices.value = pricingState.value.myServicesModel!;
    }

    update();
  }

  void onAddNewPressed() async {
    await Get.toNamed(Routes.ADD_NEW_SERVICE);
    getMyServices();
  }

  void onEditServicePressed(ProviderServices service) {
    log('asfsafafsa ${service?.toJson()} dddd ${service.service?.id}');
    Get.toNamed(Routes.EDIT_SERVICE, arguments: {'service': service});
  }

  void onDeleteServicePressed(int serviceID) {
    Get.defaultDialog(
      title: Get.find<LanguageController>().keys.value.deleteService!,
      middleText:
          Get.find<LanguageController>().keys.value.deleteServiceMessage!,
      onConfirm: () async {
        showWaitingIndicator();
        final response = await provider.deleteService(serviceID);
        if (response) {
          getMyServices();
        }
        hideWaitingIndicator();
        Get.back();
      },
      textConfirm: Get.find<LanguageController>().keys.value.yes!,
      textCancel: Get.find<LanguageController>().keys.value.no!,
    );
  }
}
