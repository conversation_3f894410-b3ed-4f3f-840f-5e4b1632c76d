import 'package:get_clean/global/models/my_services_model.dart';

class PricingState {
  MyServicesModel? myServicesModel;
  String? errorMessage;
}

class PricingSuccessState extends PricingState {
  PricingSuccessState(MyServicesModel myServicesModel) {
    this.myServicesModel = myServicesModel;
  }
}

class PricingFailedState extends PricingState {
  PricingFailedState(String errorMessage) {
    this.errorMessage = errorMessage;
  }
}

class PricingLoadingState extends PricingState {}
