import 'dart:async';

import 'package:get/get.dart';
import 'package:get_clean/app/modules/payment/provider/payment_remote_provider.dart';
import 'package:get_clean/global/models/user_booking.dart';

import '../../../routes/app_pages.dart';

class PaymentCompeletedController extends GetxController {
  final BookingData? bookingData = Get.arguments['order'];
  final bool? payDeposit = Get.arguments['payDeposit'];
  final tip = Get.arguments['tip'];

  final provider = PaymentRemoteProvider();

  // Future<void> addTipAsUser() async {
  //   try {
  //     if (tip != null && tip > 0) {
  //       await provider.addTip(bookingData.id!.toInt(), tip);
  //     }
  //   } catch (e) {
  //     rethrow;
  //   }
  // }

  @override
  void onInit() {
    super.onInit();
    // addTipAsUser();
    Timer(const Duration(seconds: 2), () {
      // if (payDeposit) {
      Get.offAllNamed(Routes.HOME);
      // } else {
      // Get.toNamed(
      //   Routes.RATE_PROVIDER,
      //   arguments: {
      //     'order': bookingData.toJson(),
      //   },
      // );
      // }
    });
  }
}
