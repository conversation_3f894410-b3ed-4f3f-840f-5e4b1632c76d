import 'dart:developer';

import 'package:get_clean/app/modules/my_offers/controllers/states/my_offers_states.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/dio/dio_helper.dart';
import 'package:get_clean/global/help_functions/help_functions.dart';
import 'package:get_clean/global/models/my_offers_model.dart';

class MyOffersRemoteProvider {
  DioHelper helper = DioHelper();

  Future<MyOffersState> getMyOffers() async {
    try {
      final response = await helper.getData(getMyOffersURL);
      if (response['success'] == true) {
        return MyOffersSuccessState(MyOffersModel.fromJson(response));
      } else {
        showErrorToast(response['message']);
        return MyOffersErrorState(response['message']);
      }
    } catch (e) {
      log(e.toString());
      return MyOffersErrorState(e.toString());
    }
  }

  Future<bool> deleteOffer(int id) async {
    try {
      final response = await helper.postData(
        deleteOfferURL + id.toString(),
        {"": null},
      );
      if (response['success'] == true) {
        return true;
      } else {
        showErrorToast(response['message']);
        return false;
      }
    } catch (e) {
      log(e.toString());
      return false;
    }
  }

  Future<bool> setActivityOfOffer(int id, bool value) async {
    try {
      final response = await helper.postData(
        editOfferURL + id.toString(),
        {"is_active": value},
      );
      if (response['success'] == true) {
        return true;
      } else {
        showErrorToast(response['message']);
        return false;
      }
    } catch (e) {
      log(e.toString());
      return false;
    }
  }
}
