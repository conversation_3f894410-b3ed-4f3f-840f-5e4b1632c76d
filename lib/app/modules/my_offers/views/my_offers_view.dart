import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/my_offers/controllers/states/my_offers_states.dart';
import 'package:get_clean/global/widget/custom_button.dart';
import 'package:get_clean/global/widget/loading_widget.dart';
import 'package:get_clean/global/widget/my_offer_widget.dart';
import '../../../../global/constants/theme.dart';
import '../../../../global/controllers/language_controller.dart';
import '../controllers/my_offers_controller.dart';

class MyOffersView extends GetView<MyOffersController> {
  const MyOffersView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return GetBuilder<MyOffersController>(builder: (controller) {
      return Scaffold(
        appBar: AppBar(
          title: Text(Get.find<LanguageController>().keys.value.myOffers!),
          centerTitle: true,
        ),
        body: Builder(builder: (context) {
          if (controller.myOffersState.value is MyOffersLoadingState) {
            return const LoadingWidget();
          }
          if (controller.myOffersState.value is MyOffersErrorState) {
            return Center(
              child: Text(
                controller.myOffersState.value.errorMessage!,
                style: bigTextStyle,
              ),
            );
          }
          return Padding(
            padding: const EdgeInsets.all(10.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomButton(
                  label: Get.find<LanguageController>().keys.value.addOffer!,
                  onTap: controller.onAddOfferPressed,
                  height: 30.h,
                  width: 100.w,
                  fontSize: 12,
                  borderRadius: 10,
                  icon: const Icon(
                    FontAwesomeIcons.circlePlus,
                    color: Colors.white,
                    size: 17,
                  ),
                ),
                Expanded(
                  child: ListView.builder(
                    itemCount: controller.myOffers.value.data!.length,
                    itemBuilder: (context, index) {
                      return MyOfferWidget(
                        onDeletePressed: () => controller.onDeletePressed(
                            controller.myOffers.value.data![index]),
                        onEditPressed: () => controller.onEditPressed(
                            controller.myOffers.value.data![index]),
                        isActive:
                            controller.myOffers.value.data![index].isActive,
                        onActiveChanged: (value) => controller.onActiveChanged(
                            value, controller.myOffers.value.data![index]),
                        imageURL: controller.myOffers.value.data![index].image,
                        name: controller.myOffers.value.data![index].name,
                        address:
                            '${controller.myOffers.value.data![index].address!.name!} , ${controller.myOffers.value.data![index].address!.area!.name!}',
                      );
                    },
                  ),
                ),
              ],
            ),
          );
        }),
      );
    });
  }
}
