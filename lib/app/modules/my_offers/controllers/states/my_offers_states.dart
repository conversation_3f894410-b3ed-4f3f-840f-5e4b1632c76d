import 'package:get_clean/global/models/my_offers_model.dart';

class MyOffersState {
  MyOffersModel? myOffers;
  String? errorMessage;
}

class MyOffersSuccessState extends MyOffersState {
  MyOffersSuccessState(MyOffersModel myOffers) {
    this.myOffers = myOffers;
  }
}

class MyOffersErrorState extends MyOffersState {
  MyOffersErrorState(String errorMessage) {
    this.errorMessage = errorMessage;
  }
}

class MyOffersLoadingState extends MyOffersState {}
