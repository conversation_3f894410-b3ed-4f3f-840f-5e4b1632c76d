import 'package:get/get.dart';
import 'package:get_clean/global/controllers/global_values_controller.dart';
import 'package:get_clean/global/models/all_offers_model.dart';
import 'package:get_clean/global/models/home_model.dart';

import '../../../routes/app_pages.dart';

class OffersController extends GetxController {
  final AllOffersModel offers =
      Get.find<GlobalValuesController>().allOffers.value;

  void onOfferTapped(offer) {
    Get.toNamed(
      Routes.OFFER_DETAILS,
      arguments: {'offer': Offers.fromJson(offer.toJson())},
    );
  }
}
