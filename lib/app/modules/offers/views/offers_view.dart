import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/home/<USER>/home_controller.dart';
import 'package:get_clean/app/modules/offers/views/widgets/offer_widget.dart';
import 'package:get_clean/global/widget/bottom_navigation_bar.dart';
import 'package:get_clean/global/widget/custom_form_field.dart';

import '../../../../global/controllers/language_controller.dart';
import '../controllers/packages_controller.dart';

class OffersView extends GetView<OffersController> {
  const OffersView({super.key});

  @override
  Widget build(BuildContext context) {
    Get.put(HomeController());
    return SafeArea(
      top: false,
      child: Scaffold(
        bottomNavigationBar: const BottomNavBarWidget(),
        appBar: AppBar(
          title: Text(Get.find<LanguageController>().keys.value.offers!),
          centerTitle: true,
        ),
        body: Container(
          decoration: const BoxDecoration(
            image: DecorationImage(
              image: AssetImage(
                'assets/images/main_background_bottom.png',
              ),
              fit: BoxFit.fill,
            ),
          ),
          alignment: Alignment.center,
          padding: const EdgeInsets.all(10),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomFormField(
                keyboardType: TextInputType.text,
                hint: Get.find<LanguageController>().keys.value.search!,
              ),
              Expanded(
                child: ListView.builder(
                  itemBuilder: (context, index) {
                    return OfferWidget(
                      address:
                          '${controller.offers.data![index].address!.name!},${controller.offers.data![index].address!.area!.name!}',
                      name: controller.offers.data![index].name,
                      onBookPressed: () => controller
                          .onOfferTapped(controller.offers.data![index]),
                      onOfferPressed: () => controller
                          .onOfferTapped(controller.offers.data![index]),
                      imageURL: controller.offers.data![index].image,
                      providerImage:
                          controller.offers.data![index].provider!.image!,
                      providerName:
                          controller.offers.data![index].provider!.name!,
                      rating: controller.offers.data![index].provider!.rating!
                          .toDouble(),
                    );
                  },
                  itemCount: controller.offers.data?.length ?? 0,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
