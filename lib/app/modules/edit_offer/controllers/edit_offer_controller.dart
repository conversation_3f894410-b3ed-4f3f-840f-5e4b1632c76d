import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:dio/dio.dart' as d;
import 'package:get_clean/app/modules/edit_offer/provider/edit_offer_remote_provider.dart';
import 'package:get_clean/global/models/my_offers_model.dart';
import '../../../../global/help_functions/help_functions.dart';
import '../../../../global/models/work_zones.dart';
import '../../home/<USER>/home_controller.dart';

class EditOfferController extends GetxController {
  final OfferData offer = Get.arguments['offer'];

  final homeController = Get.find<HomeController>();
  final provider = EditOfferRemoteProvider();

  final offerImage = File('').obs;
  final choosedWorkZone = WorkZones().obs;

  final isActive = false.obs;

  TextEditingController nameController = TextEditingController();
  TextEditingController priceController = TextEditingController();
  TextEditingController fromDateController = TextEditingController();
  TextEditingController toDateController = TextEditingController();
  TextEditingController averageTimeController = TextEditingController();
  TextEditingController descriptionController = TextEditingController();

  @override
  void onInit() {
    super.onInit();
    final workZone = WorkZones.fromJson(offer.address!.toJson());
    choosedWorkZone.value = homeController.user.value.provider!.workZones!
        .firstWhere((element) => element.id == workZone.id);
    isActive.value = offer.isActive!;
    nameController = TextEditingController(text: offer.name);
    priceController = TextEditingController(text: offer.price.toString());
    fromDateController = TextEditingController(text: offer.startDate);
    toDateController = TextEditingController(text: offer.endDate);
    averageTimeController =
        TextEditingController(text: offer.duration.toString());
    descriptionController = TextEditingController(text: offer.description);
  }

  void onOfferImagePressed() async {
    offerImage.value = await pickFile();
    update();
  }

  void onAddressChanged(value) {
    choosedWorkZone.value = value;
    update();
  }

  void onchangeIsActive(value) {
    isActive.value = value;
    update();
  }

  void onTapSave() async {
    showWaitingIndicator();

    final response = await provider.editOffer(offer.id!, {
      "district_id": choosedWorkZone.value.area?.id,
      "name": nameController.text,
      "description": descriptionController.text,
      "duration": averageTimeController.text,
      "start_date": fromDateController.text,
      "end_date": toDateController.text,
      if (offerImage.value.path.isNotEmpty)
        "image": await d.MultipartFile.fromFile(
          offerImage.value.path,
          filename: 'offerImage.png',
        ),
      "is_active": isActive.value,
      "price": priceController.text,
    });

    hideWaitingIndicator();

    if (response) {
      Get.back();
    }
  }
}
