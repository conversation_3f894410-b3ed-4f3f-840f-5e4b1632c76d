import 'dart:developer';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/dio/dio_helper.dart';
import 'package:get_clean/global/help_functions/help_functions.dart';

class EditOfferRemoteProvider {
  DioHelper helper = DioHelper();

  Future<bool> editOffer(int id, data) async {
    try {
      final response =
          await helper.postData(editOfferURL + id.toString(), data);
      if (response['success'] == true) {
        showSuccessToast(response['message']);
        return true;
      } else {
        showErrorToast(response['message']);
        return false;
      }
    } catch (e) {
      log(e.toString());
      return false;
    }
  }
}
