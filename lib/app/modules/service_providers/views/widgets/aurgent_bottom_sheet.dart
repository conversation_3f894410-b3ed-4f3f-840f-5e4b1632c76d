import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/service_providers/controllers/states/providers_state.dart';
import 'package:get_clean/app/modules/service_providers/views/widgets/area_and_city_widget.dart';
import 'package:get_clean/global/controllers/language_controller.dart';
import 'package:get_clean/global/widget/custom_button.dart';
import 'package:get_clean/global/widget/custom_form_field.dart';
import 'package:get_clean/global/widget/loading_widget.dart';

import '../../../../../global/constants/constants.dart';
import '../../../../../global/constants/theme.dart';
import '../../controllers/service_providers_controller.dart';
import 'how_many_hours_sofa.dart';
import 'select_how_many_hours_widget.dart';

class AurgentBottomSheet extends StatelessWidget {
  const AurgentBottomSheet({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ServiceProvidersController>(builder: (controller) {
      log('afsasaf ${controller.serviceFromGlobal.toJson()}');

      return Container(
        padding: const EdgeInsets.all(10),
        height: Get.height * 0.8,
        decoration: const BoxDecoration(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(45),
            topRight: Radius.circular(45),
          ),
          color: Colors.white,
        ),
        child: SingleChildScrollView(
          child: Column(
            children: [
              Container(
                decoration: BoxDecoration(
                  color: primaryColor,
                  borderRadius: BorderRadius.circular(10),
                ),
                margin: const EdgeInsets.all(10),
                height: 3,
                width: Get.width * 0.3,
              ),
              Text(
                Get.find<LanguageController>().keys.value.urgentRequest!,
                style: bigTextStyle,
              ),
              const AreaAndCityProvidersWidget(),
              CustomFormField(
                keyboardType: TextInputType.text,
                controller: controller.address,
                label: Get.find<LanguageController>().keys.value.address!,
                hint: Get.find<LanguageController>().keys.value.address!,
              ),
              Container(
                margin: const EdgeInsets.all(10),
                height: 130,
                width: Get.width * 0.9,
                decoration: BoxDecoration(
                  color: const Color(0xffF2F2F2),
                  borderRadius: BorderRadius.circular(14),
                  boxShadow: const [
                    BoxShadow(
                      color: Color(0xff000029),
                      blurRadius: 0.05,
                      spreadRadius: 0.0000000000005,
                    )
                  ],
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    Container(
                      alignment: Alignment.center,
                      width: Get.width * 0.7,
                      height: 50,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(15),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.grey[400]!,
                            blurRadius: 2,
                          )
                        ],
                      ),
                      child: Text(
                        Get.find<LanguageController>().keys.value.today!,
                        style: bigTextStyle,
                      ),
                    ),
                    Container(
                      alignment: Alignment.center,
                      width: Get.width * 0.7,
                      height: 50,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(15),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.grey[400]!,
                            blurRadius: 2,
                          )
                        ],
                      ),
                      child: Text(
                        '${DateTime.now().day}-${DateTime.now().month}-${DateTime.now().year}',
                        style: bigTextStyle,
                      ),
                    ),
                  ],
                ),
              ),
              const Divider(color: primaryColor),
              // here we show how many hours for sofa
              if (controller.serviceFromGlobal.value.pricingOption?.id == 3)
                const HowManyHoursSofa(),

              // here we show the select how many hours widget
              if (controller.serviceFromGlobal.value.pricingOption?.id == 2)
                CustomFormField(
                  keyboardType: TextInputType.number,
                  controller: controller.howManyMeters,
                  label:
                      Get.find<LanguageController>().keys.value.howManyMeters!,
                  hint:
                      Get.find<LanguageController>().keys.value.howManyMeters!,
                ),

              // here we show the select how many hours widget
              if (controller.serviceFromGlobal.value.pricingOption?.id == 1)
                const SelectHowManyHoursWidget(),
              const Divider(color: primaryColor),
              Container(
                alignment: Get.find<LanguageController>().isArabic
                    ? Alignment.centerRight
                    : Alignment.centerLeft,
                child: Text(
                  Get.find<LanguageController>().keys.value.whatTime!,
                  style: big2TextStyle,
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // CustomDropDownButton(
                  //   buttonHeight: 40.0,
                  //   buttonWidth: Get.width * 0.4,
                  //   hint: Get.find<LanguageController>().keys.value.whatTime!,
                  //   value: 'AM',
                  //   onChanged: (value) {},
                  //   items: const [
                  //     DropdownMenuItem(
                  //       value: 'AM',
                  //       child: Text('AM'),
                  //     ),
                  //     DropdownMenuItem(
                  //       value: 'PM',
                  //       child: Text('PM'),
                  //     ),
                  //   ],
                  // ),
                  InkWell(
                    onTap: () => controller.showTimePickers(context),
                    child: Text(
                      controller.pickedTime.value,
                      style: bigTextStyle,
                    ),
                  ),
                ],
              ),
              CustomFormField(
                keyboardType: TextInputType.text,
                controller: controller.note,
                label: Get.find<LanguageController>().keys.value.note!,
                hint: Get.find<LanguageController>().keys.value.note!,
                minLines: 3,
                maxLines: 5,
              ),
              const Divider(color: primaryColor),
              Row(
                children: [
                  Checkbox(
                    value: controller.iNeedMaterial.value,
                    onChanged: controller.onChangeNeedMaterial,
                  ),
                  Text(
                    Get.find<LanguageController>().keys.value.iNeedMaterial!,
                    style: regularTextStyle,
                  ),
                ],
              ),
              Center(
                child: controller.providersState.value
                        is AurgentRequestLoadingState
                    ? const LoadingWidget()
                    : CustomButton(
                        label: Get.find<LanguageController>()
                            .keys
                            .value
                            .sendToAll!,
                        onTap: controller.onAurgentPressed,
                        height: 50,
                        width: Get.width * 0.8,
                      ),
              ),
            ],
          ),
        ),
      );
    });
  }
}
