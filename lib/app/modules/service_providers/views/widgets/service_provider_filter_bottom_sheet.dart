import 'package:flutter/material.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/add_new_service/controllers/add_new_service_controller.dart';
import 'package:get_clean/app/modules/service_providers/views/widgets/area_and_city_widget.dart';
import 'package:get_clean/app/modules/service_providers/views/widgets/how_many_hours_sofa.dart';
import 'package:get_clean/app/modules/service_providers/views/widgets/select_how_many_hours_widget.dart';
import 'package:get_clean/global/controllers/language_controller.dart';
import 'package:get_clean/global/widget/custom_button.dart';
import 'package:get_clean/global/widget/main_date_widget.dart';
import 'package:intl/intl.dart';

import '../../../../../global/constants/constants.dart';
import '../../../../../global/constants/theme.dart';
import '../../../../../global/widget/custom_form_field.dart';
import '../../controllers/service_providers_controller.dart';

class ServiceProviderFilterBottomSheet extends StatelessWidget {
  const ServiceProviderFilterBottomSheet({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ServiceProvidersController>(builder: (controller) {
      return Container(
        padding: const EdgeInsets.all(10),
        height: isOffer(
          controller.serviceFromGlobal.value.pricingOption?.id ?? 0,
        )
            ? Get.height * 0.4
            : Get.height * 0.8,
        decoration: const BoxDecoration(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(45),
            topRight: Radius.circular(45),
          ),
          color: Colors.white,
        ),
        child: SingleChildScrollView(
          child: Column(
            children: [
              Container(
                decoration: BoxDecoration(
                  color: primaryColor,
                  borderRadius: BorderRadius.circular(10),
                ),
                margin: const EdgeInsets.all(10),
                height: 3,
                width: Get.width * 0.3,
              ),
              Text(
                controller.service.name!,
                style: bigTextStyle,
              ),
              const AreaAndCityProvidersWidget(),
              if (!isOffer(
                controller.serviceFromGlobal.value.pricingOption?.id ?? 0,
              )) ...[
                Container(
                  alignment: Get.find<LanguageController>().isArabic
                      ? Alignment.centerRight
                      : Alignment.centerLeft,
                  child: Text(
                    Get.find<LanguageController>().keys.value.selectedDay!,
                    style: big2TextStyle,
                  ),
                ),
                SizedBox(
                  height: 100,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemBuilder: (context, index) {
                      final date = DateTime.now().add(Duration(days: index));
                      return InkWell(
                        onTap: () => controller.changeSelectedDate(date),
                        child: Container(
                          width: 120,
                          alignment: Alignment.center,
                          padding: const EdgeInsets.all(10),
                          margin: const EdgeInsets.all(5),
                          decoration: BoxDecoration(
                            border: Border.all(color: primaryColor),
                            borderRadius: BorderRadius.circular(10),
                            color:
                                controller.selectedDate.value.day == date.day &&
                                        controller.selectedDate.value.month ==
                                            date.month
                                    ? primaryColor
                                    : Colors.white,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                DateFormat.d().format(date),
                                style: controller.selectedDate.value.day ==
                                            date.day &&
                                        controller.selectedDate.value.month ==
                                            date.month
                                    ? regularWhiteTextStyle
                                    : regularTextStyle,
                              ),
                              Text(
                                DateFormat.EEEE().format(date),
                                style: controller.selectedDate.value.day ==
                                            date.day &&
                                        controller.selectedDate.value.month ==
                                            date.month
                                    ? regularWhiteTextStyle
                                    : regularTextStyle,
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                    itemCount: 60,
                  ),
                ),

                Obx(
                  () => MainDateWidget(
                    date: DateFormat('dd/MM/yyyy')
                        .format(controller.selectedDate.value),
                  ),
                ),

                const Divider(color: primaryColor),

                // here we show how many hours for sofa
                if (controller.serviceFromGlobal.value.pricingOption?.id == 3)
                  const HowManyHoursSofa(),

                // here we show the select how many hours widget
                if (controller.serviceFromGlobal.value.pricingOption?.id == 1)
                  const SelectHowManyHoursWidget(),
                // here we show the select how many Meters widget
                if (controller.serviceFromGlobal.value.pricingOption?.id == 2)
                  CustomFormField(
                    keyboardType: TextInputType.number,
                    label: Get.find<LanguageController>()
                        .keys
                        .value
                        .howManyMeters!,
                    hint: Get.find<LanguageController>()
                        .keys
                        .value
                        .howManyMeters!,
                    controller: controller.howManyMeters,
                  ),

                const Divider(color: primaryColor),
                Container(
                  alignment: Get.find<LanguageController>().isArabic
                      ? Alignment.centerRight
                      : Alignment.centerLeft,
                  child: Text(
                    Get.find<LanguageController>().keys.value.whatTime!,
                    style: big2TextStyle,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    color: primaryColor,
                  ),
                  child: InkWell(
                    onTap: () => controller.showTimePickers(context),
                    child: Text(
                      controller.pickedTime.value,
                      style: bigTextStyle.copyWith(
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ],
              SizedBox(height: 20.h),
              CustomButton(
                label: Get.find<LanguageController>().keys.value.done!,
                onTap: controller.filterServices,
                height: 50,
                width: Get.width * 0.8,
              ),
              KeyboardVisibilityBuilder(builder: (context, isKeyboardVisible) {
                return SizedBox(height: isKeyboardVisible ? 200.h : 30.h);
              }),
            ],
          ),
        ),
      );
    });
  }
}
