import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/service_providers/controllers/service_providers_controller.dart';

import '../../../../../global/constants/constants.dart';
import '../../../../../global/constants/theme.dart';
import '../../../../../global/controllers/language_controller.dart';

class SelectHowManyHoursWidget extends StatelessWidget {
  const SelectHowManyHoursWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ServiceProvidersController>(builder: (controller) {
      return Container(
        decoration: BoxDecoration(
          border: Border.all(
            color: primaryColor,
            width: 0.5,
          ),
          borderRadius: BorderRadius.circular(25),
          color: Colors.white,
        ),
        child: Column(
          children: [
            Container(
              decoration: BoxDecoration(
                border: Border.all(
                  color: primaryColor,
                  width: 0.5,
                ),
                borderRadius: const BorderRadius.only(
                  topRight: Radius.circular(25),
                  topLeft: Radius.circular(25),
                ),
                color: primaryColor,
              ),
              alignment: Alignment.center,
              child: Text(
                Get.find<LanguageController>().keys.value.howManyHours!,
                style: regularWhiteTextStyle,
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                for (int i = 1; i < 10; i++)
                  Expanded(
                    child: InkWell(
                      onTap: () => controller.onSelectedHoursTap(i + 1),
                      child: Container(
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          borderRadius: i == 9
                              ? BorderRadius.only(
                                  bottomLeft:
                                      Get.find<LanguageController>().isArabic
                                          ? const Radius.circular(25)
                                          : const Radius.circular(0),
                                  bottomRight:
                                      Get.find<LanguageController>().isArabic
                                          ? const Radius.circular(0)
                                          : const Radius.circular(25),
                                )
                              : i == 1
                                  ? BorderRadius.only(
                                      bottomLeft: Get.find<LanguageController>()
                                              .isArabic
                                          ? const Radius.circular(0)
                                          : const Radius.circular(25),
                                      bottomRight:
                                          Get.find<LanguageController>()
                                                  .isArabic
                                              ? const Radius.circular(25)
                                              : const Radius.circular(0),
                                    )
                                  : BorderRadius.circular(0),
                          color: controller.howManyHours.value == i + 1
                              ? primaryColor
                              : Colors.white,
                        ),
                        padding: const EdgeInsets.all(6),
                        child: Text(
                          '${i + 1}',
                          style: controller.howManyHours.value == i + 1
                              ? middleWhiteTextStyle
                              : middleTextStyle,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ],
        ),
      );
    });
  }
}
