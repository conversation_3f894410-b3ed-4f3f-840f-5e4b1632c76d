import 'package:get_clean/global/models/provider_reviews.dart';

class ProviderReviewsState {
  String? errorMessage;
  ProviderReviews? reviews;
}

class ProviderReviewsLoading extends ProviderReviewsState {}

class ProviderReviewsFailed extends ProviderReviewsState {
  ProviderReviewsFailed(String errorMessage) {
    this.errorMessage = errorMessage;
  }
}

class ProviderReviewsSuccess extends ProviderReviewsState {
  ProviderReviewsSuccess(ProviderReviews reviews) {
    this.reviews = reviews;
  }
}
