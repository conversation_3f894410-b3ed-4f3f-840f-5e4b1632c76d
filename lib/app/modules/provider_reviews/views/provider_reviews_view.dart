import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../global/controllers/language_controller.dart';
import '../../../../global/widget/review_widget.dart';
import '../controllers/provider_reviews_controller.dart';

class ProviderReviewsView extends GetView<ProviderReviewsController> {
  const ProviderReviewsView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: primaryColor,
      appBar: AppBar(
        elevation: 0,
        title: Text(Get.find<LanguageController>().keys.value.reviews!),
        centerTitle: true,
      ),
      body: SafeArea(
        child: Container(
          padding: EdgeInsets.only(
            bottom: 10,
            top: 30.h,
            left: 10,
            right: 10,
          ),
          width: Get.width,
          height: Get.height,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(45),
              topRight: Radius.circular(45),
            ),
          ),
          child: ListView.builder(
            itemBuilder: (context, index) {
              return ReviewWidget(
                review: controller.reviews![index],
              );
            },
            itemCount: controller.reviews!.length,
          ),
        ),
      ),
    );
  }
}
