import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:webview_flutter/webview_flutter.dart';
import '../../../../global/controllers/language_controller.dart';
import '../controllers/payment_webview_controller.dart';

class PaymentWebviewView extends GetView<PaymentWebviewController> {
  const PaymentWebviewView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(Get.find<LanguageController>().keys.value.payment!),
        centerTitle: true,
      ),
      body: WebViewWidget(controller: controller.controller),
    );
  }
}
