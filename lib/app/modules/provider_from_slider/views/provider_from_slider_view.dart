import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/global/models/provider_services.dart';
import 'package:get_clean/global/widget/service_widget.dart';

import '../../../../global/constants/constants.dart';
import '../../../../global/constants/theme.dart';
import '../../../../global/controllers/language_controller.dart';
import '../../../../global/widget/review_widget.dart';
import '../../../../global/widget/text_with_background.dart';
import '../../../routes/app_pages.dart';
import '../controllers/provider_from_slider_controller.dart';

class ProviderFromSliderView extends GetView<ProviderFromSliderController> {
  const ProviderFromSliderView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            child: Container(
              alignment: Alignment.topLeft,
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Color(0xff001F57),
                    Color(0xff2955A1),
                    Color(0xff2B5AA5),
                    Color(0xff001F57),
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
              ),
              child: SafeArea(
                child: IconButton(
                  onPressed: Get.back,
                  icon: const Icon(
                    CupertinoIcons.back,
                    color: Colors.white,
                    size: 40,
                  ),
                ),
              ),
            ),
          ),
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              height: 710.h,
              width: Get.width,
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(45),
                  topRight: Radius.circular(45),
                ),
                image: DecorationImage(
                  image: AssetImage(
                    'assets/images/main_background.png',
                  ),
                  fit: BoxFit.fill,
                  opacity: 0.4,
                ),
              ),
              child: Padding(
                padding: EdgeInsets.only(
                  top: 63.h,
                  right: 10,
                  left: 10,
                  bottom: 10,
                ),
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(
                        controller.provider.name!,
                        style: big2TextStyle,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Image.asset(
                            'assets/images/position.png',
                          ),
                          SizedBox(width: 5.w),
                          Text(
                            controller.provider.city!.name!,
                          ),
                          SizedBox(width: 10.w),
                          // Image.asset(
                          //   'assets/images/user.png',
                          // ),
                          // SizedBox(width: 5.w),
                          // Text(
                          //   'age : ${controller.provider.}',
                          // ),
                        ],
                      ),
                      Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(5),
                          color: Colors.white,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.grey[400]!,
                              blurRadius: 3,
                            ),
                          ],
                        ),
                        margin: const EdgeInsets.all(5),
                        padding: const EdgeInsets.all(10),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Text(
                              Get.find<LanguageController>().keys.value.skills!,
                              style: regularTextStyle,
                            ),
                            if (controller.provider.skills != null)
                              Text(
                                controller.provider.skills!
                                    .map((e) => e.name)
                                    .toList()
                                    .join(','),
                                style: middleTextStyle,
                              ),
                          ],
                        ),
                      ),
                      Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(5),
                          color: Colors.white,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.grey[400]!,
                              blurRadius: 3,
                            ),
                          ],
                        ),
                        margin: const EdgeInsets.all(5),
                        padding: const EdgeInsets.all(10),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Expanded(
                              child: Text(
                                Get.find<LanguageController>()
                                    .keys
                                    .value
                                    .workAreas!,
                                style: regularTextStyle,
                              ),
                            ),
                            Text(
                              controller.provider.workAreas == null
                                  ? ''
                                  : controller.provider.workAreas!
                                      .map((e) => e.name)
                                      .toList()
                                      .join(','),
                              style: middleTextStyle,
                            ),
                          ],
                        ),
                      ),
                      // Row(
                      //   children: [
                      //     Container(
                      //       decoration: BoxDecoration(
                      //         borderRadius: BorderRadius.circular(5),
                      //         color: Colors.white,
                      //         boxShadow: [
                      //           BoxShadow(
                      //             color: Colors.grey[400]!,
                      //             blurRadius: 3,
                      //           ),
                      //         ],
                      //       ),
                      //       margin: const EdgeInsets.all(5),
                      //       padding: const EdgeInsets.all(10),
                      //       child: Row(
                      //         mainAxisAlignment: MainAxisAlignment.start,
                      //         children: [
                      //           Text(
                      //             Get.find<LanguageController>().keys.value.salary!,
                      //             style: regularTextStyle,
                      //           ),
                      //           Text(
                      //             '100 in hour',
                      //             style: middleTextStyle,
                      //           ),
                      //         ],
                      //       ),
                      //     ),
                      //     Expanded(child: Container()),
                      //   ],
                      // ),
                      Container(
                        padding: const EdgeInsets.all(10),
                        alignment: Alignment.centerLeft,
                        child: Text(
                          Get.find<LanguageController>()
                              .keys
                              .value
                              .workingTimes!,
                          style: big2TextStyle,
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.all(10),
                        margin: const EdgeInsets.all(10),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(15),
                          color: const Color(0xffF3F3F3),
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: Column(
                                children: [
                                  TextWithBackground(
                                    color: primaryColor,
                                    text: Get.find<LanguageController>()
                                        .keys
                                        .value
                                        .days!,
                                  ),
                                  for (int i = 0;
                                      i <
                                          controller
                                              .provider.workingTime!.length;
                                      i++)
                                    TextWithBackground(
                                      color: Colors.white,
                                      text: controller
                                          .provider.workingTime![i].day!,
                                      textColor: Colors.black,
                                    ),
                                ],
                              ),
                            ),
                            const SizedBox(width: 10),
                            Expanded(
                              child: Column(
                                children: [
                                  TextWithBackground(
                                    color: primaryColor,
                                    text: Get.find<LanguageController>()
                                        .keys
                                        .value
                                        .open!,
                                  ),
                                  for (int i = 0;
                                      i <
                                          controller
                                              .provider.workingTime!.length;
                                      i++)
                                    TextWithBackground(
                                      color: Colors.white,
                                      text: controller
                                          .provider.workingTime![i].startsAt!,
                                      textColor: Colors.black,
                                    ),
                                ],
                              ),
                            ),
                            SizedBox(width: 10.w),
                            Expanded(
                              child: Column(
                                children: [
                                  TextWithBackground(
                                    color: primaryColor,
                                    text: Get.find<LanguageController>()
                                        .keys
                                        .value
                                        .close!,
                                  ),
                                  for (int i = 0;
                                      i <
                                          controller
                                              .provider.workingTime!.length;
                                      i++)
                                    TextWithBackground(
                                      color: Colors.white,
                                      text: controller
                                          .provider.workingTime![i].endsAt!,
                                      textColor: Colors.black,
                                    ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.all(10),
                        alignment: Alignment.centerLeft,
                        child: Text(
                          Get.find<LanguageController>().keys.value.holidays!,
                          style: big2TextStyle,
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.all(10),
                        margin: const EdgeInsets.all(10),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(15),
                          color: const Color(0xffF3F3F3),
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: Column(
                                children: [
                                  TextWithBackground(
                                    color: primaryColor,
                                    text: Get.find<LanguageController>()
                                        .keys
                                        .value
                                        .from!,
                                  ),
                                  for (int i = 0;
                                      i < controller.provider.holidays!.length;
                                      i++)
                                    TextWithBackground(
                                      color: Colors.white,
                                      text: controller
                                          .provider.holidays![i].startsAt!,
                                      textColor: Colors.black,
                                    ),
                                ],
                              ),
                            ),
                            SizedBox(width: 10.w),
                            Expanded(
                              child: Column(
                                children: [
                                  TextWithBackground(
                                    color: primaryColor,
                                    text: Get.find<LanguageController>()
                                        .keys
                                        .value
                                        .to!,
                                  ),
                                  for (int i = 0;
                                      i < controller.provider.holidays!.length;
                                      i++)
                                    TextWithBackground(
                                      color: Colors.white,
                                      text: controller
                                          .provider.holidays![i].endsAt!,
                                      textColor: Colors.black,
                                    ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            Get.find<LanguageController>().keys.value.reviews!,
                            style: bigTextStyle,
                          ),
                          TextButton(
                            onPressed: () => Get.toNamed(
                              Routes.PROVIDER_REVIEWS,
                              arguments: {
                                'reviews': controller.provider.reviews,
                              },
                            ),
                            child: Text(
                              Get.find<LanguageController>()
                                  .keys
                                  .value
                                  .viewAll!,
                            ),
                          ),
                        ],
                      ),

                      // provider reviews
                      for (int i = 0;
                          i < controller.provider.reviews!.length;
                          i++)
                        ReviewWidget(
                          review: controller.provider.reviews![i],
                        ),

                      SizedBox(
                        height: 100.h,
                        child: ListView.builder(
                          scrollDirection: Axis.horizontal,
                          itemBuilder: (context, index) {
                            return ServiceWidget(
                              service: ProviderServices.fromJson(controller
                                  .provider.services![index]
                                  .toJson()),
                              onTap: () {
                                Get.toNamed(
                                  Routes.PROVIDER_PAGE,
                                  arguments: {
                                    'provider': controller.provider,
                                    'service': ProviderServices.fromJson(
                                        controller.provider.services![index]
                                            .toJson()),
                                  },
                                );
                              },
                            );
                          },
                          itemCount: controller.provider.services!.length,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
          Positioned(
            top: 70.h,
            right: 140.w,
            left: 140.w,
            child: CircleAvatar(
              radius: 70.sp,
              backgroundColor: primaryColor,
              child: CircleAvatar(
                backgroundColor: Colors.white,
                radius: 52.sp,
                backgroundImage: NetworkImage(controller.provider.image!),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
