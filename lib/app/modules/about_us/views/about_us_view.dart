import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/controllers/global_values_controller.dart';

import '../../../../global/constants/theme.dart';
import '../../../../global/controllers/language_controller.dart';
import '../controllers/about_us_controller.dart';

class AboutUsView extends GetView<AboutUsController> {
  const AboutUsView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return GetBuilder<GlobalValuesController>(builder: (controller) {
      return Scaffold(
        body: Container(
          width: Get.width,
          height: Get.height,
          padding: const EdgeInsets.all(10),
          decoration: const BoxDecoration(
            image: DecorationImage(
              image: AssetImage(
                'assets/images/main_background.png',
              ),
              fit: BoxFit.fill,
            ),
          ),
          child: SafeArea(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    IconButton(
                      onPressed: Get.back,
                      icon: const Icon(
                        CupertinoIcons.back,
                        size: 30,
                      ),
                    ),
                    Expanded(
                      child: Text(
                        Get.find<LanguageController>().keys.value.aboutUs!,
                        style: bigTextStyle,
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 20.h),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      height: Get.height * 0.5,
                      padding: const EdgeInsets.all(10),
                      margin: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        boxShadow: [
                          BoxShadow(
                            color: Colors.grey[300]!,
                            blurRadius: 5,
                          ),
                        ],
                        color: Colors.white,
                        border: Border.all(color: primaryColor),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: SingleChildScrollView(
                        child: Html(
                          data: controller.aboutUsModel.value.data!.text
                              .toString(),
                        ),
                      ),
                    ),
                    Text(
                      Get.find<LanguageController>()
                          .keys
                          .value
                          .forMoreInfoContactUs!,
                      style: big2TextStyle,
                    ),
                    SizedBox(height: 20.h),
                    Row(
                      children: [
                        const Icon(
                          Icons.email,
                        ),
                        SizedBox(width: 10.w),
                        Text(
                          Get.find<LanguageController>().keys.value.email!,
                          style: regularTextStyle,
                        ),
                      ],
                    ),
                    Text(
                      controller.contactInfoModel.value.data!.email!,
                      style: regularTextStyle,
                    ),
                    SizedBox(height: 20.h),
                    Row(
                      children: [
                        const Icon(
                          Icons.phone,
                        ),
                        SizedBox(width: 10.w),
                        Text(
                          Get.find<LanguageController>().keys.value.phone!,
                          style: regularTextStyle,
                        ),
                      ],
                    ),
                    Text(
                      controller.contactInfoModel.value.data!.phone!,
                      style: regularTextStyle,
                      textDirection: TextDirection.ltr,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      );
    });
  }
}
