import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/my_orders/controllers/my_booking_controller.dart';
import 'package:get_clean/app/modules/provider_offer_services/controllers/provider_offer_services_controller.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/controllers/language_controller.dart';
import 'package:get_clean/global/widget/bottom_navigation_bar.dart';

import '../../../../global/widget/order_widget.dart';

class ProviderOfferServicesView
    extends GetView<ProviderOfferServicesController> {
  const ProviderOfferServicesView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Get.put<MyBookingController>(
      MyBookingController(),
    );
    Get.put<ProviderOfferServicesController>(
      ProviderOfferServicesController(),
    );

    return Scaffold(
      bottomNavigationBar: const BottomNavBarWidget(),
      appBar: AppBar(
        title: Text(
            Get.find<LanguageController>().keys.value.myProviderOfferServices!),
        centerTitle: true,
      ),
      body: Container(
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              'assets/images/main_background_bottom.png',
            ),
            fit: BoxFit.fill,
          ),
        ),
        alignment: Alignment.center,
        padding: const EdgeInsets.all(10),
        child: Column(
          children: [
            //? Tab Bar
            SizedBox(
              height: 50.h,
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Container(
                  margin: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(5),
                    color: Colors.white,
                    border: Border.all(
                      color: primaryColor,
                    ),
                  ),
                  child: Obx(() {
                    return Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        InkWell(
                          onTap: () => controller.onChangeIndex(0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Container(
                                alignment: Alignment.center,
                                height: 35.h,
                                width: 100.w,
                                color: controller.tabIndex.value == 0
                                    ? primaryColor
                                    : Colors.white,
                                child: Text(
                                  Get.find<LanguageController>()
                                      .keys
                                      .value
                                      .pending!,
                                  style: TextStyle(
                                    fontSize: 16.sp,
                                    color: controller.tabIndex.value == 0
                                        ? Colors.white
                                        : primaryColor,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              Container(
                                width: 1.w,
                                height: 35.h,
                                color: primaryColor,
                              ),
                            ],
                          ),
                        ),
                        InkWell(
                          onTap: () => controller.onChangeIndex(1),
                          child: Row(
                            children: [
                              Container(
                                alignment: Alignment.center,
                                height: 35.h,
                                width: 140.w,
                                color: controller.tabIndex.value == 1
                                    ? primaryColor
                                    : Colors.white,
                                child: Text(
                                  Get.find<LanguageController>()
                                      .keys
                                      .value
                                      .waitingForVisit!,
                                  style: TextStyle(
                                    fontSize: 16.sp,
                                    color: controller.tabIndex.value == 1
                                        ? Colors.white
                                        : primaryColor,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ],
                          ),
                        ),
                        InkWell(
                          onTap: () => controller.onChangeIndex(2),
                          child: Row(
                            children: [
                              Container(
                                alignment: Alignment.center,
                                height: 35.h,
                                width: 140.w,
                                color: controller.tabIndex.value == 2
                                    ? primaryColor
                                    : Colors.white,
                                child: Text(
                                  Get.find<LanguageController>()
                                      .keys
                                      .value
                                      .unPaidOrders!,
                                  style: TextStyle(
                                    fontSize: 16.sp,
                                    color: controller.tabIndex.value == 2
                                        ? Colors.white
                                        : primaryColor,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    );
                  }),
                ),
              ),
            ),

            //! Orders
            Expanded(
              child: Obx(() {
                if (controller.loading.value) {
                  return const Center(
                    child: CircularProgressIndicator(),
                  );
                } else {
                  if (controller.tabIndex.value == 0) {
                    return ListView.builder(
                      itemBuilder: (context, index) {
                        return OrderWidget(
                          bookingData: controller.pendingOfferOrders[index],
                          isUser: true,
                          showPhone: true,
                          isOffer: true,
                        );
                      },
                      itemCount: controller.pendingOfferOrders.length,
                    );
                  } else if (controller.tabIndex.value == 1) {
                    return ListView.builder(
                      itemBuilder: (context, index) {
                        return OrderWidget(
                          bookingData: controller.waitingOfferOrders[index],
                          isUser: true,
                          showPhone: true,
                          isOffer: true,
                          isWaitingOffer: true,
                        );
                      },
                      itemCount: controller.waitingOfferOrders.length,
                    );
                  } else {
                    return ListView.builder(
                      itemBuilder: (context, index) {
                        return OrderWidget(
                          bookingData: controller.unPaidOfferOrders[index],
                          isUser: true,
                          showPhone: true,
                          isOffer: true,
                          isWaitingOffer: true,
                        );
                      },
                      itemCount: controller.unPaidOfferOrders.length,
                    );
                  }
                }
              }),
            ),
          ],
        ),
        // Column(
        //   crossAxisAlignment: CrossAxisAlignment.start,
        //   children: [
        // Expanded(
        //   child: ListView.builder(
        //     itemBuilder: (context, index) {
        //       return ProviderOfferServiceWidget(
        //         address:
        //             '${controller.providerOfferServices.data![index].address!.name!},${controller.providerOfferServices.data![index].address!.area!.name!}',
        //         name: controller.providerOfferServices.data![index].name,
        //         onBookPressed: () => controller
        //             .onProviderOfferServiceTapped(controller.providerOfferServices.data![index]),
        //         onProviderOfferServicePressed: () => controller
        //             .onProviderOfferServiceTapped(controller.providerOfferServices.data![index]),
        //         imageURL: controller.providerOfferServices.data![index].image,
        //         providerImage:
        //             controller.providerOfferServices.data![index].provider!.image!,
        //         providerName:
        //             controller.providerOfferServices.data![index].provider!.name!,
        //         rating: controller.providerOfferServices.data![index].provider!.rating!
        //             .toDouble(),
        //       );
        //     },
        //     itemCount: controller.providerOfferServices.data!.length,
        //   ),
        // ),

        // ],
        // ),
      ),
    );
  }
}
