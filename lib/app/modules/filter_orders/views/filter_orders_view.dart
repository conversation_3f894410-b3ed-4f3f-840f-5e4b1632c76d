import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/global/widget/custom_drop_down_button.dart';

import '../../../../global/constants/constants.dart';
import '../../../../global/controllers/language_controller.dart';
import '../../../../global/widget/order_widget.dart';
import '../controllers/filter_orders_controller.dart';

class FilterOrdersView extends GetView<FilterOrdersController> {
  const FilterOrdersView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          Get.find<LanguageController>().keys.value.filterOrders!,
        ),
        centerTitle: true,
      ),
      body: GetBuilder<FilterOrdersController>(
        builder: (controller) {
          if (controller.allDates.isEmpty) {
            return Container();
          }

          return Column(
            children: [
              CustomDropDownButton(
                hint: Get.find<LanguageController>().keys.value.date!,
                value: controller.choosedDate.value.isEmpty
                    ? null
                    : controller.choosedDate.value,
                onChanged: (value) =>
                    controller.onChangeDate(value!.toString()),
                items: controller.allDates
                    .map(
                      (element) => DropdownMenuItem<String>(
                        value: element,
                        child: Text(element),
                      ),
                    )
                    .toList(),
                buttonHeight: 50.0,
                buttonWidth: Get.width,
              ),
              SizedBox(
                height: 50.h,
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Container(
                    margin: const EdgeInsets.all(10),
                    // width: 304.w,
                    height: 35.h,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(5),
                      color: Colors.white,
                      border: Border.all(
                        color: primaryColor,
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        InkWell(
                          onTap: () => controller.changeChoosedOrderType(0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Container(
                                alignment: Alignment.center,
                                height: 35.h,
                                width: 100.w,
                                color: controller.choosedOrdersType.value == 0
                                    ? primaryColor
                                    : Colors.white,
                                child: Text(
                                  Get.find<LanguageController>()
                                      .keys
                                      .value
                                      .pending!,
                                  style: TextStyle(
                                    fontSize: 16.sp,
                                    color:
                                        controller.choosedOrdersType.value == 0
                                            ? Colors.white
                                            : primaryColor,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              Container(
                                width: 1.w,
                                height: 35.h,
                                color: primaryColor,
                              ),
                            ],
                          ),
                        ),
                        InkWell(
                          onTap: () => controller.changeChoosedOrderType(1),
                          child: Row(
                            children: [
                              Container(
                                alignment: Alignment.center,
                                height: 35.h,
                                width: 120.w,
                                color: controller.choosedOrdersType.value == 1
                                    ? primaryColor
                                    : Colors.white,
                                child: FittedBox(
                                  fit: BoxFit.scaleDown,
                                  child: Text(
                                    Get.find<LanguageController>()
                                        .keys
                                        .value
                                        .unPaidOrders!,
                                    style: TextStyle(
                                      fontSize: 16.sp,
                                      color:
                                          controller.choosedOrdersType.value ==
                                                  1
                                              ? Colors.white
                                              : primaryColor,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                              ),
                              Container(
                                width: 1.w,
                                height: 35.h,
                                color: primaryColor,
                              ),
                            ],
                          ),
                        ),
                        InkWell(
                          onTap: () => controller.changeChoosedOrderType(2),
                          child: Row(
                            children: [
                              Container(
                                alignment: Alignment.center,
                                height: 35.h,
                                width: 100.w,
                                color: controller.choosedOrdersType.value == 2
                                    ? primaryColor
                                    : Colors.white,
                                child: Text(
                                  Get.find<LanguageController>()
                                      .keys
                                      .value
                                      .accepted!,
                                  style: TextStyle(
                                    fontSize: 16.sp,
                                    color:
                                        controller.choosedOrdersType.value == 2
                                            ? Colors.white
                                            : primaryColor,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              Container(
                                width: 1.w,
                                height: 35.h,
                                color: primaryColor,
                              ),
                            ],
                          ),
                        ),
                        InkWell(
                          onTap: () => controller.changeChoosedOrderType(3),
                          child: Row(
                            children: [
                              Container(
                                alignment: Alignment.center,
                                height: 35.h,
                                width: 100.w,
                                color: controller.choosedOrdersType.value == 3
                                    ? primaryColor
                                    : Colors.white,
                                child: Text(
                                  Get.find<LanguageController>()
                                      .keys
                                      .value
                                      .oldOrders!,
                                  style: TextStyle(
                                    fontSize: 16.sp,
                                    color:
                                        controller.choosedOrdersType.value == 3
                                            ? Colors.white
                                            : primaryColor,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              Expanded(
                child: ListView.builder(
                  itemBuilder: (context, index) {
                    return OrderWidget(
                      showPhone: true,
                      bookingData: controller.filterBookings()[index],
                      isUser:
                          controller.isUser(controller.filterBookings()[index]),
                    );
                  },
                  itemCount: controller.filterBookings().length,
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
