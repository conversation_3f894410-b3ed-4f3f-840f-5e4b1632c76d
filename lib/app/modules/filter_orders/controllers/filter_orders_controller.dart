import 'package:get/get.dart';

import '../../../../global/models/user_booking.dart';
import '../../home/<USER>/home_controller.dart';
import '../../my_orders/controllers/my_orders_controller.dart';

class FilterOrdersController extends GetxController {
  final myOrdersController = Get.find<MyOrdersController>();
  final user = Get.find<HomeController>().user;
  final allDates = <String>[].obs;
  final choosedDate = ''.obs;
  final choosedOrdersType = 0.obs;

  @override
  void onInit() {
    super.onInit();
    getAllDates();
  }

  bool isUser(BookingData bookingData) {
    return bookingData.user!.id! == user.value.id!;
  }

  void getAllDates() {
    List<String> dates = [];
    for (var element in myOrdersController.myOrders.value.bookingData!) {
      dates.add(element.date ?? '');
    }

    allDates.value = dates.toSet().toList();
    choosedDate.value = allDates.first;
    update();
  }

  void onChangeDate(String value) {
    choosedDate.value = value;
    update();
  }

  void changeChoosedOrderType(int type) {
    choosedOrdersType.value = type;
    update();
  }

  List<BookingData> filterBookings() {
    //0522227534
    List<BookingData> data = [];

    if (choosedOrdersType.value == 0) {
      for (var element in myOrdersController.myOrders.value.bookingData ?? []) {
        if (element.isPending! && element.date == choosedDate.value) {
          data.add(element);
        }
      }
    } else if (choosedOrdersType.value == 1) {
      for (var element in myOrdersController.myOrders.value.bookingData ?? []) {
        if (element.isApproved! && element.date == choosedDate.value) {
          data.add(element);
        }
      }
    } else if (choosedOrdersType.value == 2) {
      for (var element in myOrdersController.myOrders.value.bookingData ?? []) {
        if (element.isConfirmed! && element.date == choosedDate.value) {
          data.add(element);
        }
      }
    } else {
      for (var element in myOrdersController.myOrders.value.bookingData ?? []) {
        if (!element.isPending! &&
            !element.isApproved! &&
            !element.isConfirmed! &&
            element.date == choosedDate.value) {
          data.add(element);
        }
      }
    }
    return data;
  }
}
