import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/payment/controllers/states/payment_states.dart';
import 'package:get_clean/global/widget/custom_button.dart';
import 'package:get_clean/global/widget/loading_widget.dart';

import '../../../../global/constants/constants.dart';
import '../../../../global/constants/theme.dart';
import '../../../../global/controllers/language_controller.dart';
import '../../my_booking_details/views/widgets/item_widget.dart';
import '../controllers/payment_controller.dart';

class PaymentView extends GetView<PaymentController> {
  const PaymentView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(Get.find<LanguageController>().keys.value.payment!),
        centerTitle: true,
      ),
      body: GetBuilder<PaymentController>(builder: (controller) {
        if (controller.state.value is PaymentLoadingState) {
          return const LoadingWidget();
        }

        log('asfasf ${controller.order.paidAmount}');

        final paidAmount = controller.order.paidAmount!;
        // (((controller.order.totalPrice!) - controller.order.paidAmount!) +
        //     controller.paidTips.value);

        return SingleChildScrollView(
          child: Column(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                margin: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(15),
                  border: Border.all(color: primaryColor, width: 0.5.w),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      Get.find<LanguageController>().keys.value.paymentDetails!,
                      style: bigTextStyle,
                    ),
                    const Divider(color: primaryColor),
                    ItemWidget(
                      title:
                          Get.find<LanguageController>().keys.value.finalPrice!,
                      description:
                          '${controller.order.totalPrice!.toStringAsFixed(2)} ${Get.find<LanguageController>().keys.value.ils!}',
                      showThird: true,
                    ),
                    ItemWidget(
                      title:
                          '${Get.find<LanguageController>().keys.value.deposit!} (${(controller.order.depositPercentage! * 100).toStringAsFixed(0)}%)',
                      description:
                          '${controller.order.deposit!.toStringAsFixed(2)} ${Get.find<LanguageController>().keys.value.ils!}',
                      showThird: true,
                      thirdTitle: controller.payDeposit ? '' : 'Done',
                    ),
                    ItemWidget(
                      title: Get.find<LanguageController>()
                          .keys
                          .value
                          .cleaningMaterial!,
                      description:
                          '${controller.order.materialPrice!.toStringAsFixed(2)} ${Get.find<LanguageController>().keys.value.ils!}',
                      showThird: true,
                      // thirdTitle: controller.payDeposit ? '' : 'Done',
                    ),
                    if (controller
                            .order.orderData!.bookingData!.pricingOption!.id ==
                        1)
                      ItemWidget(
                        title: Get.find<LanguageController>()
                            .keys
                            .value
                            .hoursNumber!,
                        description:
                            controller.order.duration!.toStringAsFixed(0) +
                                Get.find<LanguageController>().keys.value.hour!,
                        showThird: true,
                        // thirdTitle: 'Done',
                      ),
                    if (controller
                            .order.orderData!.bookingData!.pricingOption!.id ==
                        2)
                      ItemWidget(
                        title: Get.find<LanguageController>()
                            .keys
                            .value
                            .metersNumber!,
                        description: controller.order.duration!
                                .toStringAsFixed(0) +
                            Get.find<LanguageController>().keys.value.meter!,
                        showThird: true,
                        // thirdTitle: 'Done',
                      ),
                    if (controller
                            .order.orderData!.bookingData!.pricingOption!.id ==
                        1)
                      ItemWidget(
                        title: Get.find<LanguageController>()
                                .keys
                                .value
                                .subTotal ??
                            'Sub Total',
                        description:
                            controller.order.subTotal!.toStringAsFixed(2) +
                                Get.find<LanguageController>().keys.value.ils!,
                        showThird: true,
                        // thirdTitle: 'Done',
                      ),
                    if (controller
                            .order.orderData!.bookingData!.pricingOption!.id ==
                        2)
                      ItemWidget(
                        title: Get.find<LanguageController>()
                            .keys
                            .value
                            .meterPrice!,
                        description:
                            controller.order.unitPrice!.toStringAsFixed(2) +
                                Get.find<LanguageController>().keys.value.ils!,
                        showThird: true,
                        // thirdTitle: 'Done',
                      ),
                  ],
                ),
              ),
              if (!controller.payDeposit)
                Container(
                  margin: const EdgeInsets.all(10),
                  padding: const EdgeInsets.all(10),
                  child: Row(
                    children: [
                      Text(
                        "${Get.find<LanguageController>().keys.value.payTheRestOfTheAmount!} ${paidAmount.abs().toStringAsFixed(2)} ${Get.find<LanguageController>().keys.value.ils!}",
                        style: regularTextStyle,
                      ),
                    ],
                  ),
                ),
              if (controller.payDeposit)
                Container(
                  margin: const EdgeInsets.all(10),
                  padding: const EdgeInsets.all(10),
                  child: Row(
                    children: [
                      Text(
                        "${Get.find<LanguageController>().keys.value.payDeposit!} : ${controller.order.deposit!.toStringAsFixed(2)} ${Get.find<LanguageController>().keys.value.ils!}",
                        style: regularTextStyle,
                      ),
                    ],
                  ),
                ),
              Column(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  // if (controller.order.isConfirmed == true)
                  //   CustomFormField(
                  //     keyboardType: TextInputType.number,
                  //     onChanged: (value) {
                  //       controller.paidTips.value = double.parse(value);
                  //     },
                  //     hint: Get.find<LanguageController>().keys.value.tips!,
                  //   ),

                  if (paidAmount > 0)
                    CustomButton(
                      label: Get.find<LanguageController>().keys.value.next!,
                      onTap: () => controller.confirmPayment(),
                      height: 42.h,
                      width: 294.w,
                    ),

                  // CustomButton(
                  //   label: Get.find<LanguageController>().keys.value.addTip!,
                  //   onTap: controller.addTipAsUser,
                  //   height: 42.h,
                  //   width: 294.w,
                  // ),
                  CustomButton(
                    label: Get.find<LanguageController>().keys.value.cancel!,
                    onTap: () => Get.back(),
                    height: 42.h,
                    width: 294.w,
                  ),
                ],
              ),
            ],
          ),
        );
      }),
    );
  }
}
