import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/add_new_service/views/widgets/meter_widget.dart';
import 'package:get_clean/app/modules/add_new_service/views/widgets/sofa_widget.dart';
import 'package:get_clean/global/widget/custom_button.dart';
import 'package:get_clean/global/widget/custom_drop_down_button.dart';
import 'package:get_clean/global/widget/custom_form_field.dart';

import '../../../../global/constants/theme.dart';
import '../../../../global/controllers/language_controller.dart';
import '../controllers/add_new_service_controller.dart';
import 'widgets/car_widget.dart';
import 'widgets/offer_widget.dart';

class AddNewServiceView extends GetView<AddNewServiceController> {
  const AddNewServiceView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<AddNewServiceController>(builder: (controller) {
      final withPricingOptions = controller.choosedService.value.id != null &&
          controller.choosedService.value.pricingOption!.hasTypes!;

      return Scaffold(
        body: Container(
          width: Get.width,
          height: Get.height,
          padding: const EdgeInsets.all(10),
          decoration: const BoxDecoration(
            image: DecorationImage(
              image: AssetImage(
                'assets/images/main_background.png',
              ),
              fit: BoxFit.fill,
            ),
          ),
          child: SafeArea(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      IconButton(
                        onPressed: Get.back,
                        icon: const Icon(
                          CupertinoIcons.back,
                          size: 30,
                        ),
                      ),
                      Expanded(
                        child: Text(
                          Get.find<LanguageController>()
                              .keys
                              .value
                              .addNewService!,
                          style: bigTextStyle,
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      CustomDropDownButton(
                        label:
                            Get.find<LanguageController>().keys.value.service!,
                        hint:
                            Get.find<LanguageController>().keys.value.service!,
                        value: controller.choosedService.value.id == null
                            ? null
                            : controller.choosedService.value,
                        onChanged: controller.onChangeService,
                        items: controller.allServices.value.data!
                            .map((e) => DropdownMenuItem(
                                  value: e,
                                  child: Text(
                                    e.name!,
                                    style: regularTextStyle,
                                  ),
                                ))
                            .toList(),
                        buttonHeight: 40.h,
                        buttonWidth: Get.width * 0.6,
                      ),
                      const SizedBox(width: 10),
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.only(top: 20),
                          child: Row(
                            children: [
                              if (controller.choosedService.value.id != null)
                                const Icon(
                                  FontAwesomeIcons.solidCircle,
                                  size: 15,
                                ),
                              const SizedBox(width: 10),
                              Expanded(
                                child: Text(
                                  controller.choosedService.value.id == null
                                      ? ''
                                      : controller.choosedService.value
                                          .pricingOption!.name!,
                                  style: regularTextStyle,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                  if (!isCarService(
                          controller.choosedService.value.pricingOption?.id) &&
                      !isOffer(
                          controller.choosedService.value.pricingOption?.id) &&
                      !isClothesService(
                          controller.choosedService.value.pricingOption?.id))
                    CustomFormField(
                      label: Get.find<LanguageController>()
                          .keys
                          .value
                          .materialPrice!,
                      hint: Get.find<LanguageController>()
                          .keys
                          .value
                          .materialPrice!,
                      keyboardType: TextInputType.number,
                      controller: controller.materialPriceController,
                    ),

                  if (isClothesService(
                      controller.choosedService.value.pricingOption?.id))
                    // deliver switch
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          Get.find<LanguageController>().keys.value.deliver!,
                          style: big2TextStyle,
                        ),
                        CupertinoSwitch(
                          value: controller.isDeliver.value,
                          onChanged: controller.onDeliverChange,
                        ),
                      ],
                    ).paddingSymmetric(
                      horizontal: 12,
                      vertical: 5,
                    ),

                  if (withPricingOptions &&
                      !isCarService(
                          controller.choosedService.value.pricingOption?.id))
                    const SofaWidget(),
                  if (withPricingOptions &&
                      isCarService(
                          controller.choosedService.value.pricingOption?.id))
                    const CarWidget(),
                  // if (controller.choosedService.value.id != null &&
                  //     !controller
                  //         .choosedService.value.pricingOption!.hasTypes! &&
                  //     !isOffer(
                  //         controller.choosedService.value.pricingOption?.id))
                  //   const MetersWidget(),

                  if (isHoursService(
                          controller.choosedService.value.pricingOption?.id) ||
                      isMeterService(
                          controller.choosedService.value.pricingOption?.id))
                    const MetersWidget(),

                  // * Offer ================================
                  if (isOffer(
                      controller.choosedService.value.pricingOption?.id))
                    const OffersWidget(),

                  SizedBox(height: 30.h),

                  Center(
                    child: CustomButton(
                      label: Get.find<LanguageController>().keys.value.save!,
                      onTap: controller.addNewService,
                      height: 50.h,
                      width: Get.width * 0.5,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    });
  }
}

// SizedBox(height: 20.h),
// Row(
//   mainAxisAlignment: MainAxisAlignment.spaceAround,
//   children: [
//     Row(
//       children: [
//         Text(
//           Get.find<LanguageController>().keys.value.withTax!,
//           style: big2TextStyle,
//         ),
//         Radio(
//           activeColor: primaryColor,
//           value: true,
//           groupValue: controller.withTax.value,
//           onChanged: controller.onChangeWithTax,
//         ),
//       ],
//     ),
//     Row(
//       children: [
//         Text(
//           Get.find<LanguageController>()
//               .keys
//               .value
//               .withoutTax!,
//           style: big2TextStyle,
//         ),
//         Radio(
//           activeColor: primaryColor,
//           value: false,
//           groupValue: controller.withTax.value,
//           onChanged: controller.onChangeWithTax,
//         ),
//       ],
//     ),
//   ],
// ),
