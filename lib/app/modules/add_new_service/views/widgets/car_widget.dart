import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/add_new_service/views/widgets/add_service_form_field.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/controllers/language_controller.dart';
import 'package:get_clean/global/widget/text_with_background.dart';

import '../../controllers/add_new_service_controller.dart';

class CarWidget extends StatelessWidget {
  const CarWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<AddNewServiceController>(
      builder: (controller) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: controller.carList.length,
              itemBuilder: (context, index) {
                final car = controller.carList[index];
                return Container(
                  margin: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(15),
                    color: const Color(0xffF3F3F3),
                  ),
                  child: ExpansionTile(
                    initiallyExpanded: true,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(15),
                    ),
                    title: Text(car.name.text),
                    childrenPadding: const EdgeInsets.all(8),
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Column(
                              children: [
                                TextWithBackground(
                                  color: primaryColor,
                                  text: Get.find<LanguageController>()
                                      .keys
                                      .value
                                      .carType!,
                                ),
                                for (int i = 0; i < car.carServices.length; i++)
                                  AddServiceFormField(
                                    keyboardType: TextInputType.text,
                                    controller: car.carServices[i].name,
                                    active: false,
                                  ),
                              ],
                            ),
                          ),
                          SizedBox(width: 10.w),
                          Expanded(
                            child: Column(
                              children: [
                                Row(
                                  children: [
                                    Expanded(
                                      child: TextWithBackground(
                                        color: primaryColor,
                                        text: Get.find<LanguageController>()
                                            .keys
                                            .value
                                            .price!,
                                      ),
                                    ),
                                  ],
                                ),
                                for (int i = 0; i < car.carServices.length; i++)
                                  Row(
                                    children: [
                                      Expanded(
                                        child: AddServiceFormField(
                                          keyboardType: TextInputType.number,
                                          controller: car.carServices[i].price,
                                          active: true,
                                        ),
                                      ),
                                    ],
                                  ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                );
              },
            ),
          ],
        );
      },
    );
  }
}
