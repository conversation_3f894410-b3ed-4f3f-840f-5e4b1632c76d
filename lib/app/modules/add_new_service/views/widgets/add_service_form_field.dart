// ignore_for_file: prefer_typing_uninitialized_variables

import 'package:flutter/material.dart';

class AddServiceFormField extends StatelessWidget {
  final active, controller, onChanged, initialValue;
  final TextInputType keyboardType;
  final String label;

  const AddServiceFormField({
    Key? key,
    this.active = true,
    this.controller,
    this.onChanged,
    this.initialValue,
    required this.keyboardType,
    this.label = '',
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Colors.grey[500]!,
            blurRadius: 2,
          ),
        ],
        color: Colors.white,
      ),
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: TextFormField(
        keyboardType: keyboardType,
        initialValue: initialValue,
        enabled: active,
        controller: controller,
        onChanged: onChanged,
        decoration: InputDecoration(
          labelText: label,
          contentPadding: const EdgeInsets.symmetric(horizontal: 5),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10),
            borderSide: const BorderSide(
              style: BorderStyle.none,
              color: Colors.transparent,
            ),
            gapPadding: 5,
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10),
            borderSide: const BorderSide(
              style: BorderStyle.none,
              color: Colors.transparent,
            ),
            gapPadding: 5,
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10),
            borderSide: const BorderSide(
              style: BorderStyle.none,
              color: Colors.transparent,
            ),
            gapPadding: 5,
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10),
            borderSide: const BorderSide(
              style: BorderStyle.none,
              color: Colors.transparent,
            ),
            gapPadding: 5,
          ),
          disabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10),
            borderSide: const BorderSide(
              style: BorderStyle.none,
              color: Colors.transparent,
            ),
            gapPadding: 5,
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10),
            borderSide: const BorderSide(
              style: BorderStyle.none,
              color: Colors.transparent,
            ),
            gapPadding: 5,
          ),
        ),
      ),
    );
  }
}
