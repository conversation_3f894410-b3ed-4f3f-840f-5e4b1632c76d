import 'dart:developer';

import 'package:get_clean/global/dio/dio_helper.dart';
import 'package:get_clean/global/help_functions/help_functions.dart';
import 'package:get_clean/global/models/service_offer_model.dart';

import '../../../../global/constants/constants.dart';

class AddNewServiceProvider {
  DioHelper helper = DioHelper();

  Future<bool> addNewServiceWithoutType(data) async {
    try {
      final response =
          await helper.postData(addNewServiceWithoutTypesURL, data);
      if (response['success'] == true) {
        showSuccessToast(response['message']);
        return true;
      } else {
        log('RESPONSE $response');
        showErrorToast(response['message']);
        return false;
      }
    } catch (e) {
      log(e.toString());
      return false;
    }
  }

  Future<bool> addNewServiceWithTypes(data) async {
    try {
      final response = await helper.postData(addNewServiceWithTypesURL, data);
      log('Dataaa $data Res $response');

      // {service_id: 15, material_price: 0, with_tax: false,
      // subService4: [{3: 10},
      // {4: 20}, {5: 0}],
      // subService5: [{1: 0}, {2: 0},
      // {5: 0}], subService6: [{1: 0}, {2: 0}]}

      if (response['success'] == true) {
        showSuccessToast(response['message']);
        return true;
      } else {
        showErrorToast(response['message']);
        return false;
      }
    } catch (e) {
      log(e.toString());
      return false;
    }
  }

  //? get offer services
  Future<ServiceOffersModel> getOfferServices() async {
    try {
      final response = await helper.getData(getServiceOfferDetailsURL);

      log('Offer Services $response');

      if (response['success'] == true) {
        return ServiceOffersModel.fromJson(response);
      } else {
        showErrorToast(response['message']);
        return ServiceOffersModel.fromJson(response);
      }
    } catch (e) {
      log(e.toString());
      return ServiceOffersModel.fromJson({});
    }
  }

  //? add offer services
  Future<bool> addOfferServices(data) async {
    try {
      final response = await helper.postData(addServiceOfferURL, data);
      log('Add Offer Services $response');

      if (response['success'] == true) {
        showSuccessToast(response['message']);
        return true;
      } else {
        showErrorToast(response['message']);
        return false;
      }
    } catch (e) {
      log(e.toString());
      return false;
    }
  }
}
