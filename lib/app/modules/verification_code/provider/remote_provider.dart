import 'dart:developer';

import 'package:get_clean/app/modules/verification_code/controllers/states/verify_code_state.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/dio/dio_helper.dart';
import 'package:get_clean/global/help_functions/help_functions.dart';
import 'package:get_clean/global/models/user.dart';

class VerificationCodeRemoteProvider {
  DioHelper helper = DioHelper();

  Future<VerifyCodeState> sendCode(String code) async {
    try {
      final response = await helper.postData(verifyAccountURL, {
        "otp": code,
      });

      if (response['success'] == true) {
        return VerifyCodeSuccessState(User.fromJson(response));
      } else {
        showErrorToast(response['message']);
        return VerifyCodeFailedState(response['message']);
      }
    } catch (e) {
      log(e.toString());
      return VerifyCodeFailedState(e.toString());
    }
  }

  Future<VerifyCodeState> resendCode(String phone) async {
    try {
      final response = await helper.postData(
        resendOTPURL,
        {"email_phone": phone},
      );
      if (response['success'] == true) {
        return VerifyCodeSuccessState(User.fromJson(response));
      } else {
        showErrorToast(response['message']);
        return VerifyCodeFailedState(response['message']);
      }
    } catch (e) {
      log(e.toString());
      return VerifyCodeFailedState(e.toString());
    }
  }
}
