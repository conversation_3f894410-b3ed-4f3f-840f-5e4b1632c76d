import 'dart:developer';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/login/providers/local_provider.dart';
import 'package:get_clean/app/modules/verification_code/controllers/states/verify_code_state.dart';
import 'package:get_clean/app/modules/verification_code/provider/remote_provider.dart';
import 'package:get_clean/app/routes/app_pages.dart';

import '../../../../global/controllers/global_values_controller.dart';

class VerificationCodeController extends GetxController {
  final code = Get.arguments['code'];
  final phone = Get.arguments['phone'];
  final user = Get.arguments['user'];
  final otpCode = ''.obs;
  final verifyCodeState = VerifyCodeState().obs;
  final remoteProvider = VerificationCodeRemoteProvider();

  @override
  void onInit() {
    super.onInit();
    log(code.toString());
    log(user.toString());
  }

  void onSubmitPressed() async {
    verifyCodeState.value = VerifyCodeLoadingState();
    update();
    verifyCodeState.value = await remoteProvider.sendCode(otpCode.value);
    update();
    if (verifyCodeState.value is VerifyCodeSuccessState) {
      LocalLoginProvider().saveUserData(user);
      Get.find<GlobalValuesController>().setUserLoggedIn();
      Get.toNamed(
        Routes.CONGRATULATION_PAGE,
        arguments: {'user': user},
      );
    }
  }

  void resendCode() async {
    verifyCodeState.value = VerifyCodeLoadingState();
    update();
    verifyCodeState.value = await remoteProvider.resendCode(phone);
    log(verifyCodeState.value.user!.otp.toString());
    update();
  }

  void onChangeOTP(value) {
    otpCode.value = value;
    update();
  }
}
