import 'package:get_clean/global/models/user.dart';

class VerifyCodeState {
  User? user;
  String? errorMessage;
}

class VerifyCodeSuccessState extends VerifyCodeState {
  VerifyCodeSuccessState(User user) {
    this.user = user;
  }
}

class VerifyCodeFailedState extends VerifyCodeState {
  VerifyCodeFailedState(String errorMessage) {
    this.errorMessage = errorMessage;
  }
}

class VerifyCodeLoadingState extends VerifyCodeState {}
