import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/rate_provider/controllers/states/rate_provider_states.dart';
import 'package:get_clean/app/routes/app_pages.dart';
import 'package:get_clean/global/help_functions/help_functions.dart';
import '../../../../global/controllers/global_values_controller.dart';
import '../../../../global/models/user_booking.dart';
import '../provider/rate_provider_remote_provider.dart';

class RateProviderController extends GetxController {
  final BookingData bookingData = BookingData.fromJson(Get.arguments['order']);
  final state = RateProviderStates().obs;
  final remoteProvider = RateProviderRemoteProvider();

  double rate = 5.0;
  TextEditingController comment = TextEditingController();

  void rateProvider() async {
    state.value = RateProviderLoading();
    update();

    state.value = await remoteProvider.rateProvider(
        rate, comment.text, bookingData.id!.toInt());

    if (state.value is RateProviderSuccess) {
      await Get.find<GlobalValuesController>().getAllData();
      Get.offAllNamed(Routes.HOME);
    } else {
      showErrorToast(state.value.errorMessage!);
    }
  }

  void onRatingUpdate(value) {
    rate = value;
  }
}
