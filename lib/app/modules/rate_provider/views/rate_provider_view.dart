import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/rate_provider/controllers/states/rate_provider_states.dart';
import 'package:get_clean/app/routes/app_pages.dart';
import 'package:get_clean/global/constants/theme.dart';
import 'package:get_clean/global/widget/custom_button.dart';
import 'package:get_clean/global/widget/custom_form_field.dart';
import 'package:get_clean/global/widget/custom_rating_bar.dart';
import 'package:get_clean/global/widget/loading_widget.dart';

import '../../../../global/controllers/language_controller.dart';
import '../controllers/rate_provider_controller.dart';

class RateProviderView extends GetView<RateProviderController> {
  const RateProviderView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: Get.width,
        height: Get.height,
        padding: const EdgeInsets.all(10),
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              'assets/images/main_background.png',
            ),
            fit: BoxFit.fill,
          ),
        ),
        child: SafeArea(
          child: Builder(
            builder: (context) {
              if (controller.state.value is RateProviderLoading) {
                return const LoadingWidget();
              }
              return SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Image.asset(
                      'assets/images/star.png',
                      width: 100.w,
                      height: 100.h,
                      fit: BoxFit.fill,
                    ),
                    SizedBox(height: 20.h),
                    CircleAvatar(
                      radius: 32.sp,
                      backgroundImage:
                          NetworkImage(controller.bookingData.provider!.image!),
                    ),
                    Text(
                      controller.bookingData.provider!.name!,
                      style: regularTextStyle,
                    ),
                    SizedBox(height: 20.h),
                    CustomRatingBar(
                      iconSize: 30,
                      canChangeRate: false,
                      onRatingUpdate: controller.onRatingUpdate,
                    ),
                    SizedBox(height: 20.h),
                    Text(
                      Get.find<LanguageController>()
                          .keys
                          .value
                          .rateYourProvider!,
                      style: bigTextStyle,
                    ),
                    SizedBox(height: 20.h),
                    CustomFormField(
                      keyboardType: TextInputType.text,
                      controller: controller.comment,
                      label: Get.find<LanguageController>()
                          .keys
                          .value
                          .writeComment!,
                      hint: Get.find<LanguageController>()
                          .keys
                          .value
                          .writeComment!,
                      minLines: 3,
                      maxLines: 5,
                    ),
                    SizedBox(height: 20.h),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        CustomButton(
                          label:
                              Get.find<LanguageController>().keys.value.rate!,
                          onTap: controller.rateProvider,
                          height: 50.h,
                          width: 148.w,
                        ),
                        CustomButton(
                          label:
                              Get.find<LanguageController>().keys.value.cancel!,
                          onTap: () => Get.offAllNamed(Routes.HOME),
                          height: 50.h,
                          width: 148.w,
                        ),
                      ],
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}
