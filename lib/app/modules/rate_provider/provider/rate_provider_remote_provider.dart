import 'dart:developer';

import 'package:get_clean/app/modules/rate_provider/controllers/states/rate_provider_states.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/dio/dio_helper.dart';
import 'package:get_clean/global/help_functions/help_functions.dart';

class RateProviderRemoteProvider {
  DioHelper helper = DioHelper();

  Future<RateProviderStates> rateProvider(
      double rate, String comment, int orderID) async {
    try {
      final response = await helper.postData(
        rateBookingURL + orderID.toString(),
        {
          "rating": rate.toInt(),
          "comment": comment,
        },
      );
      log(response.toString());
      if (response['success'] == true) {
        showSuccessToast(response['message']);
        return RateProviderSuccess();
      } else {
        return RateProviderFailed(response['message']);
      }
    } catch (e) {
      return RateProviderFailed(e.toString());
    }
  }
}
