import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/home/<USER>/home_controller.dart';
import 'package:get_clean/app/modules/my_cart/controllers/my_cart_controller.dart';
import 'package:get_clean/app/modules/my_cart/views/cart/widgets/my_cart_provider_tabs.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/controllers/language_controller.dart';

class CartTabs extends StatelessWidget {
  const CartTabs({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (isProvider()) const CartProviderTabs(),
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Container(
            margin: const EdgeInsets.all(10),
            // height: 35.h,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(5),
              color: Colors.white,
              border: Border.all(
                color: primaryColor,
              ),
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CustomInkWellCart(
                      orderType: 0,
                      displayText:
                          Get.find<LanguageController>().keys.value.needAction!,
                    ),
                    CustomInkWellCart(
                      orderType: 1,
                      displayText:
                          Get.find<LanguageController>().keys.value.todo!,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

class CustomInkWellCart extends GetView<MyCartController> {
  final int orderType;
  final String displayText;
  final bool haveDivider;
  final bool isProviderOrderType;

  const CustomInkWellCart({
    super.key,
    required this.orderType,
    required this.displayText,
    this.haveDivider = false,
    this.isProviderOrderType = false,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      bool check = isProviderOrderType
          ? controller.choosedMainTabs.value == orderType
          : controller.choosedOrdersType.value == orderType;

      return InkWell(
        onTap: () {
          if (isProviderOrderType) {
            controller.changeProviderOrderType(orderType);
          } else {
            controller.changeChoosedOrderType(orderType);
          }
        },
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              alignment: Alignment.center,
              height: 35.h,
              padding: EdgeInsets.symmetric(horizontal: 10.w),
              color: check ? primaryColor : Colors.white,
              child: Text(
                displayText,
                style: TextStyle(
                  fontSize: 16.sp,
                  color: check ? Colors.white : primaryColor,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            if (haveDivider)
              Container(
                width: 1.w,
                height: 35.h,
                color: primaryColor,
              ),
          ],
        ),
      );
    });
  }
}
