import 'package:get/get.dart';
import 'package:get_clean/app/modules/my_cart/controllers/states/my_cart_states.dart';
import 'package:get_clean/app/modules/my_cart/provider/my_cart_remote_provider.dart';
import 'package:get_clean/global/models/user_booking.dart';

import '../../../../global/help_functions/help_functions.dart';
import '../../home/<USER>/home_controller.dart';

class MyCartController extends GetxController {
  final provider = MyCartRemoteProvider();
  final state = MyCartState().obs;
  final myCartNeedAction = UserBookings().obs;
  final myCartNeedTODO = UserBookings().obs;

  //! Provider
  final myCartProviderNeedAction = UserBookings().obs;
  final myCartProviderNeedTODO = UserBookings().obs;

  final user = Get.find<HomeController>().user;

  final choosedMainTabs = 0.obs; //? (MY Orders , My Booking)
  final choosedOrdersType = 0.obs; //? (Need Action , TODO)

  @override
  void onInit() {
    super.onInit();

    if (user.value.type == "provider") {
      getMyCartProvider();
    } else {
      getMyCartUser();
    }
  }

  bool isUser(BookingData bookingData) {
    return (bookingData.user?.id ?? 0) == user.value.id;
  }

  List<BookingData> filterCarts() {
    List<BookingData> data = [];

    // for (var element in myCartNeedAction.value.bookingData ?? <BookingData>[]) {
    //   if (choosedOrdersType.value == 0) {
    //     if (element.isPending!) {
    //       data.add(element);
    //     }
    //   } else if (choosedOrdersType.value == 1) {
    //     if (element.isApproved!) {
    //       data.add(element);
    //     }
    //   } else if (choosedOrdersType.value == 7) {
    //     if (!element.isPending! &&
    //         !element.isApproved! &&
    //         !element.isConfirmed! &&
    //         !(element.status == "waiting_dropdawn") &&
    //         !(element.status == "received") &&
    //         !(element.status == "waiting_approval") &&
    //         !(element.status == "deliver")) {
    //       data.add(element);
    //     }
    //   }
    // }

    if (isProvider()) {
      if (choosedMainTabs.value == 0) {
        if (choosedOrdersType.value == 0) {
          data = myCartProviderNeedAction.value.bookingData ?? [];
        } else if (choosedOrdersType.value == 1) {
          data = myCartProviderNeedTODO.value.bookingData ?? [];
        }
      } else {
        if (choosedOrdersType.value == 0) {
          data = myCartNeedAction.value.bookingData ?? [];
        } else if (choosedOrdersType.value == 1) {
          data = myCartNeedTODO.value.bookingData ?? [];
        }
      }
    } else {
      if (choosedOrdersType.value == 0) {
        data = myCartNeedAction.value.bookingData ?? [];
      } else if (choosedOrdersType.value == 1) {
        data = myCartNeedTODO.value.bookingData ?? [];
      }
    }

    return data;
  }

  final cartProvider = MyCartRemoteProvider();

  Future<List<BookingData>> getMyCartUser() async {
    state.value = MyCartLoadingState();
    update();

    state.value = await cartProvider.getUserMyCartNeedAction();

    final todoState = await cartProvider.getUserMyCartTODO();

    update();

    if (todoState is MyCartSuccessState) {
      final s = todoState;
      myCartNeedTODO.value = s.orders!;
    }

    if (state.value is MyCartSuccessState) {
      final s = state.value as MyCartSuccessState;
      myCartNeedAction.value = s.orders!;

      update();

      return s.orders?.bookingData ?? [];
    } else {
      final s = state.value as MyCartErrorState;
      showErrorToast(s.errorMessage!);

      return [];
    }
  }

  Future<List<BookingData>> getMyCartProvider() async {
    state.value = MyCartLoadingState();
    update();

    state.value = await cartProvider.getProviderMyCartNeedAction();

    final todoState = await cartProvider.getProviderMyCartTODO();

    update();

    if (todoState is MyCartSuccessState) {
      final s = todoState;
      myCartProviderNeedTODO.value = s.orders!;
    }

    if (state.value is MyCartSuccessState) {
      final s = state.value as MyCartSuccessState;
      myCartProviderNeedAction.value = s.orders!;

      update();

      return s.orders?.bookingData ?? [];
    } else {
      final s = state.value as MyCartErrorState;
      showErrorToast(s.errorMessage!);

      return [];
    }
  }

  void changeChoosedOrderType(int type) {
    choosedOrdersType.value = type;
    update();
  }

  void changeProviderOrderType(int type) {
    choosedMainTabs.value = type;
    update();
  }

  // group state
  final groupState = MyCartState().obs;
  final groupOrders = <String, UserBookings>{}.obs;

  Future<List<BookingData>> getFutureByGroupId(String groupId) async {
    groupState.value = MyCartLoadingState();
    update();

    groupState.value = await provider.getFutureByGroupId(groupId);
    update();

    if (groupState.value is MyCartSuccessState) {
      final s = groupState.value as MyCartSuccessState;
      // groupOrders.value = s.orders!;
      groupOrders[groupId] = s.orders!;
      update();

      return s.orders?.bookingData ?? [];
    } else {
      final s = groupState.value as MyCartErrorState;
      showErrorToast(s.errorMessage!);

      return [];
    }
  }
}
