import 'dart:developer';

import 'package:get/get.dart';
import 'package:get_clean/app/modules/favorites/provider/remote_provider.dart';
import 'package:get_clean/global/controllers/language_controller.dart';
import 'package:get_clean/global/help_functions/help_functions.dart';
import 'package:get_clean/global/models/provider.dart';
import 'package:get_clean/global/utils/dynamic_links.dart';
import 'package:share_plus/share_plus.dart';

class FavoriteController extends GetxController {
  final languageController = Get.find<LanguageController>();
  final provider = FavoriteRemoteProvider();

  final favoriteProviders = <Provider>[].obs;

  final loading = false.obs;

  @override
  void onInit() {
    super.onInit();

    favoriteProviders.value = [];

    getFavorites();
  }

  Future<void> getFavorites() async {
    try {
      loading.value = true;
      final response = await provider.getFavorite();
      if (response.success == true) {
        favoriteProviders.value = response.favoriteProviders;

        log('asfdsfas ${favoriteProviders.map((element) => element.name)}');
      }
    } catch (e) {
      log('ERROR IN GETTING FAVORITES $e');
    }

    loading.value = false;

    update();
  }

  bool isFavorite(int? id) =>
      favoriteProviders.any((element) => element.id == id);

  Future<bool> addToFavorite({
    required int? providerId,
  }) async {
    try {
      showWaitingIndicator();

      await provider.addNewFavorite(providerId: providerId);

      await getFavorites();

      hideWaitingIndicator();

      update();
      return true;
    } catch (e) {
      return false;
    } finally {}
  }

  Future<void> shareProvider({
    required Provider provider,
  }) async {
    try {
      showWaitingIndicator();

      // final String linkPathData = '?provider=${provider.toJson()}';

      final dynamicLink = await DynamicLinkHandler.createDynamicLink(
        provider.toParams(),
      );

      hideWaitingIndicator();

      Share.share(dynamicLink.toString());
    } catch (e) {}
  }
}
