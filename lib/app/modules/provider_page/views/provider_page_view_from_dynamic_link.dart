import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/provider_page/views/widgets/services/normal_service_widget.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/constants/theme.dart';
import 'package:get_clean/global/models/provider.dart';
import 'package:get_clean/global/widget/bottom_navigation_bar.dart';

import '../../../../global/controllers/language_controller.dart';
import '../controllers/provider_page_controller.dart';
import 'widgets/provider_albums.dart';

class ProviderPageFromDynamicLinkView extends GetView<ProviderPageController> {
  const ProviderPageFromDynamicLinkView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      bottomNavigationBar: const BottomNavBarWidget(),
      body: Stack(
        children: [
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            child: Container(
              alignment: Alignment.topLeft,
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Color(0xff001F57),
                    Color(0xff2955A1),
                    Color(0xff2B5AA5),
                    Color(0xff001F57),
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
              ),
              child: SafeArea(
                child: IconButton(
                  onPressed: Get.back,
                  icon: const Icon(
                    CupertinoIcons.back,
                    color: Colors.white,
                    size: 40,
                  ),
                ),
              ),
            ),
          ),
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              height: 710.h,
              width: Get.width,
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(45),
                  topRight: Radius.circular(45),
                ),
                image: DecorationImage(
                  image: AssetImage(
                    'assets/images/main_background.png',
                  ),
                  fit: BoxFit.fill,
                  opacity: 0.4,
                ),
              ),
              child: Padding(
                padding: EdgeInsets.only(
                  top: 63.h,
                  right: 10,
                  left: 10,
                  bottom: 10,
                ),
                child: SingleChildScrollView(
                  padding: const EdgeInsets.only(top: 80),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Center(
                        child: Text(
                          controller.provider.name ?? '',
                          style: big2TextStyle,
                        ),
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Image.asset(
                            'assets/images/position.png',
                          ),
                          SizedBox(width: 5.w),
                          Text(
                            controller.provider.city?.name ?? '',
                          ),
                          SizedBox(width: 10.w),
                          // Image.asset(
                          //   'assets/images/user.png',
                          // ),
                          // SizedBox(width: 5.w),
                          // Text(
                          //   'age : ${controller.provider.}',
                          // ),
                        ],
                      ),
                      Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(5),
                          color: Colors.white,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.grey[400]!,
                              blurRadius: 3,
                            ),
                          ],
                        ),
                        margin: const EdgeInsets.all(5),
                        padding: const EdgeInsets.all(10),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Text(
                              Get.find<LanguageController>().keys.value.skills!,
                              style: regularTextStyle,
                            ),
                            if (controller.provider.skills != null)
                              Text(
                                controller.provider.skills!
                                    .map((e) => e.name)
                                    .toList()
                                    .join(',')
                                    .removeBrackets(),
                                style: middleTextStyle,
                              ),
                          ],
                        ),
                      ),
                      Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(5),
                          color: Colors.white,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.grey[400]!,
                              blurRadius: 3,
                            ),
                          ],
                        ),
                        margin: const EdgeInsets.all(5),
                        padding: const EdgeInsets.all(10),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              Get.find<LanguageController>()
                                  .keys
                                  .value
                                  .workAreas!,
                              style: regularTextStyle,
                            ),
                            Expanded(
                              child: Text(
                                controller.provider.workAreas == null
                                    ? ''
                                    : controller.provider.workAreas!
                                        .map((e) => e.name)
                                        .toList()
                                        .join(',')
                                        .removeBrackets(),
                                style: middleTextStyle,
                              ),
                            ),
                          ],
                        ),
                      ),
                      // Row(
                      //   children: [
                      //     Container(
                      //       decoration: BoxDecoration(
                      //         borderRadius: BorderRadius.circular(5),
                      //         color: Colors.white,
                      //         boxShadow: [
                      //           BoxShadow(
                      //             color: Colors.grey[400]!,
                      //             blurRadius: 3,
                      //           ),
                      //         ],
                      //       ),
                      //       margin: const EdgeInsets.all(5),
                      //       padding: const EdgeInsets.all(10),
                      //       child: Row(
                      //         mainAxisAlignment: MainAxisAlignment.start,
                      //         children: [
                      //           Text(
                      //             Get.find<LanguageController>()
                      //                 .keys
                      //                 .value
                      //                 .salary!,
                      //             style: regularTextStyle,
                      //           ),
                      //           Text(
                      //             '100 in hour',
                      //             style: middleTextStyle,
                      //           ),
                      //         ],
                      //       ),
                      //     ),
                      //     Expanded(child: Container()),
                      //   ],
                      // ),

                      AlbumsWidget(
                        albums: controller.provider.albums,
                      ),

                      NormalServiceWidget(
                        controller: controller,
                      ),

                      // if (isCarService) ...[
                      //   CarServiceWidget(
                      //     controller: controller,
                      //   ),
                      // ],
                      //
                      // const SizedBox(height: 20),
                      // ],

                      // Row(
                      //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      //   children: [
                      //     Text(
                      //       Get.find<LanguageController>().keys.value.reviews!,
                      //       style: bigTextStyle,
                      //     ),
                      //     TextButton(
                      //       onPressed: () => Get.toNamed(
                      //         Routes.PROVIDER_REVIEWS,
                      //         arguments: {
                      //           'reviews': controller.provider.reviews,
                      //         },
                      //       ),
                      //       child: Text(
                      //         Get.find<LanguageController>()
                      //             .keys
                      //             .value
                      //             .viewAll!,
                      //       ),
                      //     ),
                      //   ],
                      // ),

                      // if (controller.provider.reviews != null)
                      //   // provider reviews
                      //   for (int i = 0;
                      //       i < controller.provider.reviews!.length;
                      //       i++)
                      //     ReviewWidget(
                      //       review: controller.provider.reviews![i],
                      //     ),
                    ],
                  ),
                ),
              ),
            ),
          ),
          Positioned(
            top: 70.h,
            right: 140.w,
            left: 140.w,
            child: CircleAvatar(
              radius: 70.sp,
              backgroundColor: primaryColor,
              child: CircleAvatar(
                backgroundColor: Colors.white,
                radius: 52.sp,
                backgroundImage: NetworkImage(controller.provider.image!),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
