import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/add_new_service/controllers/add_new_service_controller.dart';
import 'package:get_clean/app/modules/provider_page/controllers/provider_page_controller.dart';
import 'package:get_clean/app/modules/provider_page/views/widgets/how_many_clothes.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/constants/theme.dart';
import 'package:get_clean/global/controllers/language_controller.dart';
import 'package:get_clean/global/widget/custom_form_field.dart';
import 'package:get_clean/global/widget/main_date_widget.dart';
import 'package:intl/intl.dart';

import '../../choose_time.dart';
import '../../request_sheet_sofa_widget.dart';

class SelectProviderTimeBody extends GetView<ProviderPageController> {
  const SelectProviderTimeBody({super.key});

  @override
  Widget build(BuildContext context) {
    // final isHours = controller.serviceFromGlobal.value.pricingOption?.id == 1;

    log('aewfadfsa ${controller.serviceFromGlobal.value.toJson()}');

    return Column(
      children: [
        Container(
          alignment: Get.find<LanguageController>().isArabic
              ? Alignment.centerRight
              : Alignment.centerLeft,
          child: Text(
            Get.find<LanguageController>().keys.value.selectedDay!,
            style: big2TextStyle,
          ),
        ),

        SizedBox(
          height: 100,
          child: ListView.builder(
            controller: controller.scrollController,
            scrollDirection: Axis.horizontal,
            itemBuilder: (context, index) {
              final date = DateTime.now().add(Duration(days: index));

              return Obx(() => InkWell(
                    onTap: () => controller.changeSelectedDate(date),
                    child: Container(
                      width: 120,
                      alignment: Alignment.center,
                      padding: const EdgeInsets.all(10),
                      margin: const EdgeInsets.all(5),
                      decoration: BoxDecoration(
                        border: Border.all(color: primaryColor),
                        borderRadius: BorderRadius.circular(10),
                        color: controller.selectedDate.value.day == date.day &&
                                controller.selectedDate.value.month ==
                                    date.month
                            ? primaryColor
                            : Colors.white,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            DateFormat.d().format(date),
                            style:
                                controller.selectedDate.value.day == date.day &&
                                        controller.selectedDate.value.month ==
                                            date.month
                                    ? regularWhiteTextStyle
                                    : regularTextStyle,
                          ),
                          Text(
                            DateFormat.EEEE().format(date),
                            style:
                                controller.selectedDate.value.day == date.day &&
                                        controller.selectedDate.value.month ==
                                            date.month
                                    ? regularWhiteTextStyle
                                    : regularTextStyle,
                          ),
                        ],
                      ),
                    ),
                  ));
            },
            itemCount: 60,
          ),
        ),

        Obx(
          () => MainDateWidget(
            date:
                DateFormat('dd/MM/yyyy').format(controller.selectedDate.value),
          ),
        ),

        // Container(
        //   alignment: Alignment.center,
        //   child: Text(
        //     DateFormat.yMd().format(controller.selectedDate.value),
        //     style: big2TextStyle,
        //   ),
        // ),

        //! For Sofa
        if (controller.serviceFromGlobal.value.pricingOption?.id == 3) ...[
          const Divider(color: primaryColor),
          const RequestSheetSofaWidget(),
        ],

        // here we show how many hours for sofa
        if (isClothesService(
            controller.serviceFromGlobal.value.pricingOption?.id)) ...[
          const Divider(color: primaryColor),
          const HowManyClothes(),
        ],

        // here we show the select how many hours widget
        // if (isHours) ...[
        //   const Divider(color: primaryColor),
        //   const SelectHowManyHoursWidget(),
        // ],

        // here we show the select how many Meters widget
        if (controller.serviceFromGlobal.value.pricingOption?.id == 2)
          Row(
            children: [
              Expanded(
                child: CustomFormField(
                  // onChanged: (value) {
                  //   controller.onChangeHowManyMeters();
                  // },
                  keyboardType: TextInputType.number,
                  label:
                      Get.find<LanguageController>().keys.value.howManyMeters!,
                  hint:
                      Get.find<LanguageController>().keys.value.howManyMeters!,
                  controller: controller.howManyMeters,
                ),
              ),
              // ElevatedButton(
              //   onPressed: () {
              //     controller.onChangeHowManyMeters();
              //   },
              //   child: Text(
              //       Get.find<LanguageController>().keys.value.showTimes!),
              // ),
            ],
          ),

        const Divider(color: primaryColor),

        // choose available time
        Text(
          Get.find<LanguageController>().keys.value.whatTime!,
          style: big2TextStyle,
        ),

        const ChooseTime(),
      ],
    );
  }
}
