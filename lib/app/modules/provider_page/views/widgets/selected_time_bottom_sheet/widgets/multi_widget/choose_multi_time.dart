import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/provider_page/controllers/provider_page_controller.dart';
import 'package:get_clean/app/modules/provider_page/controllers/states/provider_page_state.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/constants/theme.dart';
import 'package:get_clean/global/controllers/language_controller.dart';
import 'package:intl/intl.dart';

class ChooseMultiTime extends StatelessWidget {
  final Rx<String>? selectedTime;

  const ChooseMultiTime({super.key, required this.selectedTime});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ProviderPageController>(builder: (controller) {
      //! Get working hours

      final currentWorkingDay = controller.provider.workingTime
          ?.firstWhereOrNull((element) =>
              element.day ==
              DateFormat('EEEE').format(DateTime.now()).toLowerCase());

      //! Get End Time
      final endsAt = currentWorkingDay?.endsAt;

      final bookingDays = controller.multiAvilableTimes.value.data?.bookingDays;

      //! Filter booking times
      final filteredBookingTimes =
          bookedTimes(bookingDays: bookingDays, endsAt: endsAt);

      if (controller.state.value is ProviderPageTimesLoadingState) {
        return Center(
          child: Text(
            Get.find<LanguageController>().keys.value.pleaseWait!,
            style: regularBlackTextStyle,
          ),
        );
      }

      return SizedBox(
        height: 100,
        child: controller.multiAvilableTimes.value.data == null
            ? Center(
                child: Text(
                  Get.find<LanguageController>().keys.value.noAvilableTimes!,
                  style: big2TextStyle,
                ),
              )
            : controller.multiAvilableTimes.value.data!.bookingDays!.isEmpty
                ? Center(
                    child: Text(
                      Get.find<LanguageController>()
                          .keys
                          .value
                          .noAvilableTimes!,
                      style: big2TextStyle,
                    ),
                  )
                : ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemBuilder: (context, index) {
                      return InkWell(
                        onTap: () => controller.onChangeMultiSelectedTime(
                          controller.multiAvilableTimes.value.data!
                              .bookingDays![0].bookingTimes![index],
                          selectedTime: selectedTime,
                        ),
                        child: Container(
                          width: 120,
                          alignment: Alignment.center,
                          padding: const EdgeInsets.all(10),
                          margin: const EdgeInsets.all(5),
                          decoration: BoxDecoration(
                            border: Border.all(color: primaryColor),
                            borderRadius: BorderRadius.circular(10),
                            color: selectedTime?.value ==
                                    controller.multiAvilableTimes.value.data!
                                        .bookingDays![0].bookingTimes![index]
                                ? primaryColor
                                : Colors.white,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                filteredBookingTimes![index],
                                style: selectedTime?.value ==
                                        filteredBookingTimes[index]
                                    ? regularWhiteTextStyle
                                    : regularTextStyle,
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                    itemCount: controller
                            .multiAvilableTimes.value.data!.bookingDays!.isEmpty
                        ? 0
                        : filteredBookingTimes?.length ?? 0,
                  ),
      );
    });
  }
}
