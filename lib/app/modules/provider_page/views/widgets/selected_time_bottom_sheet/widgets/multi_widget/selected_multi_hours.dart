import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/provider_page/controllers/provider_page_controller.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/constants/theme.dart';
import 'package:get_clean/global/controllers/language_controller.dart';

class SelectedMultiHowManyHours extends StatelessWidget {
  final Rx<int>? selectedHours;
  final Function? additionalOnChanged;

  const SelectedMultiHowManyHours(
      {super.key, required this.selectedHours, this.additionalOnChanged});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ProviderPageController>(builder: (controller) {
      final minHours = controller.provider.services
              ?.firstWhereOrNull(
                (service) => service.id == controller.service.id,
              )
              ?.minHours
              ?.toInt() ??
          2;

      return Container(
        decoration: BoxDecoration(
          border: Border.all(
            color: primaryColor,
            width: 0.5,
          ),
          borderRadius: BorderRadius.circular(25),
          color: Colors.white,
        ),
        child: Column(
          children: [
            Container(
              decoration: BoxDecoration(
                border: Border.all(
                  color: primaryColor,
                  width: 0.5,
                ),
                borderRadius: const BorderRadius.only(
                  topRight: Radius.circular(25),
                  topLeft: Radius.circular(25),
                ),
                color: primaryColor,
              ),
              alignment: Alignment.center,
              child: Text(
                Get.find<LanguageController>().keys.value.howManyHours!,
                style: regularWhiteTextStyle,
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                for (int i = minHours; i < minHours + 11; i++)
                  HookBuilder(builder: (context) {
                    useEffect(() {
                      if (i == minHours) {
                        WidgetsBinding.instance.addPostFrameCallback((_) {
                          controller.onSelectedHoursTap(i);
                        });
                      }
                      return () {};
                    }, const []);

                    return Expanded(
                      child: InkWell(
                        onTap: () {
                          controller.onSelectedMultiHoursTap(i,
                              howManyHours: selectedHours);

                          if (additionalOnChanged != null) {
                            additionalOnChanged!();
                          }
                        },
                        child: Container(
                          padding: const EdgeInsets.all(10),
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            borderRadius: i == minHours
                                ? BorderRadius.only(
                                    bottomLeft:
                                        Get.find<LanguageController>().isArabic
                                            ? const Radius.circular(0)
                                            : const Radius.circular(25),
                                    bottomRight:
                                        Get.find<LanguageController>().isArabic
                                            ? const Radius.circular(25)
                                            : const Radius.circular(0),
                                  )
                                : i == minHours + 10
                                    ? BorderRadius.only(
                                        bottomLeft:
                                            Get.find<LanguageController>()
                                                    .isArabic
                                                ? const Radius.circular(25)
                                                : const Radius.circular(0),
                                        bottomRight:
                                            Get.find<LanguageController>()
                                                    .isArabic
                                                ? const Radius.circular(0)
                                                : const Radius.circular(25),
                                      )
                                    : BorderRadius.circular(0),
                            color: selectedHours?.value == i
                                ? primaryColor
                                : Colors.white,
                          ),
                          child: Text(
                            '$i',
                            style: selectedHours?.value == i
                                ? middleWhiteTextStyle
                                : middleTextStyle,
                          ),
                        ),
                      ),
                    );
                  }),
              ],
            ),
          ],
        ),
      );
    });
  }
}
