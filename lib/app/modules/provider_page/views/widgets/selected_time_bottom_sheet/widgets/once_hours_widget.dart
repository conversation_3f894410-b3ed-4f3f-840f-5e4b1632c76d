import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/provider_page/controllers/provider_page_controller.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/constants/theme.dart';
import 'package:get_clean/global/controllers/language_controller.dart';
import 'package:get_clean/global/widget/main_date_widget.dart';
import 'package:intl/intl.dart';

import '../../choose_time.dart';
import '../../select_how_many_hours_widget.dart';

class OnceHoursWidget extends GetView<ProviderPageController> {
  const OnceHoursWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          alignment: Get.find<LanguageController>().isArabic
              ? Alignment.centerRight
              : Alignment.centerLeft,
          child: Text(
            Get.find<LanguageController>().keys.value.selectedDay!,
            style: big2TextStyle,
          ),
        ),

        SizedBox(
          height: 100,
          child: ListView.builder(
            controller: controller.scrollController,
            scrollDirection: Axis.horizontal,
            itemBuilder: (context, index) {
              final date = DateTime.now().add(Duration(days: index));

              return Obx(() => InkWell(
                    onTap: () => controller.changeSelectedDate(date),
                    child: Container(
                      width: 120,
                      alignment: Alignment.center,
                      padding: const EdgeInsets.all(10),
                      margin: const EdgeInsets.all(5),
                      decoration: BoxDecoration(
                        border: Border.all(color: primaryColor),
                        borderRadius: BorderRadius.circular(10),
                        color: controller.selectedDate.value.day == date.day &&
                                controller.selectedDate.value.month ==
                                    date.month
                            ? primaryColor
                            : Colors.white,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            DateFormat.d().format(date),
                            style:
                                controller.selectedDate.value.day == date.day &&
                                        controller.selectedDate.value.month ==
                                            date.month
                                    ? regularWhiteTextStyle
                                    : regularTextStyle,
                          ),
                          Text(
                            DateFormat.EEEE().format(date),
                            style:
                                controller.selectedDate.value.day == date.day &&
                                        controller.selectedDate.value.month ==
                                            date.month
                                    ? regularWhiteTextStyle
                                    : regularTextStyle,
                            maxLines: 1,
                          ),
                        ],
                      ),
                    ),
                  ));
            },
            itemCount: 60,
          ),
        ),

        Obx(
          () => MainDateWidget(
            date:
                DateFormat('dd/MM/yyyy').format(controller.selectedDate.value),
          ),
        ),
        // here we show the select how many hours widget
        const Divider(color: primaryColor),

        const SelectHowManyHoursWidget(),

        const Divider(color: primaryColor),

        // choose available time
        Text(
          Get.find<LanguageController>().keys.value.whatTime!,
          style: big2TextStyle,
        ),
        const ChooseTime(),
      ],
    );
  }
}
