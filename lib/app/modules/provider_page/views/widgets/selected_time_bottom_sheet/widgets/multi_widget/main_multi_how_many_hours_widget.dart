import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/provider_page/controllers/provider_page_controller.dart';
import 'package:get_clean/app/modules/provider_page/views/widgets/selected_time_bottom_sheet/widgets/multi_widget/choose_multi_time.dart';
import 'package:get_clean/app/modules/provider_page/views/widgets/selected_time_bottom_sheet/widgets/multi_widget/selected_multi_hours.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/constants/theme.dart';
import 'package:get_clean/global/controllers/language_controller.dart';
import 'package:get_clean/global/widget/main_date_widget.dart';
import 'package:intl/intl.dart';

class MainMultiHoursWidget extends GetView<ProviderPageController> {
  final bool isInitExpanded;
  final Function? additionalOnChanged;

  const MainMultiHoursWidget(
      {super.key, this.isInitExpanded = true, this.additionalOnChanged});

  @override
  Widget build(BuildContext context) {
    return Obx(() => ListView.separated(
          itemCount: controller.multiList.length,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          separatorBuilder: (context, index) =>
              const Divider(color: primaryColor),
          itemBuilder: (context, multiIndex) {
            return ExpansionTile(
              initiallyExpanded: isInitExpanded,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
              title: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Obx(() {
                    final formattedDate = DateFormat.yMd().format(
                        controller.multiList[multiIndex].selectedDate?.value ??
                            DateTime.now());

                    return Expanded(
                      child: Text(
                        '${Get.find<LanguageController>().keys.value.date!} ${multiIndex + 1}:\n$formattedDate ${controller.multiList[multiIndex].selectedTime?.value} - ${controller.multiList[multiIndex].howManyHours?.value} hours',
                        style: big2TextStyle,
                      ),
                    );
                  }),
                  CircleAvatar(
                    backgroundColor: Colors.red,
                    child: IconButton(
                      onPressed: () {
                        controller.onDeleteMultiList(multiIndex);
                      },
                      icon: const Icon(Icons.delete, color: Colors.white),
                    ),
                  ),
                ],
              ),
              children: [
                Column(
                  children: [
                    Container(
                      alignment: Get.find<LanguageController>().isArabic
                          ? Alignment.centerRight
                          : Alignment.centerLeft,
                      child: Text(
                        Get.find<LanguageController>().keys.value.selectedDay!,
                        style: big2TextStyle,
                      ),
                    ),

                    SizedBox(
                      height: 100,
                      child: ListView.builder(
                        controller: controller.scrollController,
                        scrollDirection: Axis.horizontal,
                        itemBuilder: (context, index) {
                          final date =
                              DateTime.now().add(Duration(days: index));

                          return Obx(() => InkWell(
                                onTap: () => controller.changeMultiSelectedDate(
                                    date, multiIndex),
                                child: Container(
                                  width: 120,
                                  alignment: Alignment.center,
                                  padding: const EdgeInsets.all(10),
                                  margin: const EdgeInsets.all(5),
                                  decoration: BoxDecoration(
                                    border: Border.all(color: primaryColor),
                                    borderRadius: BorderRadius.circular(10),
                                    color: controller.multiList[multiIndex]
                                                    .selectedDate?.value.day ==
                                                date.day &&
                                            controller
                                                    .multiList[multiIndex]
                                                    .selectedDate
                                                    ?.value
                                                    .month ==
                                                date.month
                                        ? primaryColor
                                        : Colors.white,
                                  ),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Text(
                                        DateFormat.d().format(date),
                                        style: controller
                                                        .multiList[multiIndex]
                                                        .selectedDate
                                                        ?.value
                                                        .day ==
                                                    date.day &&
                                                controller
                                                        .multiList[multiIndex]
                                                        .selectedDate
                                                        ?.value
                                                        .month ==
                                                    date.month
                                            ? regularWhiteTextStyle
                                            : regularTextStyle,
                                      ),
                                      Text(
                                        DateFormat.EEEE().format(date),
                                        style: controller
                                                        .multiList[multiIndex]
                                                        .selectedDate
                                                        ?.value
                                                        .day ==
                                                    date.day &&
                                                controller
                                                        .multiList[multiIndex]
                                                        .selectedDate
                                                        ?.value
                                                        .month ==
                                                    date.month
                                            ? regularWhiteTextStyle
                                            : regularTextStyle,
                                      ),
                                    ],
                                  ),
                                ),
                              ));
                        },
                        itemCount: 60,
                      ),
                    ),

                    Obx(
                      () => MainDateWidget(
                        date: DateFormat('dd/MM/yyyy').format(controller
                                .multiList[multiIndex].selectedDate?.value ??
                            DateTime.now()),
                      ),
                    ),

                    // here we show the select how many hours widget
                    const Divider(color: primaryColor),

                    SelectedMultiHowManyHours(
                        selectedHours:
                            controller.multiList[multiIndex].howManyHours,
                        additionalOnChanged: additionalOnChanged),

                    const Divider(color: primaryColor),

                    // choose available time
                    Text(
                      Get.find<LanguageController>().keys.value.whatTime!,
                      style: big2TextStyle,
                    ),
                    ChooseMultiTime(
                      selectedTime:
                          controller.multiList[multiIndex].selectedTime,
                    ),
                  ],
                )
              ],
            );
          },
        ));
  }
}
