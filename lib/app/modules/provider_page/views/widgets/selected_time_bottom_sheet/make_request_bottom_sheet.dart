import 'dart:developer';

import 'package:calendar_date_picker2/calendar_date_picker2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/provider_page/controllers/provider_page_controller.dart';
import 'package:get_clean/app/modules/provider_page/views/widgets/selected_time_bottom_sheet/widgets/select_hours_widget.dart';
import 'package:get_clean/app/modules/provider_page/views/widgets/selected_time_bottom_sheet/widgets/select_provider_time_body.dart';
import 'package:get_clean/global/controllers/language_controller.dart';
import 'package:get_clean/global/widget/custom_button.dart';

import '../../../../../../global/constants/constants.dart';
import '../../../../../../global/constants/theme.dart';
import '../../../../service_providers/controllers/service_providers_controller.dart';
import '../../../../service_providers/views/widgets/area_and_city_widget.dart';

class SelectProviderTimeBottomSheet extends StatelessWidget {
  const SelectProviderTimeBottomSheet({super.key});

  @override
  Widget build(BuildContext context) {
    Get.lazyPut<ServiceProvidersController>(
      () => ServiceProvidersController(),
      fenix: true,
    );

    return GetBuilder<ProviderPageController>(builder: (controller) {
      controller.onInitBottomSheet();

      final isHours = controller.serviceFromGlobal.value.pricingOption?.id == 1;

      log('asfafsgggaf ${isHours}');

      final isEng =
          Get.find<LanguageController>().selectedLanguage.value.slug == 'en';

      final isMulti = controller.tabIndex.value == 1;

      return Container(
        padding: const EdgeInsets.all(10),
        height: Get.height * 0.85,
        decoration: const BoxDecoration(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(45),
            topRight: Radius.circular(45),
          ),
          color: Colors.white,
        ),
        child: SingleChildScrollView(
          child: Column(
            children: [
              Container(
                decoration: BoxDecoration(
                  color: primaryColor,
                  borderRadius: BorderRadius.circular(10),
                ),
                margin: const EdgeInsets.all(10),
                height: 3,
                width: Get.width * 0.3,
              ),
              Text(
                controller.service.service?.name ?? '',
                style: bigTextStyle,
              ),

              const FilteredAreaAndCityProvidersWidget(),

              SizedBox(
                height: 10.h,
              ),

              if (isHours)
                Obx(() => Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        InkWell(
                          onTap: () => controller.onChangeIndex(0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Container(
                                alignment: Alignment.center,
                                height: 35.h,
                                width: 100.w,
                                decoration: BoxDecoration(
                                  borderRadius: isEng
                                      ? const BorderRadius.only(
                                          topLeft: Radius.circular(5),
                                          bottomLeft: Radius.circular(5),
                                        )
                                      : const BorderRadius.only(
                                          topRight: Radius.circular(5),
                                          bottomRight: Radius.circular(5),
                                        ),
                                  color: controller.tabIndex.value == 0
                                      ? primaryColor
                                      : Colors.blueGrey.shade50,
                                ),
                                child: Text(
                                  Get.find<LanguageController>()
                                      .keys
                                      .value
                                      .onece!,
                                  style: TextStyle(
                                    fontSize: 16.sp,
                                    color: controller.tabIndex.value == 0
                                        ? Colors.blueGrey.shade50
                                        : primaryColor,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              Container(
                                width: 1.w,
                                height: 35.h,
                                color: primaryColor,
                              ),
                            ],
                          ),
                        ),
                        InkWell(
                          onTap: () => controller.onChangeIndex(1),
                          child: Row(
                            children: [
                              Container(
                                alignment: Alignment.center,
                                height: 35.h,
                                width: 100.w,
                                decoration: BoxDecoration(
                                  borderRadius: isEng
                                      ? const BorderRadius.only(
                                          topRight: Radius.circular(5),
                                          bottomRight: Radius.circular(5),
                                        )
                                      : const BorderRadius.only(
                                          topLeft: Radius.circular(5),
                                          bottomLeft: Radius.circular(5),
                                        ),
                                  color: controller.tabIndex.value == 1
                                      ? primaryColor
                                      : Colors.blueGrey.shade50,
                                ),
                                child: Text(
                                  Get.find<LanguageController>()
                                                  .keys
                                                  .value
                                                  .multi ==
                                              null ||
                                          Get.find<LanguageController>()
                                              .keys
                                              .value
                                              .multi!
                                              .isEmpty
                                      ? "Multi"
                                      : Get.find<LanguageController>()
                                          .keys
                                          .value
                                          .multi!,
                                  style: TextStyle(
                                    fontSize: 16.sp,
                                    color: controller.tabIndex.value == 1
                                        ? Colors.white
                                        : primaryColor,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ],
                          ),
                        ),
                        if (controller.tabIndex.value == 1)
                          Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 4.0),
                            child: IconButton(
                              icon: const Icon(Icons.calendar_today),
                              onPressed: () async {
                                final picked = showDialog(
                                  context: context,
                                  builder: (_) {
                                    return Dialog(
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(20),
                                      ),
                                      child: CalendarDatePicker2(
                                        config: CalendarDatePicker2Config(
                                          disableMonthPicker: true,
                                          disableModePicker: true,
                                          calendarType:
                                              CalendarDatePicker2Type.multi,
                                          selectedDayHighlightColor:
                                              Colors.indigo,
                                          selectableDayPredicate: (date) {
                                            return controller.multiList
                                                .any((multiList) {
                                              return multiList.selectedDate
                                                          ?.value.day ==
                                                      date.day &&
                                                  multiList.selectedDate?.value
                                                          .month ==
                                                      date.month &&
                                                  multiList.selectedDate?.value
                                                          .year ==
                                                      date.year;
                                            });
                                          },
                                        ),
                                        value: controller.multiList
                                            .map((e) => e.selectedDate!.value)
                                            .toList(),
                                        onValueChanged: null,
                                      ),
                                    );
                                  },
                                );
                                // final DateTime? picked = await showDatePicker(
                                //   context: context,
                                //   initialDate: controller
                                //           .multiList[0].selectedDate?.value ??
                                //       DateTime.now(),
                                //   firstDate: controller
                                //           .multiList[0].selectedDate?.value ??
                                //       DateTime.now(),
                                //   lastDate: controller
                                //           .multiList[
                                //               controller.multiList.length - 1]
                                //           .selectedDate
                                //           ?.value ??
                                //       DateTime.now(),
                                //   selectableDayPredicate: (date) {
                                //     return controller.multiList
                                //         .any((multiList) {
                                //       return multiList
                                //                   .selectedDate?.value.day ==
                                //               date.day &&
                                //           multiList.selectedDate?.value.month ==
                                //               date.month &&
                                //           multiList.selectedDate?.value.year ==
                                //               date.year;
                                //     });
                                //   },
                                // );
                                if (picked != null) {
                                  // Do something with the picked date if needed
                                }
                              },
                            ),
                          ),
                      ],
                    )),

              // * Select Hours Or Any Other Widget ========================
              if (isHours)
                const SelectHoursWidget()
              else
                const SelectProviderTimeBody(),

              // * Submit Button ========================
              if (controller.avilableTimes.value.data != null &&
                  controller
                      .avilableTimes.value.data!.bookingDays!.isNotEmpty &&
                  controller.avilableTimes.value.data!.bookingDays![0]
                      .bookingTimes!.isNotEmpty &&
                  controller.howManyMeters.value.text.isNotEmpty &&
                  !isMulti)
                CustomButton(
                  label: Get.find<LanguageController>().keys.value.done!,
                  onTap: controller.onOkSend,
                  height: 50,
                  width: Get.width * 0.8,
                ),
              // * Submit Button Multi========================
              if (controller.multiAvilableTimes.value.data != null &&
                  controller
                      .multiAvilableTimes.value.data!.bookingDays!.isNotEmpty &&
                  controller.multiAvilableTimes.value.data!.bookingDays![0]
                      .bookingTimes!.isNotEmpty &&
                  controller.howManyMeters.value.text.isNotEmpty &&
                  isMulti)
                CustomButton(
                  label: Get.find<LanguageController>().keys.value.done!,
                  onTap: controller.onOkSend,
                  height: 50,
                  width: Get.width * 0.8,
                ),

              // * Keyboard Visibility ========================
              KeyboardVisibilityBuilder(builder: (context, isKeyboardVisible) {
                return SizedBox(
                    height: isKeyboardVisible ? context.height / 2 : 0);
              }),
            ],
          ),
        ),
      );
    });
  }
}
