import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/provider_page/controllers/provider_page_controller.dart';
import 'package:get_clean/global/models/provider_services.dart';

import '../../../../../global/constants/constants.dart';
import '../../../../../global/constants/theme.dart';
import '../../../../../global/controllers/language_controller.dart';
import '../../../add_new_service/views/widgets/add_service_form_field.dart';

class HowManyClothes extends HookWidget {
  const HowManyClothes({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<ProviderPageController>();
    final services = useState<List<PricingList?>>([]);

    useEffect(() {
      final providerServices = controller.provider.services!
          .firstWhereOrNull((element) => element.id == controller.service.id)!
          .pricingList!
          .where((element) => element.price != null && element.price != 0)
          .toList();

      if (providerServices.isNotEmpty) {
        services.value = [providerServices.firstOrNull];
      }

      return () {};
    }, const []);

    return StatefulBuilder(builder: (context, setState) {
      return Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(25),
          color: Colors.white,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Container(
              decoration: BoxDecoration(
                border: Border.all(
                  color: primaryColor,
                  width: 0.5,
                ),
                borderRadius: const BorderRadius.only(
                  topRight: Radius.circular(25),
                  topLeft: Radius.circular(25),
                ),
                color: primaryColor,
              ),
              alignment: Alignment.center,
              child: Text(
                Get.find<LanguageController>().keys.value.sofas!,
                style: regularWhiteTextStyle,
              ),
            ),
            Row(
              children: [
                Expanded(
                  child: Column(
                    children: services.value.map((service) {
                      return AddServiceFormField(
                        keyboardType: TextInputType.text,
                        initialValue: service?.typeModel?.name.text ?? '',
                        active: false,
                      );
                    }).toList(),
                  ),
                ),
                SizedBox(width: 10.w),
                Expanded(
                  child: Column(
                    children: services.value.indexed.map((service) {
                      return Row(
                        children: [
                          Expanded(
                            child: AddServiceFormField(
                              keyboardType: TextInputType.number,
                              onChanged: (value) =>
                                  controller.onChangedHowManyCloth(
                                      int.tryParse(value) ?? 0,
                                      service.$2?.typeModel?.id ?? 0),
                              active: true,
                            ),
                          ),
                          if (service.$1 !=
                              0) // Do not show remove button for the first row
                            IconButton(
                              icon: const Icon(Icons.remove),
                              onPressed: () {
                                services.value.removeAt(service.$1);

                                setState(() {});
                              },
                            ),
                        ],
                      );
                    }).toList(),
                  ),
                ),
              ],
            ),
            TextButton(
              onPressed: () {
                final providerServices = controller.provider.services!
                    .firstWhereOrNull(
                        (element) => element.id == controller.service.id)!
                    .pricingList!
                    .where((element) =>
                        element.price != null && element.price != 0)
                    .toList();

                showDialog(
                  context: context,
                  builder: (BuildContext context) {
                    return AlertDialog(
                      title:
                          Text(Get.find<LanguageController>().keys.value.add!),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(25),
                      ),
                      content: SizedBox(
                        height: 200.h,
                        width: double.maxFinite,
                        child: ListView.builder(
                          shrinkWrap: true,
                          itemCount: providerServices.length,
                          itemBuilder: (BuildContext context, int index) {
                            return ListTile(
                              enabled: !services.value
                                  .contains(providerServices[index]),
                              title: Text(providerServices[index]
                                      .typeModel
                                      ?.name
                                      .text ??
                                  ''),
                              onTap: () {
                                if (services.value.length <
                                    providerServices.length) {
                                  services.value = [
                                    ...services.value,
                                    providerServices[index]
                                  ];
                                }

                                Navigator.of(context).pop();
                              },
                            );
                          },
                        ),
                      ),
                    );
                  },
                );
              },
              child: Text(Get.find<LanguageController>().keys.value.add!),
            ),
          ],
        ),
      );
    });
  }
}
