import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/provider_page/controllers/provider_page_controller.dart';

import '../../../../../global/constants/constants.dart';
import '../../../../../global/constants/theme.dart';
import '../../../../../global/controllers/language_controller.dart';

class SelectHowManyHoursWidget extends StatelessWidget {
  const SelectHowManyHoursWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ProviderPageController>(builder: (controller) {
      final minHours = controller.provider.services
              ?.firstWhereOrNull(
                (service) => service.id == controller.service.id,
              )
              ?.minHours
              ?.toInt() ??
          2;

      return Container(
        decoration: BoxDecoration(
          border: Border.all(
            color: primaryColor,
            width: 0.5,
          ),
          borderRadius: BorderRadius.circular(25),
          color: Colors.white,
        ),
        child: Column(
          children: [
            Container(
              decoration: BoxDecoration(
                border: Border.all(
                  color: primaryColor,
                  width: 0.5,
                ),
                borderRadius: const BorderRadius.only(
                  topRight: Radius.circular(25),
                  topLeft: Radius.circular(25),
                ),
                color: primaryColor,
              ),
              alignment: Alignment.center,
              child: Text(
                Get.find<LanguageController>().keys.value.howManyHours!,
                style: regularWhiteTextStyle,
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                for (int i = minHours; i <= minHours + 10; i++)
                  HookBuilder(builder: (context) {
                    useEffect(() {
                      if (i == minHours) {
                        WidgetsBinding.instance.addPostFrameCallback((_) {
                          controller.onSelectedHoursTap(i);
                        });
                      }
                      return () {};
                    }, const []);

                    return Expanded(
                      child: InkWell(
                        onTap: () => controller.onSelectedHoursTap(i),
                        child: Container(
                          padding: const EdgeInsets.all(10),
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            borderRadius: i == minHours
                                ? BorderRadius.only(
                                    bottomRight:
                                        Get.find<LanguageController>().isArabic
                                            ? const Radius.circular(25)
                                            : const Radius.circular(0),
                                    bottomLeft:
                                        Get.find<LanguageController>().isArabic
                                            ? const Radius.circular(0)
                                            : const Radius.circular(25),
                                  )
                                : i == minHours + 10
                                    ? BorderRadius.only(
                                        bottomRight:
                                            Get.find<LanguageController>()
                                                    .isArabic
                                                ? const Radius.circular(0)
                                                : const Radius.circular(25),
                                        bottomLeft:
                                            Get.find<LanguageController>()
                                                    .isArabic
                                                ? const Radius.circular(25)
                                                : const Radius.circular(0),
                                      )
                                    : BorderRadius.circular(0),
                            color: controller.howManyHours.value == i
                                ? primaryColor
                                : Colors.white,
                          ),
                          child: Text(
                            '$i',
                            style: controller.howManyHours.value == i
                                ? middleWhiteTextStyle
                                : middleTextStyle,
                          ),
                        ),
                      ),
                    );
                  }),
              ],
            ),
          ],
        ),
      );
    });
  }
// Widget build(BuildContext context) {
//   return GetBuilder<ProviderPageController>(builder: (controller) {
//     final minHours = controller.provider.services
//             ?.firstWhereOrNull(
//               (service) => service.id == controller.service.id,
//             )
//             ?.minHours ??
//         1;
//
//     return Container(
//       decoration: BoxDecoration(
//         border: Border.all(
//           color: primaryColor,
//           width: 0.5,
//         ),
//         borderRadius: BorderRadius.circular(25),
//         color: Colors.white,
//       ),
//       child: Column(
//         children: [
//           Container(
//             decoration: BoxDecoration(
//               border: Border.all(
//                 color: primaryColor,
//                 width: 0.5,
//               ),
//               borderRadius: const BorderRadius.only(
//                 topRight: Radius.circular(25),
//                 topLeft: Radius.circular(25),
//               ),
//               color: primaryColor,
//             ),
//             alignment: Alignment.center,
//             child: Text(
//               Get.find<LanguageController>().keys.value.howManyHours!,
//               style: regularWhiteTextStyle,
//             ),
//           ),
//           Row(
//             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//             children: [
//               for (int i = 1; i < 10; i++)
//                 Expanded(
//                   child: InkWell(
//                     onTap: () => controller.onSelectedHoursTap(i + 1),
//                     child: Container(
//                       alignment: Alignment.center,
//                       decoration: BoxDecoration(
//                         borderRadius: i == 9
//                             ? BorderRadius.only(
//                                 bottomLeft:
//                                     Get.find<LanguageController>().isArabic
//                                         ? const Radius.circular(25)
//                                         : const Radius.circular(0),
//                                 bottomRight:
//                                     Get.find<LanguageController>().isArabic
//                                         ? const Radius.circular(0)
//                                         : const Radius.circular(25),
//                               )
//                             : i == 1
//                                 ? BorderRadius.only(
//                                     bottomLeft: Get.find<LanguageController>()
//                                             .isArabic
//                                         ? const Radius.circular(0)
//                                         : const Radius.circular(25),
//                                     bottomRight:
//                                         Get.find<LanguageController>()
//                                                 .isArabic
//                                             ? const Radius.circular(25)
//                                             : const Radius.circular(0),
//                                   )
//                                 : BorderRadius.circular(0),
//                         color: controller.howManyHours.value == i + 1
//                             ? primaryColor
//                             : Colors.white,
//                       ),
//                       padding: const EdgeInsets.all(6),
//                       child: Text(
//                         '${i + 1} ${Get.find<LanguageController>().keys.value.hour!}',
//                         style: controller.howManyHours.value == i + 1
//                             ? middleWhiteTextStyle
//                             : middleTextStyle,
//                       ),
//                     ),
//                   ),
//                 ),
//             ],
//           ),
//         ],
//       ),
//     );
//   });
// }
}
