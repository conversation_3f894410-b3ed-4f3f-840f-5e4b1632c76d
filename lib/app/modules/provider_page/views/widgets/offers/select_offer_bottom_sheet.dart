import 'package:flutter/material.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/provider_page/controllers/provider_page_controller.dart';
import 'package:get_clean/app/modules/provider_page_filtered/controllers/provider_page_filtered_controller.dart';
import 'package:get_clean/app/modules/provider_page_filtered/views/widgets/map_location_picker.dart';
import 'package:get_clean/app/modules/service_providers/controllers/service_providers_controller.dart';
import 'package:get_clean/app/modules/service_providers/views/widgets/area_and_city_widget.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/constants/theme.dart';
import 'package:get_clean/global/controllers/language_controller.dart';
import 'package:get_clean/global/widget/custom_button.dart';
import 'package:get_clean/global/widget/custom_form_field.dart';

class SelectOfferTimeBottomSheet extends StatelessWidget {
  const SelectOfferTimeBottomSheet({super.key});

  @override
  Widget build(BuildContext context) {
    Get.lazyPut<ServiceProvidersController>(
      () => ServiceProvidersController(),
      fenix: true,
    );
    Get.lazyPut<ProviderPageController>(
      () => ProviderPageController(),
      fenix: true,
    );

    Get.lazyPut<ProviderPageFilteredController>(
      () => ProviderPageFilteredController(),
      fenix: true,
    );

    return GetBuilder<ProviderPageFilteredController>(builder: (controller) {
      // controller.onInitBottomSheet();

      return Form(
        key: controller.formKey,
        child: Container(
          padding: const EdgeInsets.all(10),
          height: Get.height * 0.7,
          decoration: const BoxDecoration(
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(45),
              topRight: Radius.circular(45),
            ),
            color: Colors.white,
          ),
          child: SingleChildScrollView(
            child: Column(
              children: [
                Container(
                  decoration: BoxDecoration(
                    color: primaryColor,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  margin: const EdgeInsets.all(10),
                  height: 3,
                  width: Get.width * 0.3,
                ),
                Text(
                  controller.service.name ?? '',
                  style: bigTextStyle,
                ),

                const FilteredAreaAndCityProvidersWidget(),

                // offer average time
                CustomFormField(
                  hint: Get.find<LanguageController>().keys.value.address!,
                  label: Get.find<LanguageController>().keys.value.address!,
                  controller: controller.addressController,
                ),

                Center(
                  child: GestureDetector(
                    onTap: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (BuildContext context) => MapLocationPicker(
                            selectedMarkers: controller.marker,
                            onSave: (marker) {
                              controller.setMarker(marker);
                            },
                          ),
                        ),
                      );
                    },
                    child: Text(
                      Get.find<LanguageController>()
                          .keys
                          .value
                          .setLocationInMap!,
                      style: const TextStyle(
                          color: bgColor,
                          decoration: TextDecoration.underline,
                          decorationColor: bgColor),
                    ),
                    // child: Text(S.of(context).SetLocationOnMap),
                  ),
                ),

                // Row(
                //   children: [
                //     Expanded(
                //       child: DateTimePickerWidget(
                //         hint: Get.find<LanguageController>().keys.value.from!,
                //         label: Get.find<LanguageController>().keys.value.from!,
                //         // controller: controller.fromDateController,
                //       ),
                //     ),
                //     Expanded(
                //       child: DateTimePickerWidget(
                //         hint: Get.find<LanguageController>().keys.value.to!,
                //         label: Get.find<LanguageController>().keys.value.to!,
                //         // controller: controller.toDateController,
                //       ),
                //     ),
                //   ],
                // ),

                // Description
                CustomFormField(
                  hint: Get.find<LanguageController>().keys.value.note!,
                  label: Get.find<LanguageController>().keys.value.note!,
                  minLines: 3,
                  maxLines: 5,
                  controller: controller.notesController,
                ),

                SizedBox(height: 30.h),

                CustomButton(
                  label: Get.find<LanguageController>().keys.value.done!,
                  onTap: controller.onSubmitOfferButtonPressed,
                  height: 50,
                  width: Get.width * 0.8,
                ),

                // Row(
                //   mainAxisAlignment: MainAxisAlignment.spaceAround,
                //   children: [
                //     Text(
                //       controller.provider.city!.name!,
                //       style: big2TextStyle,
                //     ),
                //     Text(
                //       controller.provider.district!.name!,
                //       style: big2TextStyle,
                //     ),
                //   ],
                // ),
                // Container(
                //   alignment: Get.find<LanguageController>().isArabic
                //       ? Alignment.centerRight
                //       : Alignment.centerLeft,
                //   child: Text(
                //     Get.find<LanguageController>().keys.value.selectedDay!,
                //     style: big2TextStyle,
                //   ),
                // ),
                // SizedBox(
                //   height: 100,
                //   child: ListView.builder(
                //     controller: controller.scrollController,
                //     scrollDirection: Axis.horizontal,
                //     itemBuilder: (context, index) {
                //       final date = DateTime.now().add(Duration(days: index));
                //
                //       return InkWell(
                //         onTap: () => controller.changeSelectedDate(date),
                //         child: Container(
                //           width: 120,
                //           alignment: Alignment.center,
                //           padding: const EdgeInsets.all(10),
                //           margin: const EdgeInsets.all(5),
                //           decoration: BoxDecoration(
                //             border: Border.all(color: primaryColor),
                //             borderRadius: BorderRadius.circular(10),
                //             color:
                //                 controller.selectedDate.value.day == date.day &&
                //                         controller.selectedDate.value.month ==
                //                             date.month
                //                     ? primaryColor
                //                     : Colors.white,
                //           ),
                //           child: Column(
                //             crossAxisAlignment: CrossAxisAlignment.center,
                //             mainAxisAlignment: MainAxisAlignment.center,
                //             children: [
                //               Text(
                //                 DateFormat.d().format(date),
                //                 style: controller.selectedDate.value.day ==
                //                             date.day &&
                //                         controller.selectedDate.value.month ==
                //                             date.month
                //                     ? regularWhiteTextStyle
                //                     : regularTextStyle,
                //               ),
                //               Text(
                //                 DateFormat.EEEE().format(date),
                //                 style: controller.selectedDate.value.day ==
                //                             date.day &&
                //                         controller.selectedDate.value.month ==
                //                             date.month
                //                     ? regularWhiteTextStyle
                //                     : regularTextStyle,
                //               ),
                //             ],
                //           ),
                //         ),
                //       );
                //     },
                //     itemCount: 10,
                //   ),
                // ),
                // Container(
                //   alignment: Alignment.center,
                //   child: Text(
                //     DateFormat.yMd().format(controller.selectedDate.value),
                //     style: big2TextStyle,
                //   ),
                // ),
                //
                // // here we show how many hours for sofa
                // // if (controller.serviceFromGlobal.value.pricingOption!.id ==
                // //     3) ...[
                // //   const Divider(color: primaryColor),
                // //   const HowManyHoursSofa(),
                // // ],
                // //
                // // // here we show the select how many hours widget
                // // if (controller.serviceFromGlobal.value.pricingOption!.id ==
                // //     1) ...[
                // //   const Divider(color: primaryColor),
                // //   const SelectHowManyHoursWidget(),
                // // ],
                //
                // // here we show the select how many Meters widget
                // // if (controller.serviceFromGlobal.value.pricingOption!.id == 2)
                // //   Row(
                // //     children: [
                // //       Expanded(
                // //         child: CustomFormField(
                // //           // onChanged: (value) {
                // //           //   controller.onChangeHowManyMeters();
                // //           // },
                // //           keyboardType: TextInputType.number,
                // //           label: Get.find<LanguageController>()
                // //               .keys
                // //               .value
                // //               .howManyMeters!,
                // //           hint: Get.find<LanguageController>()
                // //               .keys
                // //               .value
                // //               .howManyMeters!,
                // //           controller: controller.howManyMeters,
                // //         ),
                // //       ),
                // //       // ElevatedButton(
                // //       //   onPressed: () {
                // //       //     controller.onChangeHowManyMeters();
                // //       //   },
                // //       //   child: Text(
                // //       //       Get.find<LanguageController>().keys.value.showTimes!),
                // //       // ),
                // //     ],
                // //   ),
                //
                // const Divider(color: primaryColor),
                //
                // // choose available time
                // // Text(
                // //   Get.find<LanguageController>().keys.value.whatTime!,
                // //   style: big2TextStyle,
                // // ),
                // // const ChooseTime(),
                // if (controller.avilableTimes.value.data != null &&
                //     controller
                //         .avilableTimes.value.data!.bookingDays!.isNotEmpty &&
                //     controller.avilableTimes.value.data!.bookingDays![0]
                //         .bookingTimes!.isNotEmpty &&
                //     controller.howManyMeters.value.text.isNotEmpty)

                //
                KeyboardVisibilityBuilder(
                    builder: (context, isKeyboardVisible) {
                  return SizedBox(
                      height: isKeyboardVisible ? context.height / 2 : 0);
                }),
              ],
            ),
          ),
        ),
      );
    });
  }
}
