import '../../../../../global/models/provider_avilable_times.dart';

class ProviderPageState {
  String? errorMessage;
}

class ProviderPageTimesLoadingState extends ProviderPageState {}

class ProviderPageTimesState extends ProviderPageState {
  ProviderAvilableTimes? times;
  ProviderPageTimesState(ProviderAvilableTimes this.times);
}

class ProviderPageFailedState extends ProviderPageState {
  ProviderPageFailedState(String errorMessage) {
    this.errorMessage = errorMessage;
  }
}
