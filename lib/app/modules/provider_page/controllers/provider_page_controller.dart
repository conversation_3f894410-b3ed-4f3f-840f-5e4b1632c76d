import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/add_new_service/controllers/add_new_service_controller.dart';
import 'package:get_clean/app/modules/provider_page/views/widgets/offers/select_offer_bottom_sheet.dart';
import 'package:get_clean/app/modules/provider_page/views/widgets/selected_time_bottom_sheet/make_request_bottom_sheet.dart';
import 'package:get_clean/app/modules/provider_page_filtered/controllers/provider_page_filtered_controller.dart';
import 'package:get_clean/global/controllers/language_controller.dart';
import 'package:get_clean/global/help_functions/help_functions.dart';
import 'package:get_clean/global/models/multi_list_model.dart';
import 'package:get_clean/global/models/provider_services.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';

import '../../../../global/constants/constants.dart';
import '../../../../global/controllers/global_values_controller.dart';
import '../../../../global/models/areas.dart';
import '../../../../global/models/cities.dart';
import '../../../../global/models/city.dart';
import '../../../../global/models/provider.dart';
import '../../../../global/models/provider_avilable_times.dart';
import '../../../routes/app_pages.dart';
import '../../service_providers/controllers/service_providers_controller.dart';
import '../provider/provider_page_remote_provider.dart';
import 'states/provider_page_state.dart';

class ProviderPageController extends GetxController {
  final Provider provider =
      Get.arguments != null ? Get.arguments['provider'] : Provider();
  final ProviderServices service =
      Get.arguments != null && Get.arguments['service'] != null
          ? Get.arguments['service']
          : ProviderServices();

  // List<ProviderServices>? providerServices = [];

  // void getProviderServices() async {
  //   providerServices = await Get.find<GlobalValuesController>().getAllServices(
  //     providerId: provider.id!,
  //   );
  //
  //   log('asfffffffffffasf ${providerServices!.map((e) => e.toJson())}');
  //
  //   update();
  // }

  final selectedDay = ''.obs;

  bool get isHours => serviceFromGlobal.value.pricingOption?.id == 1;

  // * Change Tab Index
  final tabIndex = 0.obs;

  void onChangeIndex(int index) {
    tabIndex.value = index;
    update();
  }

  void onChangeSelectedDay(String day) async {
    if (GetStorage().hasData(tokenKey) == false) {
      showErrorToast(
          Get.find<LanguageController>().keys.value.shouldLoginFirst!);
      return;
    }

    selectedDay.value = day;

    isBottomSheetInit = false;

    await Get.bottomSheet(
      // const SelectOfferTimeBottomSheet(),
      isOffer(service.pricingOption!.id!)
          ? const SelectOfferTimeBottomSheet()
          : const SelectProviderTimeBottomSheet(),
      isScrollControlled: true,
    );

    isBottomSheetInit = false;

    update();
  }

  bool isBottomSheetInit = false;
  final multiList = <MultiListModel>[].obs;

  void onDeleteMultiList(int index) {
    log('afaf ${multiList.length}');

    /// show get toast if index 1
    if (multiList.length == 1) {
      showErrorToast(
        Get.find<LanguageController>().keys.value.shouldHaveAtLeastOne!,
      );

      return;
    }

    multiList.removeAt(index);
    update();
  }

  void onAddMultiList() {
    multiList.add(
      MultiListModel(
        howManyHours: 2.obs,
        selectedDate: DateTime.now().obs,
        selectedTime: ''.obs,
      ),
    );
    update();
  }

  void onInitBottomSheet() {
    if (isBottomSheetInit || selectedDay.value.isEmpty) {
      return;
    }

    final thisWeekDateTimes = List.generate(
      7,
      (index) => DateTime.now().add(Duration(days: index)),
    );

    final firstDayEqualsToSelectedDay = thisWeekDateTimes.firstWhereOrNull(
      (element) =>
          DateFormat.EEEE().format(element).toLowerCase() == selectedDay.value,
    );

    changeSelectedDate(firstDayEqualsToSelectedDay!);

    //? navigate scrollController to selected day
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final indexOfSelectedDay = thisWeekDateTimes.indexOf(
        firstDayEqualsToSelectedDay,
      );

      scrollController.animateTo(
        indexOfSelectedDay * 120.0,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeIn,
      );
    });

    isBottomSheetInit = true;

    update();
  }

  Rx<ProviderServices> get serviceFromGlobal => Rx<ProviderServices>(service);

  // final serviceFromGlobal = ProviderServices().obs;

  final howManyHours = 2.obs;
  final howManyMeters = TextEditingController(text: '1');
  final howManyCars = TextEditingController(text: '1');
  final selectedDate = DateTime.now().obs;

  final ScrollController scrollController = ScrollController();

  final state = ProviderPageState().obs;

  final remoteProvider = ProviderPageRemoteProvider();

  final serviceProviderController = ServiceProvidersController();

  //? Filtered Areas and Cities for this provider
  final providerAreas = Areas().obs;
  final providerCities = Cities().obs;

  final choosedArea = Area().obs;
  final choosedCity = City().obs;

  Future<Areas> getProviderAreas() async {
    Areas areas = await serviceProviderController.getAreas();

    // log('siadksajdnkslndask ${provider.workZones!.map((e) => e.toJson())}');

    areas.data = areas.data!
        .where((element) =>
            provider.workAreas
                ?.any((workArea) => workArea.area?.id == element.id) ??
            false)
        .toList();

    return areas;
  }

  Future<Cities> getProviderCities(int areaId) async {
    Cities cities = await serviceProviderController.getCities(areaId);

    log('aafadf ${cities.data}');

    cities.data = cities.data!
        .where((element) =>
            provider.workAreas!.any((workArea) => element.id == workArea.id))
        .toList();

    return cities;
  }

  Future<void> onChangeArea(value) async {
    choosedCity.value = City();
    choosedArea.value = value;

    providerCities.value = await getProviderCities(value.id!);

    choosedCity.value = providerCities.value.data!.firstWhereOrNull(
          (element) => element.id == provider.workAreas!.first.id,
        ) ??
        City();

    update();
  }

  void onChangeCity(value) {
    choosedCity.value = value;
    update();
  }

  final avilableTimes = ProviderAvilableTimes().obs;
  final multiAvilableTimes = ProviderAvilableTimes().obs;

  final selectedTime = ''.obs;

  @override
  void onInit() async {
    super.onInit();

    howManyCloth.value = {};

    selectedTypesSettings.value = service.typesSettings ?? [];

    log('afafssafaffffffffasf ${selectedTypesSettings.value.map((e) => e.toJson())}');

    // Get.find<GlobalValuesController>()
    //     .allServices
    //     .value
    //     .data
    //     ?.firstWhereOrNull(
    //       (element) => element.service?.id == service.id,
    //     )
    //     ?.typesSettings ??
    // [];
    // serviceFromGlobal.value.service?.typesSettings ?? [];

    log('DDDDD ${service.id} FFFDDDDDDD ${Get.find<GlobalValuesController>().allServices.value.data?.map((e) => '\n${e.typesSettings?.map(
          (e) => e.toJson(),
        )}')}');
    // log('FFFDDDDDDD ${Get.find<GlobalValuesController>().allServices.value?.data?.map((e) => '\n${e.toJson()}')}');
    // log('FFFDDDDDDD ${Get.find<GlobalValuesController>().allServices.value?.data?.map((e) => '\n${e.toJson()}')}');
    log('MNgggggNfffNNNNNNN ${Get.find<GlobalValuesController>().allServices.value.data?.firstWhereOrNull(
          (element) => element.service?.id == service.id,
        )?.typesSettings?.map(
          (e) => e.toJson(),
        )}');
    log('MNNfffNNNNNNN ${selectedTypesSettings.value.length}');
    // log('FFAWSEWEWEWE ${serviceFromGlobal.value.toJson()}\nFFFSFFASF ${selectedTypesSettings.value.length}');

    providerAreas.value = await getProviderAreas();

    choosedArea.value = providerAreas.value.data!.firstWhere(
      (element) => element.id == provider.workAreas!.first.area!.id,
      orElse: () => Area(),
    );

    providerCities.value = await getProviderCities(choosedArea.value.id!);

    choosedCity.value = providerCities.value.data!.firstWhere(
      (element) => element.id == provider.workAreas!.first.id,
      orElse: () => City(),
    );

    if (tabIndex.value == 1) {
      getMultiProviderTimes(
        index: 0,
      );
    } else {
      getProviderTimes();
    }
  }

  @override
  void onClose() {
    howManyHours.value = 1;
    selectedDate.value = DateTime.now();
    super.onClose();
  }

  void changeSelecteTime(String time) {
    selectedTime.value = time;
    update();
  }

  void onChangeMultiSelectedTime(String time,
      {required Rx<String>? selectedTime}) {
    selectedTime?.value = time;

    update();
  }

  void changeSelectedDate(DateTime date) {
    selectedDate.value = date;

    getProviderTimes();
    update();
  }

  void changeMultiSelectedDate(DateTime date, int index) {
    multiList[index].selectedDate!.value = date;
    getMultiProviderTimes(index: index);
    update();
  }

// void onChangeHowManyMeters() {
//   // avilableTimes.value = ProviderAvilableTimes();
//   // update();
//   // getProviderTimes();
//   update();
// }

  void onChangeHowManyHoursSofa(int value, int index) {
    log('value: $value dddff $index fasfs ${serviceFromGlobal.value.typesSettings!.length}');

    log('afsfwwfwwfw ${selectedTypesSettings.value.map((e) => e.hours)}');

    selectedTypesSettings.value[index].hours = value;

    serviceFromGlobal.value.typesSettings?[index].hours = value;

    update();
  }

  // final services = [];

  // num? values = 0;

  final howManyCloth = <int, int>{}.obs;

  void onChangedHowManyCloth(int value, int id) {
    howManyCloth[id] = value;

    log('afasfsa ${howManyCloth}');
    update();
  }

  void onSelectedHoursTap(int hours) {
    howManyHours.value = hours;

    getProviderTimes();
    update();
  }

  void onSelectedMultiHoursTap(int hours, {required Rx<int>? howManyHours}) {
    howManyHours!.value = hours;

    getMultiProviderTimes(
      index: multiList.indexOf(multiList.firstWhere(
        (element) => element.howManyHours == howManyHours,
      )),
    );
    update();
  }

  void getProviderTimes() async {
    state.value = ProviderPageTimesLoadingState();
    update();

    if (serviceFromGlobal.value.pricingOption?.id == 3) {
      for (var element in serviceFromGlobal.value.typesSettings!) {
        howManyHours.value += int.tryParse(element.hours.toString()) ?? 1;
      }

      howManyHours.value = howManyHours.value ~/
          (serviceFromGlobal.value.typesSettings!.isEmpty
              ? 1
              : serviceFromGlobal.value.typesSettings!.length);
    }

    if (serviceFromGlobal.value.pricingOption?.id == 2) {
      howManyHours.value = int.tryParse(howManyMeters.text) ?? 1;
    }

    log('date: ${selectedDate.value.year}-${selectedDate.value.month}-${selectedDate.value.day} \n duration: ${howManyHours.value} \n service_id: ${service.id} \n provider_id: ${provider.userId}');

    log('asfasfs ${service.service?.name} IDDDDD ${service?.service?.id}');

    state.value = await remoteProvider.getProviderTimes({
      "provider_id": provider.userId,
      'date':
          '${selectedDate.value.year}-${selectedDate.value.month}-${selectedDate.value.day}',
      "duration": howManyHours.value,
      "service_id": service.service?.id,
    });

    update();

    if (state.value is ProviderPageTimesState) {
      final s = state.value as ProviderPageTimesState;
      avilableTimes.value = s.times!;
      multiAvilableTimes.value = s.times!;
      selectedTime.value = avilableTimes.value.data!.bookingDays!.isEmpty
          ? ""
          : avilableTimes.value.data!.bookingDays?[0].bookingTimes?[0] ?? "";

      update();
    } else if (state.value is ProviderPageFailedState) {
      // showErrorToast(state.value.errorMessage!);
    }
  }

  void getMultiProviderTimes({required int index}) async {
    state.value = ProviderPageTimesLoadingState();
    update();

    for (var element in multiList) {
      if (serviceFromGlobal.value.pricingOption?.id == 3) {
        for (var element in serviceFromGlobal.value.typesSettings!) {
          element.hours = element.hours;
        }
      }

      if (serviceFromGlobal.value.pricingOption?.id == 2) {
        element.howManyHours?.value = int.tryParse(howManyMeters.text) ?? 1;
      }
    }

    // if multi list empty take from selected date
    final date = multiList[index].selectedDate!.value;

    state.value = await remoteProvider.getProviderTimes({
      "provider_id": provider.userId,
      'date':
          '${multiList[index].selectedDate!.value.year}-${multiList[index].selectedDate!.value.month}-${multiList[index].selectedDate!.value.day}',
      // '${selectedDate.value.year}-${selectedDate.value.month}-${selectedDate.value.day}',
      "duration": howManyHours.value,
      "service_id": service.service?.id,
    });

    update();

    if (state.value is ProviderPageTimesState) {
      final s = state.value as ProviderPageTimesState;
      // avilableTimes.value = s.times!;
      multiAvilableTimes.value = s.times!;
      for (var element in multiList) {
        if (element.selectedTime?.value.isNotEmpty == true) {
          update();

          return;
        }

        element.selectedTime!.value = avilableTimes
                .value.data!.bookingDays!.isEmpty
            ? ""
            : avilableTimes.value.data!.bookingDays?[0].bookingTimes?[0] ?? "";
      }
    }
  }

  void onOkSend() {
    log('DDDDDD ${serviceFromGlobal.value.pricingOption?.id} aafFFFF ${serviceFromGlobal.value.service?.pricingOption?.id}');
    if (serviceFromGlobal.value.pricingOption?.id == 3) {
      for (var element in serviceFromGlobal.value.typesSettings!) {
        howManyHours.value += int.tryParse(element.hours.toString()) ?? 1;
      }

      log('assfasfsaf ${serviceFromGlobal.value.typesSettings?.length}');
      howManyHours.value = howManyHours.value ~/
          (serviceFromGlobal.value.typesSettings?.length == 0
              ? 1
              : serviceFromGlobal.value.typesSettings!.length);
    }

    if (serviceFromGlobal.value.pricingOption?.id == 2) {
      howManyHours.value = int.tryParse(howManyMeters.text) ?? 0;
    }
    // else {
    // howManyHours.value = 1;
    // }

    // log('daffasf ${service.id!,}')
    Get.toNamed(Routes.PROVIDER_PAGE_FILTERED, arguments: {
      'provider': provider,
      'date': selectedDate.value,
      'duration': howManyHours.value,
      'selectedTime': selectedTime.value,
      'clothManyCloth': howManyCloth,
      'service': service,
      // Get.find<GlobalValuesController>().allServices.value.data!.firstWhere(
      //       (element) => element.service?.id! == service.id!,
      // ),
      'typesSettings': serviceFromGlobal.value.typesSettings, //fix-here
      'choosedArea': choosedArea.value,
      'choosedCity': choosedCity.value,
    });
  }

  void onSubmitPressed() {
    Get.bottomSheet(
      isOffer(service.pricingOption?.id)
          ? const SelectOfferTimeBottomSheet()
          : const SelectProviderTimeBottomSheet(),
      isScrollControlled: true,
    ).then((value) {
      isBottomSheetInit = false;

      update();
    });
  }
}

bookedTimes({
  List<BookingDays>? bookingDays,
  String? endsAt,
}) {
  final bookTimes = bookingDays?.firstOrNull?.bookingTimes?.where((element) {
    // check if the day is today or return

    // convert 07 to 7
    final dayNumber =
        bookingDays.first.dateDetails?.dayNumber?.startsWith('0') == true
            ? bookingDays.first.dateDetails?.dayNumber?.substring(1)
            : bookingDays.first.dateDetails?.dayNumber;

    if (dayNumber != DateTime.now().day.toString()) {
      return true;
    }

    final elementTime = DateFormat.jm().format(
      DateFormat('hh:mm').parse(element),
    );

    final convertedHours = DateFormat('hh:mm').parse(elementTime);

    final endsAtTime = DateFormat.jm().format(
      DateFormat('hh:mm').parse(endsAt!),
    );

    log('EndTime: $endsAtTime');

    final endsAtTimeConverted = DateFormat('hh:mm').parse(endsAtTime);

    if (convertedHours.hour == endsAtTimeConverted.hour ||
        convertedHours.hour == endsAtTimeConverted.hour - 1) {
      return false;
    }

    return elementTime != endsAtTime;
  }).toList();

  return bookTimes;
}
