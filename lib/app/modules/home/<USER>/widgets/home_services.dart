// ignore_for_file: prefer_typing_uninitialized_variables

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/global/models/provider_services.dart';
import 'package:get_clean/global/widget/service_widget.dart';

import '../../../../../global/constants/theme.dart';
import '../../../../../global/controllers/language_controller.dart';
import '../../../../routes/app_pages.dart';

class HomeServices extends StatelessWidget {
  final title;
  final List<ProviderServices> services;
  final bool isMeter;
  final bool isHour;

  const HomeServices({
    Key? key,
    this.title,
    this.isMeter = false,
    this.isHour = false,
    required this.services,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: big2BlackTextStyle,
            ),
            TextButton(
              onPressed: () => Get.toNamed(
                Routes.ALL_SERVICES,
                arguments: {
                  'services': services,
                },
              ),
              child: Text(
                Get.find<LanguageController>().keys.value.viewAll!,
              ),
            ),
          ],
        ),
        SizedBox(
          height: 150.h,
          child: ListView(
            scrollDirection: Axis.horizontal,
            children: services
                .map(
                  (service) => ServiceWidget(
                    service: service,
                    isMeter: isMeter,
                    isHour: isHour,
                  ),
                )
                .toList(),
          ),
        ),
      ],
    );
  }
}
