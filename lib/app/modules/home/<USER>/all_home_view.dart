import 'package:badges/badges.dart';
import 'package:flutter/material.dart' hide Badge;
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/home/<USER>/widgets/home_services.dart';
import 'package:get_clean/app/modules/notifications/controllers/notifications_controller.dart';
import 'package:get_clean/global/controllers/language_controller.dart';

import '../../../../global/constants/theme.dart';
import '../../../../global/controllers/global_values_controller.dart';
import '../controllers/home_controller.dart';

class AllHomeView extends GetView<HomeController> {
  const AllHomeView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Get.put(NotificationsController());
    return GetBuilder<NotificationsController>(
        builder: (notificationController) {
      return Scaffold(
        body: GetBuilder<GlobalValuesController>(builder: (gVController) {
          return Container(
            alignment: Alignment.center,
            width: Get.width,
            height: Get.height,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Color(0xff28529F),
                  Color(0xff001F57),
                ],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
            child: SafeArea(
              child: Column(
                children: [
                  Container(
                    padding: const EdgeInsets.all(10),
                    child: Row(
                      children: [
                        InkWell(
                          onTap: () => Get.back(),
                          // Get.offAndToNamed(Routes.HOME),
                          child: const Icon(
                            Icons.arrow_back_ios_new,
                            color: Colors.white,
                          ),
                        ),
                        Expanded(
                          child: Text(
                            Get.find<LanguageController>().keys.value.services!,
                            textAlign: TextAlign.center,
                            style: bigWhiteTextStyle,
                          ),
                        ),

                        if (Get.find<GlobalValuesController>().isLoggedIn.value)
                          if (notificationController.notifications.value.data !=
                              null)
                            InkWell(
                              onTap: controller.onTapBell,
                              child: Badge(
                                showBadge: Get.find<NotificationsController>()
                                    .notifications
                                    .value
                                    .data!
                                    .where((element) => !element.isRead!)
                                    .toList()
                                    .isNotEmpty,
                                position: BadgePosition.topEnd(
                                  top: 0,
                                  end: 0,
                                ),
                                badgeStyle: const BadgeStyle(
                                  padding: EdgeInsets.all(2),
                                  elevation: 0,
                                  shape: BadgeShape.circle,
                                  badgeColor: Colors.white,
                                ),
                                badgeContent: Text(
                                  notificationController
                                      .notifications.value.data!
                                      .where((element) => !element.isRead!)
                                      .toList()
                                      .length
                                      .toString(),
                                  style: regularTextStyle,
                                ),
                                child: const Icon(
                                  FontAwesomeIcons.bell,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                        // if (notificationController.notifications.value.data !=
                        //     null)
                        //   if (Get.find<NotificationsController>()
                        //       .notifications
                        //       .value
                        //       .data!
                        //       .where((element) => !element.isRead!)
                        //       .toList()
                        //       .isEmpty)
                        //     InkWell(
                        //       onTap: controller.onTapBell,
                        //       child: const Icon(
                        //         FontAwesomeIcons.bell,
                        //         color: Colors.white,
                        //       ),
                        //     ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: Stack(
                      children: [
                        Container(
                          padding: const EdgeInsets.only(
                            top: 10,
                            right: 10,
                            left: 10,
                          ),
                          decoration: const BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(50),
                              topRight: Radius.circular(50),
                            ),
                          ),
                          child: Padding(
                            padding: EdgeInsets.only(top: 30.h),
                            child: ListView(
                              controller: controller.listViewController,
                              // crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // home services widgets
                                ...controller.globalValuesController.home.value
                                    .data!.services!
                                    .map((e) {
                                  return HomeServices(
                                    services: e.services!,
                                    title: e.name!,
                                    isHour: e.id == 1,
                                    isMeter: e.id == 2,
                                  );
                                }),
                              ],
                            ),
                          ),
                        ),
                        // Positioned(
                        //   bottom: 0,
                        //   left: 0,
                        //   right: 0,
                        //   child: ,
                        // ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        }),
      );
    });
  }
}

//import 'package:badges/badges.dart';
// import 'package:flutter/material.dart' hide Badge;
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:font_awesome_flutter/font_awesome_flutter.dart';
// import 'package:get/get.dart';
// import 'package:get_clean/app/modules/home/<USER>/widgets/home_services.dart';
// import 'package:get_clean/app/modules/notifications/controllers/notifications_controller.dart';
// import 'package:get_clean/global/controllers/language_controller.dart';
//
// import '../../../../global/constants/theme.dart';
// import '../../../../global/controllers/global_values_controller.dart';
// import '../controllers/home_controller.dart';
//
// class AllHomeView extends GetView<HomeController> {
//   const AllHomeView({Key? key}) : super(key: key);
//
//   @override
//   Widget build(BuildContext context) {
//     Get.put(NotificationsController());
//     return GetBuilder<NotificationsController>(
//         builder: (notificationController) {
//       return Scaffold(
//         body: GetBuilder<GlobalValuesController>(builder: (gVController) {
//           return Container(
//             alignment: Alignment.center,
//             width: Get.width,
//             height: Get.height,
//             decoration: const BoxDecoration(
//               gradient: LinearGradient(
//                 colors: [
//                   Color(0xff28529F),
//                   Color(0xff001F57),
//                 ],
//                 begin: Alignment.topCenter,
//                 end: Alignment.bottomCenter,
//               ),
//             ),
//             child: SafeArea(
//               child: Column(
//                 children: [
//                   Container(
//                     padding: const EdgeInsets.all(10),
//                     child: Row(
//                       children: [
//                         InkWell(
//                           onTap: () => Get.back(),
//                           // Get.offAndToNamed(Routes.HOME),
//                           child: const Icon(
//                             Icons.arrow_back_ios_new,
//                             color: Colors.white,
//                           ),
//                         ),
//                         Expanded(
//                           child: Text(
//                             Get.find<LanguageController>().keys.value.services!,
//                             textAlign: TextAlign.center,
//                             style: bigWhiteTextStyle,
//                           ),
//                         ),
//
//                         if (Get.find<GlobalValuesController>().isLoggedIn.value)
//                           if (notificationController.notifications.value.data !=
//                               null)
//                             InkWell(
//                               onTap: controller.onTapBell,
//                               child: Badge(
//                                 showBadge: Get.find<NotificationsController>()
//                                     .notifications
//                                     .value
//                                     .data!
//                                     .where((element) => element.isRead == false)
//                                     .toList()
//                                     .isNotEmpty,
//                                 position: BadgePosition.topEnd(
//                                   top: 0,
//                                   end: 0,
//                                 ),
//                                 badgeStyle: const BadgeStyle(
//                                   padding: EdgeInsets.all(2),
//                                   elevation: 0,
//                                   shape: BadgeShape.circle,
//                                   badgeColor: Colors.white,
//                                 ),
//                                 badgeContent: Text(
//                                   notificationController
//                                       .notifications.value.data!
//                                       .where(
//                                           (element) => element.isRead == false)
//                                       .toList()
//                                       .length
//                                       .toString(),
//                                   style: regularTextStyle,
//                                 ),
//                                 child: const Icon(
//                                   FontAwesomeIcons.bell,
//                                   color: Colors.white,
//                                 ),
//                               ),
//                             ),
//                         // if (notificationController.notifications.value.data !=
//                         //     null)
//                         //   if (Get.find<NotificationsController>()
//                         //       .notifications
//                         //       .value
//                         //       .data!
//                         //       .where((element) => !element.isRead!)
//                         //       .toList()
//                         //       .isEmpty)
//                         //     InkWell(
//                         //       onTap: controller.onTapBell,
//                         //       child: const Icon(
//                         //         FontAwesomeIcons.bell,
//                         //         color: Colors.white,
//                         //       ),
//                         //     ),
//                       ],
//                     ),
//                   ),
//                   Expanded(
//                     child: Stack(
//                       children: [
//                         Container(
//                           padding: const EdgeInsets.only(
//                             top: 10,
//                             right: 10,
//                             left: 10,
//                           ),
//                           decoration: const BoxDecoration(
//                             color: Colors.white,
//                             borderRadius: BorderRadius.only(
//                               topLeft: Radius.circular(50),
//                               topRight: Radius.circular(50),
//                             ),
//                           ),
//                           child: Padding(
//                             padding: EdgeInsets.only(top: 30.h),
//                             child: ListView(
//                               controller: controller.listViewController,
//                               // crossAxisAlignment: CrossAxisAlignment.start,
//                               children: [
//                                 // home services widgets
//                                 ...controller.globalValuesController.home.value
//                                     .data!.services!
//                                     .map((e) {
//                                   return HomeServices(
//                                     services: e.services!,
//                                     title: e.name!,
//                                     isHour: e.id == 1,
//                                     isMeter: e.id == 2,
//                                   );
//                                 }),
//                               ],
//                             ),
//                           ),
//                         ),
//                         // Positioned(
//                         //   bottom: 0,
//                         //   left: 0,
//                         //   right: 0,
//                         //   child: ,
//                         // ),
//                       ],
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//           );
//         }),
//       );
//     });
//   }
// }
