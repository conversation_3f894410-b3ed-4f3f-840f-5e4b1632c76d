import 'dart:developer';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/dio/dio_helper.dart';

class HomeRemoteProvider {
  DioHelper helper = DioHelper();

  Future updateFCMToken(fcmToken) async {
    try {
      await helper.postData(
        updateFCMTokenURL,
        {"fcm_token": fcmToken},
      );
    } catch (e) {
      log(e.toString());
    }
  }
}
