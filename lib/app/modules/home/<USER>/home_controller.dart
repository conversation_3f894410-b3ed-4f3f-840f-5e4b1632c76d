import 'dart:developer';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/home/<USER>/home_remote_provider.dart';
import 'package:get_clean/app/routes/app_pages.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/controllers/global_values_controller.dart';
import 'package:get_clean/global/models/user.dart';
import 'package:get_storage/get_storage.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../global/models/home_model.dart';

bool isProvider() {
  return Get.find<HomeController>().user.value.type! != 'user';
}

bool isLoggedIn() {
  final isLogged = GetStorage().hasData(userKey);

  return isLogged;
}

int? currentUserId() {
  if (isLoggedIn()) {
    return GetStorage().read(userKey)['id'];
  }

  return null;
}

class HomeController extends GetxController {
  final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();
  final user = User().obs;
  final storage = GetStorage();
  final globalValuesController = Get.find<GlobalValuesController>();
  final choosedPopularService = PopularServices().obs;
  ScrollController listViewController = ScrollController();

  @override
  void onInit() async {
    super.onInit();
    choosedPopularService.value =
        globalValuesController.home.value.data!.popularServices!.first;

    if (globalValuesController.isLoggedIn.value) {
      final FirebaseMessaging firebaseMessaging = FirebaseMessaging.instance;

      user.value = Get.arguments == null
          ? User.fromJson(storage.read(userKey))
          : Get.arguments['user'];
      await firebaseMessaging.deleteToken();
      final token = await firebaseMessaging.getToken(
        vapidKey: user.value.email!.toString(),
      );
      HomeRemoteProvider().updateFCMToken(
        token,
      );
      update();
    }
  }

  void onHomeTap() {
    Get.toNamed(Routes.HOME);
  }

  void onMyOrdersTap() {
    log('TYPEE ${user.value.type}');

    if (user.value.type == 'user') {
      Get.toNamed(Routes.MY_BOOKING);
    } else {
      Get.toNamed(Routes.MY_ORDERS);
    }
  }

  void onPackagesTap() {
    Get.toNamed(Routes.OFFERS);
  }

  void onSupportTap() async {
    /// to whatsapp for phone 232232
    /// "https://wa.me/232232"
    final data = globalValuesController.contactInfoModel.value.data;

    final whatsNumber = isProvider() ? data?.whatsappProvider : data?.whatsapp;

    var whatsappUrl = Uri.parse("https://wa.me/$whatsNumber");

    try {
      await launchUrl(whatsappUrl, mode: LaunchMode.externalApplication);
    } catch (e) {
      Get.snackbar(
        'Error',
        'Could not launch ${whatsappUrl.toString()}',
        margin: const EdgeInsets.all(16),
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  void onTapBell() {
    Get.toNamed(Routes.NOTIFICATIONS);
  }

  void onTapCart() {
    Get.toNamed(Routes.MY_CART);
  }

  void onTapDrawerIcon() {
    scaffoldKey.currentState!.openDrawer();
  }

  void onChangeService(service) {
    choosedPopularService.value = service;
    listViewController.jumpTo(
      listViewController.position.maxScrollExtent,
    );

    update();
  }

  void onOfferTapped(offer) {
    Get.toNamed(
      Routes.OFFER_DETAILS,
      arguments: {'offer': offer},
    );
  }

  void changeUserType(String type) {
    user.value.type = type;
    update();
  }

  void updateUser(User newUser) {
    user.value = user.value.copyWith(
      id: newUser.id,
      accessToken: newUser.accessToken,
      address: newUser.address,
      birthDate: newUser.birthDate,
      city: newUser.district,
      district: newUser.city,
      email: newUser.email,
      gender: newUser.gender,
      image: newUser.image,
      isVerified: newUser.isVerified,
      name: newUser.name,
      otp: newUser.otp,
      phone: newUser.phone,
      provider: newUser.provider,
      type: newUser.type,
    );
    update();
  }
}
