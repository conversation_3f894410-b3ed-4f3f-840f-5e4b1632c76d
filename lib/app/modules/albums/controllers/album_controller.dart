import 'dart:developer';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/albums/provider/remote_provider.dart';
import 'package:get_clean/app/modules/albums/views/widgets/add_bottom_sheet/add_images_bottom_sheet.dart';
import 'package:get_clean/global/controllers/language_controller.dart';
import 'package:get_clean/global/help_functions/help_functions.dart';
import 'package:get_clean/global/models/albums_model.dart';

import '../views/widgets/add_bottom_sheet/add_album_bottom_sheet.dart';

class AlbumController extends GetxController {
  final oldAlbum = AlbumsModel().obs;

  final languageController = Get.find<LanguageController>();
  final provider = AlbumRemoteProvider();

  // final state = AlbumAppointmentState().obs;
  final album = AllAlbumsModel().obs;
  final images = <AlbumImagesModel>[].obs;

  final choosedDate = ''.obs;
  final choosedTime = ''.obs;

  final loading = false.obs;

  final pickedAlbumCover = File('').obs;

  final pickedImages = <File>[].obs;

  TextEditingController albumNameController = TextEditingController();

  @override
  void onInit() {
    super.onInit();

    album.value.albums = [];

    getAlbums();
  }

  void pickCoverImage() async {
    pickedAlbumCover.value = await pickFile();
    update();
  }

  void pickImages() async {
    pickedImages.value = await pickMultiFiles();
    update();
  }

  void getAlbums() async {
    try {
      loading.value = true;
      final response = await provider.getAlbum();
      if (response.success == true) {
        album.value = response;

        for (var item in album.value.albums!) {
          item.images.value = await getAlbumsImages(item.id!);
        }

        Future.forEach(album.value.albums!, (element) async {
          element.images.value = await getAlbumsImages(element.id!);
        });
      }
    } catch (e) {
      log('ERROR IN GETTING ALBUMS $e');
    }

    loading.value = false;

    update();
  }

  Future<List<AlbumImagesModel>> getAlbumsImages(int albumID) async {
    try {
      final response = await provider.getAlbumImages(albumID);
      return response;
    } catch (e) {
      showErrorToast(e.toString());
      return [];
    }
  }

  // void onAddAlbumPressed() {
  //   Get.bottomSheet(
  //     AddAlbumBottomSheet(),
  //     isScrollControlled: true,
  //   );
  // }

  void addAlbumBottomSheet({AlbumsModel? editedAlbum}) async {
    albumNameController.text = editedAlbum?.name ?? '';

    if (editedAlbum != null) {
      oldAlbum.value = editedAlbum;

      update();
    }

    Get.bottomSheet(
      const AddAlbumBottomSheet(),
      isScrollControlled: true,
    ).then(
      (value) {
        pickedAlbumCover.value = File('');
        albumNameController.text = '';
        oldAlbum.value = AlbumsModel();
      },
    );
  }

  addImagesBottomSheet({required int? albumId}) async {
    Get.bottomSheet(
      AddImagesBottomSheet(albumId: albumId),
      isScrollControlled: true,
    ).then(
      (value) => pickedImages.value = [],
    );
  }

  //! Delete Album
  void onDeleteAlbum({required int? albumId}) {
    Get.defaultDialog(
      title:
          // "Delete Album",
          Get.find<LanguageController>().keys.value.deleteAlbum!,
      middleText: Get.find<LanguageController>().keys.value.deleteAlbumMessage!,
      onConfirm: () async {
        showWaitingIndicator();
        final response = await provider.deleteAlbum(albumId: albumId);
        if (response) {
          getAlbums();
        }

        hideWaitingIndicator();
        Get.back();
      },
      textConfirm: Get.find<LanguageController>().keys.value.yes!,
      textCancel: Get.find<LanguageController>().keys.value.no!,
    );
  }

  //! Delete Image
  void onDeleteImage({required int? imageId}) {
    Get.defaultDialog(
      title:
          // "Delete Image",
          Get.find<LanguageController>().keys.value.delete!,
      middleText:
          // "Are you sure that you want to delete this image?",
          Get.find<LanguageController>().keys.value.deleteImageMessage!,
      onConfirm: () async {
        showWaitingIndicator();
        final response = await provider.deleteAlbumImage(imageId: imageId);
        if (response) {
          getAlbums();
        }

        hideWaitingIndicator();
        Get.back();
      },
      textConfirm: Get.find<LanguageController>().keys.value.yes!,
      textCancel: Get.find<LanguageController>().keys.value.no!,
    );
  }

  // on Add Album Pressed
  Future<bool> addEditAlbum() async {
    try {
      bool response;
      showWaitingIndicator();

      if (oldAlbum.value.id != null) {
        response = await provider.editAlbum(
          oldAlbum.value.id!,
          name: albumNameController.text,
          cover: pickedAlbumCover.value.path,
        );
      } else {
        response = await provider.addNewAlbum(
          name: albumNameController.text,
          cover: pickedAlbumCover.value.path,
        );
      }

      getAlbums();

      hideWaitingIndicator();

      Get.back();

      return true;
    } catch (e) {
      return false;
    } finally {}
  }

  // on Add Images Pressed
  Future<bool> addImages({required int? albumId}) async {
    try {
      bool response;

      showWaitingIndicator();

      response = await provider.addAlbumImages(
        albumId: albumId,
        images: pickedImages.map((e) => e.path).toList(),
      );

      getAlbums();

      hideWaitingIndicator();

      Get.back();

      return true;
    } catch (e) {
      return false;
    }
  }
}
