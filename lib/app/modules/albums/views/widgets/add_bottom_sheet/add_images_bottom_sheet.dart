import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/albums/controllers/album_controller.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/controllers/language_controller.dart';

import '../../../../../../global/widget/custom_button.dart';

class AddImagesBottomSheet extends GetView<AlbumController> {
  final int? albumId;

  const AddImagesBottomSheet({super.key, required this.albumId});

  @override
  Widget build(BuildContext context) {
    final isEdit = controller.oldAlbum.value.id != null;

    return Container(
      color: Colors.white,
      height: 500.h,
      child: Column(
        children: [
          const SizedBox(height: 20),

          Text(
            Get.find<LanguageController>().keys.value.addImages!,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),

          //! Image
          Padding(
            padding: const EdgeInsets.all(20),
            child: InkWell(
              onTap: controller.pickImages,
              child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(15),
                    border: Border.all(
                      color: primaryColor.withOpacity(0.6),
                    ),
                  ),
                  child: Obx(() => controller.pickedImages.isEmpty
                      ? Row(
                          children: [
                            const Icon(
                              Icons.add,
                              size: 40,
                            ),
                            Expanded(
                              child: Icon(
                                Icons.image,
                                color: primaryColor.withOpacity(0.6),
                                size: Get.width * 0.4,
                              ),
                            ),
                          ],
                        )
                      : SizedBox(
                          height: 120.h,
                          child: ListView.builder(
                            scrollDirection: Axis.horizontal,
                            itemCount: controller.pickedImages.length,
                            itemBuilder: (context, index) => Container(
                              padding: const EdgeInsets.all(8),
                              child: ClipRRect(
                                  borderRadius: BorderRadius.circular(10),
                                  child: Image.file(
                                    controller.pickedImages[index],
                                    fit: BoxFit.cover,
                                  )),
                            ),
                          ),
                        ))),
            ),
          ),

          const SizedBox(height: 20),

          CustomButton(
            label: Get.find<LanguageController>().keys.value.submit!,
            onTap: () {
              controller.addImages(albumId: albumId);
            },
            height: 41.h,
            width: 190.w,
          ),
        ],
      ),
    );
  }
}
