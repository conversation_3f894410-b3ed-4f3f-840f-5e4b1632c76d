import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/albums/controllers/album_controller.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/controllers/language_controller.dart';

import '../../../../../../global/widget/custom_button.dart';

class AddAlbumBottomSheet extends GetView<AlbumController> {
  const AddAlbumBottomSheet({super.key});

  @override
  Widget build(BuildContext context) {
    final isEdit = controller.oldAlbum.value.id != null;
    return Container(
      color: Colors.white,
      height: 600.h,
      child: Column(
        children: [
          const SizedBox(height: 20),

          Text(
            isEdit
                ? Get.find<LanguageController>().keys.value.editAlbum!
                : Get.find<LanguageController>().keys.value.addAlbum!,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),
          //! Name
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: TextField(
              controller: controller.albumNameController,
              decoration: InputDecoration(
                hintText: Get.find<LanguageController>().keys.value.name!,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            ),
          ),
          const SizedBox(height: 20),

          //! Image
          Padding(
            padding: const EdgeInsets.all(20),
            child: InkWell(
              onTap: controller.pickCoverImage,
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(15),
                  border: Border.all(
                    color: primaryColor.withOpacity(0.6),
                  ),
                ),
                child: Obx(() => controller.pickedAlbumCover.value.path.isEmpty
                    ? Row(
                        children: [
                          const Icon(
                            Icons.add,
                            size: 40,
                          ),
                          Expanded(
                            child: Icon(
                              Icons.image,
                              color: primaryColor.withOpacity(0.6),
                              size: Get.width * 0.4,
                            ),
                          ),
                        ],
                      )
                    : ClipRRect(
                        borderRadius: BorderRadius.circular(15),
                        child: Image.file(
                          controller.pickedAlbumCover.value,
                          height: 150,
                          width: 250,
                          fit: BoxFit.cover,
                        ),
                      )),
              ),
            ),
          ),

          const SizedBox(height: 20),

          CustomButton(
            label: Get.find<LanguageController>().keys.value.submit!,
            onTap: () {
              controller.addEditAlbum();
            },
            height: 41.h,
            width: 190.w,
          ),
        ],
      ),
    );
  }
}
