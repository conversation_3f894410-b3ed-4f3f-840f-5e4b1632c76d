import 'dart:developer';

import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/dio/dio_helper.dart';
import 'package:get_clean/global/models/user_booking.dart';

class UserOfferServiceRemoteUser {
  DioHelper helper = DioHelper();

  Future<UserBookings?> getPendingOffers() async {
    try {
      final response = await helper.getData(
        getPendingUserServiceOffersURL,
      );

      log('PPPP ${response}');
      if (response['success'] == true) {
        return UserBookings.fromJson(response);
      } else {
        return null;
      }
    } catch (e) {
      log(e.toString());
      return null;
    }
  }

  Future<UserBookings?> getWaitingOffers() async {
    try {
      final response = await helper.getData(
        getWaitingUserServiceOffersURL,
      );
      log('WWWW ${response}');

      if (response['success'] == true) {
        return UserBookings.fromJson(response);
      } else {
        return null;
      }
    } catch (e) {
      log(e.toString());
      return null;
    }
  }

  Future<UserBookings?> getUnPaidOffers() async {
    try {
      final response = await helper.getData(
        getUnpaidUserServiceOffersURL,
      );

      log('GGGG ${response}');

      if (response['success'] == true) {
        return UserBookings.fromJson(response);
      } else {
        return null;
      }
    } catch (e) {
      log(e.toString());
      return null;
    }
  }
}
