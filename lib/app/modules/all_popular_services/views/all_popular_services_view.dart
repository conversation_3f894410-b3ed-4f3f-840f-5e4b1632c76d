import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/constants/theme.dart';
import 'package:get_clean/global/models/provider.dart';
import 'package:get_clean/global/models/provider_services.dart';

import '../../../../global/controllers/language_controller.dart';
import '../../../../global/widget/popular_service_widget.dart';
import '../../../../global/widget/provider_widget.dart';
import '../../../routes/app_pages.dart';
import '../../home/<USER>/home_controller.dart';
import '../controllers/all_popular_services_controller.dart';

class AllPopularServicesView extends GetView<AllPopularServicesController> {
  const AllPopularServicesView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: primaryColor,
      appBar: AppBar(
        centerTitle: true,
        elevation: 0,
        title: Text(
          Get.find<LanguageController>().keys.value.popularServices!,
          style: bigWhiteTextStyle,
          textAlign: TextAlign.center,
        ),
      ),
      body: Container(
        padding: const EdgeInsets.only(
          top: 30,
          left: 10,
          right: 10,
          bottom: 10,
        ),
        height: Get.height,
        decoration: const BoxDecoration(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(45),
            topRight: Radius.circular(45),
          ),
          color: Colors.white,
        ),
        child: SingleChildScrollView(
          child: GetBuilder<HomeController>(builder: (controller) {
            return Column(
              children: [
                SizedBox(
                  height: 35.h,
                  child: ListView(
                    scrollDirection: Axis.horizontal,
                    children: controller.globalValuesController.home.value.data!
                        .popularServices!
                        .map(
                          (service) => PopularServiceWidget(
                            title: service.service!.name,
                            onTap: () => controller.onChangeService(service),
                            isActive: service.service!.id ==
                                controller
                                    .choosedPopularService.value.service!.id,
                          ),
                        )
                        .toList(),
                  ),
                ),

                // popular companies and providers
                ...controller.choosedPopularService.value.providers!
                    .map(
                      (provider) => ProviderWidget(
                        provider: Provider.fromJson(provider.toJson()),
                        showBanner: true,
                        showBooking: true,
                        isHour: controller.choosedPopularService.value.service
                                ?.pricingOptionId ==
                            1,
                        isMeter: controller.choosedPopularService.value.service
                                ?.pricingOptionId ==
                            2,
                        onTap: () =>
                            Get.toNamed(Routes.PROVIDER_PAGE, arguments: {
                          'provider': provider,
                          'service': ProviderServices.fromJson(controller
                              .choosedPopularService.value.service!
                              .toJson()),
                        }),
                        serviceId:
                            controller.choosedPopularService.value.service?.id,
                      ),
                    )
                    .toList(),

                Center(
                  child: TextButton(
                    child:
                        Text(Get.find<LanguageController>().keys.value.more!),
                    onPressed: () {
                      Get.toNamed(
                        Routes.SERVICE_PROVIDERS,
                        arguments: {
                          'service': ProviderServices.fromJson(controller
                              .choosedPopularService.value.service!
                              .toJson()),
                          'isHour': controller.choosedPopularService.value
                                  .service?.pricingOptionId ==
                              1,
                          'isMeter': controller.choosedPopularService.value
                                  .service?.pricingOptionId ==
                              2,
                        },
                      );
                    },
                  ),
                ),
              ],
            );
          }),
        ),
      ),
    );
  }
}
