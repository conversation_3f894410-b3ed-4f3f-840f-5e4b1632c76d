import 'dart:developer';

import 'package:get_clean/global/models/service_offer_model.dart';

import '../../../../global/constants/constants.dart';
import '../../../../global/dio/dio_helper.dart';
import '../../../../global/help_functions/help_functions.dart';

class EditServiceProvider {
  DioHelper helper = DioHelper();

  Future<bool> editServiceWithoutType(data, int id) async {
    try {
      final response = await helper.postData(editServiceWithoutTypesURL, data);
      // editServiceWithoutTypesURL + id.toString(), data);
      log(response.toString());
      if (response['success'] == true) {
        showSuccessToast(response['message']);
        return true;
      } else {
        showErrorToast(response['message']);
        return false;
      }
    } catch (e) {
      log(e.toString());
      return false;
    }
  }

  Future<bool> editServiceWithTypes(data, int id) async {
    try {
      final response = await helper.postData(editServiceWithTypesURL, data);
      // await helper.postData(editServiceWithTypesURL + id.toString(), data);
      log(response.toString());
      if (response['success'] == true) {
        showSuccessToast(response['message']);
        return true;
      } else {
        showErrorToast(response['message']);
        return false;
      }
    } catch (e) {
      log(e.toString());
      return false;
    }
  }

  //? get offer services
  Future<ServiceOffersModel> getOfferServices() async {
    try {
      final response = await helper.getData(getServiceOfferDetailsURL);

      log('Offer Services $response');

      if (response['success'] == true) {
        return ServiceOffersModel.fromJson(response);
      } else {
        showErrorToast(response['message']);
        return ServiceOffersModel.fromJson(response);
      }
    } catch (e) {
      log(e.toString());
      return ServiceOffersModel.fromJson({});
    }
  }

  //? add offer services
  Future<bool> addOfferServices(data) async {
    try {
      final response = await helper.postData(addServiceOfferURL, data);
      log('Add Offer Services $response');

      if (response['success'] == true) {
        showSuccessToast(response['message']);
        return true;
      } else {
        showErrorToast(response['message']);
        return false;
      }
    } catch (e) {
      log(e.toString());
      return false;
    }
  }
}
