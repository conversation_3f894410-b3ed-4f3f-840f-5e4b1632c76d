import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/add_new_service/controllers/add_new_service_controller.dart';
import 'package:get_clean/app/modules/edit_service/controllers/edit_service_controller.dart';
import 'package:get_clean/global/widget/custom_form_field.dart';

import '../../../../../global/constants/constants.dart';
import '../../../../../global/controllers/language_controller.dart';
import '../../../../../global/widget/text_with_background.dart';
import 'edit_service_form_field.dart';

class MetersWidget extends StatelessWidget {
  const MetersWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<EditServiceController>(
      builder: (controller) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (isHoursService(controller.service.service!.pricingOption?.id))
              //! Min Hours
              CustomFormField(
                label: Get.find<LanguageController>().keys.value.minHours ??
                    'Min hours',
                hint: Get.find<LanguageController>().keys.value.minHours ??
                    'Min hours',
                keyboardType: TextInputType.number,
                controller: controller.minHoursController,
              ),
            Container(
              padding: const EdgeInsets.all(10),
              margin: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(15),
                color: const Color(0xffF3F3F3),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      children: [
                        TextWithBackground(
                          color: primaryColor,
                          text: Get.find<LanguageController>().keys.value.from!,
                        ),
                        for (int i = 0;
                            i < controller.hourMeterList.length;
                            i++)
                          EditServiceFormField(
                            controller: controller.hourMeterList[i].from,
                          ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 10),
                  Expanded(
                    child: Column(
                      children: [
                        TextWithBackground(
                          color: primaryColor,
                          text: Get.find<LanguageController>().keys.value.to!,
                        ),
                        for (int i = 0;
                            i < controller.hourMeterList.length;
                            i++)
                          EditServiceFormField(
                            controller: controller.hourMeterList[i].to,
                          ),
                      ],
                    ),
                  ),
                  SizedBox(width: 10.w),
                  Expanded(
                    child: Column(
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: TextWithBackground(
                                color: primaryColor,
                                text: Get.find<LanguageController>()
                                    .keys
                                    .value
                                    .price!,
                              ),
                            ),
                            const SizedBox(width: 5),
                            InkWell(
                              onTap: controller.addNewHourMeterRow,
                              child: const Icon(
                                FontAwesomeIcons.circlePlus,
                                color: primaryColor,
                                size: 15,
                              ),
                            ),
                          ],
                        ),
                        for (int i = 0;
                            i < controller.hourMeterList.length;
                            i++)
                          Row(
                            children: [
                              Expanded(
                                child: EditServiceFormField(
                                  controller: controller.hourMeterList[i].price,
                                ),
                              ),
                              const SizedBox(width: 5),
                              InkWell(
                                onTap: () => controller.removeHourMeterRow(
                                    controller.hourMeterList[i]),
                                child: const Icon(
                                  FontAwesomeIcons.trash,
                                  color: primaryColor,
                                  size: 15,
                                ),
                              ),
                            ],
                          ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }
}
