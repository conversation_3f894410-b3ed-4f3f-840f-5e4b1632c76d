import 'dart:developer';

import 'package:get/get.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/dio/dio_helper.dart';
import 'package:get_clean/global/help_functions/help_functions.dart';
import 'package:get_clean/global/widget/loading_widget.dart';

class ChangePasswordRemoteProvider {
  DioHelper helper = DioHelper();

  Future<bool> changePassword(data) async {
    try {
      Get.dialog(const LoadingWidget());
      final response = await helper.postData(changePasswordURL, data);
      if (response['success'] == true) {
        showSuccessToast(response['message']);
        return true;
      } else {
        showErrorToast(response['message']);
        return false;
      }
    } catch (e) {
      log(e.toString());
      return false;
    } finally {
      Get.back();
    }
  }
}
