import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/change_password/provider/remote_provider.dart';

class ChangePasswordController extends GetxController {
  TextEditingController passowrd = TextEditingController();
  TextEditingController confirmPassword = TextEditingController();

  final key = GlobalKey<FormState>();

  final provider = ChangePasswordRemoteProvider();

  void changePassword() async {
    if (key.currentState!.validate()) {
      await provider.changePassword({
        "password": passowrd.text,
        "password_confirmation": confirmPassword.text,
      });
    }
  }
}
