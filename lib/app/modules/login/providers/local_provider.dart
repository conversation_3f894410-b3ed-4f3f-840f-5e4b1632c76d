import 'package:get_clean/global/models/user.dart';
import 'package:get_storage/get_storage.dart';

import '../../../../global/constants/constants.dart';

class LocalLoginProvider {
  final storage = GetStorage();

  Future<void> saveLoginData(String emailOrPhone, String password) async {
    await storage.write(emailOr<PERSON>hone<PERSON>ey, emailOrPhone);
    await storage.write(passwordKey, password);
  }

  Future<void> saveUserData(User user) async {
    await storage.write(userKey, user.toJson());
    await storage.write(tokenKey, user.accessToken);
  }

  Future<void> deleteUserData() async {
    await storage.remove(userKey);
    await storage.remove(tokenKey);
    await storage.remove(emailOrPhoneKey);
    await storage.remove(passwordKey);
  }

  bool hasData() {
    return storage.hasData(emailOrPhoneKey);
  }

  String getEmailOrPhone() {
    return storage.read(emailOrPhoneKey);
  }

  String getPassword() {
    return storage.read(passwordKey);
  }
}
