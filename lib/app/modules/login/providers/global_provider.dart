import 'dart:developer';

import 'package:get_clean/app/modules/login/controllers/states/login_states.dart';
import 'package:get_clean/global/dio/dio_helper.dart';
import 'package:get_clean/global/help_functions/help_functions.dart';
import 'package:get_clean/global/models/user.dart';
import '../../../../global/constants/constants.dart';

class LoginGlobalProvider {
  DioHelper helper = DioHelper();

  Future<LoginStatus> login(String emailOrPhone, String password) async {
    try {
      final response = await helper.postData(
        loginURL,
        {
          "email_phone": emailOrPhone,
          "password": password,
        },
      );
      log(response.toString());
      log(response['success'].toString());
      if (response['success'] == true) {
        return LoginSuccessState(User.fromJson(response['data']));
      } else {
        showErrorToast(response['message']);
        return LoginFailedState(
          response['message'],
          user: User.from<PERSON><PERSON>(response['data']),
        );
      }
    } catch (e) {
      return LoginFailedState(e.toString());
    }
  }
}
