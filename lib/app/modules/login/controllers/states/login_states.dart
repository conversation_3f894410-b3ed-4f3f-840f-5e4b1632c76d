import '../../../../../global/models/user.dart';

class LoginStatus {
  User? user;
  String? errorMessge;
}

class LoginSuccessState extends LoginStatus {
  LoginSuccessState(User user) {
    this.user = user;
  }
}

class LoginFailedState extends LoginStatus {
  LoginFailedState(String errorMessge, {User? user}) {
    this.errorMessge = errorMessge;
    this.user = user;
  }
}

class LoginLoadingState extends LoginStatus {}

class LoginIdleState extends LoginStatus {}
