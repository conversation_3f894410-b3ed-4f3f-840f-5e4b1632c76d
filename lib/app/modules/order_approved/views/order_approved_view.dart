import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../global/constants/theme.dart';
import '../../../../global/controllers/language_controller.dart';
import '../controllers/order_approved_controller.dart';

class OrderApprovedView extends GetView<OrderApprovedController> {
  const OrderApprovedView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return GetBuilder<OrderApprovedController>(
      builder: (controller) {
        return Scaffold(
          body: Container(
            padding: const EdgeInsets.all(10),
            width: Get.width,
            height: Get.height,
            decoration: const BoxDecoration(
              image: DecorationImage(
                image: AssetImage(
                  'assets/images/main_background.png',
                ),
                fit: BoxFit.fill,
              ),
            ),
            child: SafeArea(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Image.asset(
                    'assets/images/check.png',
                    width: 142.w,
                    height: 142.h,
                    fit: BoxFit.fill,
                  ),
                  Text(
                    Get.find<LanguageController>()
                        .keys
                        .value
                        .orderApprovedSuccessfully!,
                    style: bigTextStyle,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
