import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/edit_schduel/controllers/edit_schduel_controller.dart';

import '../../../../../global/constants/theme.dart';
import '../../../../../global/controllers/language_controller.dart';
import '../../../../../global/widget/custom_button.dart';
import 'choose_days_widget.dart';

class ChooseWorkingTimesWidget extends StatelessWidget {
  const ChooseWorkingTimesWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<EditSchduelController>(
      builder: (controller) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              Get.find<LanguageController>().keys.value.schduelYourOrders!,
              style: big2TextStyle,
            ),

            // choose days
            const HowManyDaysWidget(),

            // choose start work time
            Container(
              alignment: Get.find<LanguageController>().isArabic
                  ? Alignment.centerRight
                  : Alignment.centerLeft,
              child: Text(
                Get.find<LanguageController>().keys.value.from!,
                style: big2TextStyle,
              ),
            ),
            Center(
              child: InkWell(
                onTap: () => controller.chooseFromTime(context),
                child: Text(
                  controller.fromTime.value,
                  style: bigTextStyle,
                ),
              ),
            ),

            // choose end work time
            Container(
              alignment: Get.find<LanguageController>().isArabic
                  ? Alignment.centerRight
                  : Alignment.centerLeft,
              child: Text(
                Get.find<LanguageController>().keys.value.to!,
                style: big2TextStyle,
              ),
            ),
            Center(
              child: InkWell(
                onTap: () => controller.chooseToTime(context),
                child: Text(
                  controller.toTime.value,
                  style: bigTextStyle,
                ),
              ),
            ),

            // add working time to working times list
            Center(
              child: CustomButton(
                label: Get.find<LanguageController>().keys.value.done!,
                onTap: controller.addWorkingTimes,
                height: 43.h,
                width: 129.w,
              ),
            ),
          ],
        );
      },
    );
  }
}
