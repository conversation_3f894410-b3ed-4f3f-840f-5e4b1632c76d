import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/edit_schduel/controllers/edit_schduel_controller.dart';

import '../../../../../global/constants/constants.dart';
import '../../../../../global/constants/theme.dart';

class HowManyDaysWidget extends StatelessWidget {
  const HowManyDaysWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<EditSchduelController>(
      builder: (controller) {
        return Container(
          decoration: BoxDecoration(
            border: Border.all(
              color: primaryColor,
              width: 0.5,
            ),
            borderRadius: BorderRadius.circular(15),
            color: Colors.white,
          ),
          height: 50,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              for (int i = 0; i < 7; i++)
                Expanded(
                  child: InkWell(
                    onTap: () => controller.changeSelectedDay(i),
                    child: Container(
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        color: controller.selectedDays[i] == 1
                            ? primaryColor
                            : Colors.white,
                      ),
                      padding: const EdgeInsets.all(6),
                      child: Text(
                        controller.weekDays[i],
                        style: controller.selectedDays[i] == 1
                            ? middleWhiteTextStyle
                            : middleTextStyle,
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }
}
