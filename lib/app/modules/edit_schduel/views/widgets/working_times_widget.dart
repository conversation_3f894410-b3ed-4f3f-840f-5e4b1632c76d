import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_clean/app/modules/edit_schduel/controllers/edit_schduel_controller.dart';
import '../../../../../global/constants/constants.dart';
import '../../../../../global/constants/theme.dart';
import '../../../../../global/controllers/language_controller.dart';
import '../../../../../global/widget/text_with_background.dart';
import 'package:get/get.dart';

class WorkingTimesWidget extends StatelessWidget {
  const WorkingTimesWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<EditSchduelController>(
      builder: (controller) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // working times
            Text(
              Get.find<LanguageController>().keys.value.workingTimes!,
              style: big2TextStyle,
            ),
            Container(
              padding: const EdgeInsets.all(10),
              margin: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(15),
                color: const Color(0xffF3F3F3),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      children: [
                        TextWithBackground(
                          color: primaryColor,
                          text: Get.find<LanguageController>().keys.value.days!,
                        ),
                        for (int i = 0; i < controller.workingTimesLength; i++)
                          TextWithBackground(
                            color: Colors.white,
                            text: controller.dayName(i),
                            textColor: Colors.black,
                          ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 10),
                  Expanded(
                    child: Column(
                      children: [
                        TextWithBackground(
                          color: primaryColor,
                          text: Get.find<LanguageController>().keys.value.open!,
                        ),
                        for (int i = 0; i < controller.workingTimesLength; i++)
                          TextWithBackground(
                            color: Colors.white,
                            text: controller.startAt(i),
                            textColor: Colors.black,
                          ),
                      ],
                    ),
                  ),
                  SizedBox(width: 10.w),
                  Expanded(
                    child: Column(
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: TextWithBackground(
                                color: primaryColor,
                                text: Get.find<LanguageController>()
                                    .keys
                                    .value
                                    .close!,
                              ),
                            ),
                            const SizedBox(width: 2),
                            const Icon(
                              FontAwesomeIcons.trash,
                              color: Colors.transparent,
                              size: 15,
                            ),
                          ],
                        ),
                        for (int i = 0; i < controller.workingTimesLength; i++)
                          Row(
                            children: [
                              Expanded(
                                child: TextWithBackground(
                                  color: Colors.white,
                                  text: controller.endAt(i),
                                  textColor: Colors.black,
                                ),
                              ),
                              const SizedBox(width: 2),
                              InkWell(
                                onTap: () => controller.deleteWorkingTime(i),
                                child: const Icon(
                                  FontAwesomeIcons.trash,
                                  color: primaryColor,
                                  size: 15,
                                ),
                              ),
                            ],
                          ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }
}
