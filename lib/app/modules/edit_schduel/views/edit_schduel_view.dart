import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/edit_schduel/controllers/states/working_times_states.dart';
import 'package:get_clean/app/modules/edit_schduel/views/widgets/choose_working_times_widget.dart';
import 'package:get_clean/app/modules/edit_schduel/views/widgets/holidays_widget.dart';
import 'package:get_clean/app/modules/edit_schduel/views/widgets/working_times_widget.dart';
import 'package:get_clean/global/widget/custom_button.dart';
import 'package:get_clean/global/widget/loading_widget.dart';

import '../../../../global/constants/theme.dart';
import '../../../../global/controllers/language_controller.dart';
import '../controllers/edit_schduel_controller.dart';

class EditSchduelView extends GetView<EditSchduelController> {
  const EditSchduelView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return GetBuilder<EditSchduelController>(
      builder: (controller) {
        return Scaffold(
          body: Container(
            width: Get.width,
            height: Get.height,
            padding: const EdgeInsets.all(10),
            decoration: const BoxDecoration(
              image: DecorationImage(
                image: AssetImage(
                  'assets/images/main_background.png',
                ),
                fit: BoxFit.fill,
              ),
            ),
            child: SafeArea(
              child: Builder(builder: (context) {
                if (controller.state.value is WorkingTimesLoadingState) {
                  return const LoadingWidget();
                }
                if (controller.state.value is WorkingTimesFailedState) {
                  return Center(
                    child: Text(
                      controller.state.value.errorMessage.toString(),
                      style: bigTextStyle,
                    ),
                  );
                }
                return SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          IconButton(
                            onPressed: Get.back,
                            icon: const Icon(
                              CupertinoIcons.back,
                              size: 30,
                            ),
                          ),
                          Expanded(
                            child: Text(
                              Get.find<LanguageController>()
                                  .keys
                                  .value
                                  .editSchduel!,
                              style: bigTextStyle,
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),

                      const ChooseWorkingTimesWidget(),

                      const WorkingTimesWidget(),

                      const HolidaysWidget(),

                      // save data
                      Center(
                        child: CustomButton(
                          label:
                              Get.find<LanguageController>().keys.value.save!,
                          onTap: () => Get.back(),
                          height: 43.h,
                          width: 129.w,
                        ),
                      ),
                    ],
                  ),
                );
              }),
            ),
          ),
        );
      },
    );
  }
}
