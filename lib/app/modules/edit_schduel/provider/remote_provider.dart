import 'dart:convert';
import 'dart:developer';

import 'package:get_clean/app/modules/edit_schduel/controllers/states/working_times_states.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/dio/dio_helper.dart';
import 'package:get_clean/global/help_functions/help_functions.dart';
import 'package:get_clean/global/models/working_times_model.dart';

class EditSchduelRemoteProvider {
  DioHelper helper = DioHelper();

  Future<dynamic> getWeekDays() async {
    try {
      await helper.getData(getProviderWeekDays);
      return 1;
    } catch (e) {
      log(e.toString());
      return 1;
    }
  }

  Future<WorkingTimesState> getWorkingTimes() async {
    try {
      final response = await helper.getData(getProviderWorkingTimes);
      if (response['success'] == true) {
        return WorkingTimesSuccessState(WorkingTimesModel.fromJson(response));
      } else {
        showErrorToast(response['message']);
        return WorkingTimesFailedState(response['message']);
      }
    } catch (e) {
      log(e.toString());
      return WorkingTimesFailedState(e.toString());
    }
  }

  Future<bool> setWorkingTimes(data) async {
    try {
      final response = await helper.postData(setProviderWorkingTimes, data);
      if (response['success'] == true) {
        showSuccessToast(response['message']);
        return true;
      } else {
        showErrorToast(response['message']);
        return false;
      }
    } catch (e) {
      log(e.toString());
      return false;
    }
  }

  Future<bool> deleteWorkingTime(String id) async {
    try {
      final response =
          await helper.postData(deleteProviderWorkingTime + id, {"": null});
      if (response['success'] == true) {
        return true;
      } else {
        return false;
      }
    } catch (e) {
      log(e.toString());
      return false;
    }
  }

  Future<bool> setHoliday(data) async {
    try {
      final response = await helper.postData(setProviderHoliday, data);
      log(jsonEncode(data));
      log(jsonEncode(response));
      if (response['success'] == true) {
        return true;
      }
      showErrorToast(response['message']);
      return false;
    } catch (e) {
      log(e.toString());
      return false;
    }
  }

  Future<bool> deleteHoliday(String id) async {
    try {
      final response = await helper.postData(
        deleteProviderHoliday + id,
        {"": null},
      );
      if (response['success'] == true) {
        return true;
      } else {
        return false;
      }
    } catch (e) {
      log(e.toString());
      return false;
    }
  }
}
