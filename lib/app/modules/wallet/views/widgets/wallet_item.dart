import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/constants/theme.dart';
import 'package:get_clean/global/models/wallet_model.dart';

import '../../../../../global/controllers/language_controller.dart';

class WalletItem extends StatelessWidget {
  final AllOrderModel order;
  final int index;
  const WalletItem({
    Key? key,
    required this.order,
    required this.index,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    log('asflakfaslfsasafa ${order.totalPrice}');
    return Container(
      padding: const EdgeInsets.all(5),
      margin: const EdgeInsets.all(5),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey[400]!,
            blurRadius: 1,
          ),
        ],
        borderRadius: BorderRadius.circular(5),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Center(
            child: CircleAvatar(
              backgroundColor: primaryColor,
              radius: 10,
              child: Text(
                (index + 1).toString(),
                style: regularWhiteTextStyle,
              ),
            ),
          ),
          const SizedBox(width: 10),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  order.user ?? '',
                ),
                Text(
                  order.date?.first ?? '',
                  style: smallGreyTextStyle,
                ),
              ],
            ),
          ),
          const SizedBox(width: 10),
          Text(
            order.providerTotalPrice!.toStringAsFixed(2) +
                Get.find<LanguageController>().keys.value.ils!,
            style: regularTextStyle,
          ),
        ],
      ),
    );
  }
}
