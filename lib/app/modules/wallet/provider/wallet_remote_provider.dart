import 'dart:developer';

import 'package:dio/dio.dart' as d;
import 'package:get_clean/app/modules/wallet/controllers/states/wallet_state.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/dio/dio_helper.dart';
import 'package:get_clean/global/help_functions/help_functions.dart';
import 'package:get_clean/global/models/wallet_model.dart';

class WalletRemoteProvider {
  DioHelper helper = DioHelper();

  Future<WalletState> getWallet() async {
    try {
      final response = await helper.getData(providerWalletURL);

      final response2 = await helper.getData(getProviderAllOrdersURL);

      if (response['success'] == true && response2['success'] == true) {
        log('WalletDataaa ${response}\nWalletDataaa222 ${response2}');

        return WalletSuccess(WalletModel.fromJson(response),
            DoneAndCompletedOrderModel.fromJson(response2['data']));
      } else {
        return WalletFailed(
          response['message'],
        );
      }
    } catch (e, s) {
      log('WalletDataaaError ${e.toString()} ${s.toString()}');
      return WalletFailed(e.toString());
    }
  }

  Future<WalletState> uploadInvoice(String path) async {
    try {
      final response = await helper.postData(
        uploadInvoiceURL,
        {
          "invoice": await d.MultipartFile.fromFile(
            path,
            filename: 'invoice.png',
          ),
        },
      );
      if (response['success'] == true) {
        showSuccessToast(response['message']);
        return WalletSuccess(WalletModel.fromJson(response));
      } else {
        showErrorToast(response['message']);
        return WalletFailed(response['message']);
      }
    } catch (e) {
      return WalletFailed(e.toString());
    }
  }
}
