import 'package:calendar_date_picker2/calendar_date_picker2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/jobs/controllers/jobs_controller.dart';
import 'package:get_clean/app/modules/jobs/views/calendar/widgets/calendar_job_tile.dart';
import 'package:get_clean/app/routes/app_pages.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/controllers/language_controller.dart';
import 'package:get_clean/global/widget/loading_widget.dart';

class CalendarJobsView extends StatelessWidget {
  const CalendarJobsView({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<JobsController>(
      builder: (controller) {
        return Scaffold(
          appBar: AppBar(
            title: Text(
              Get.find<LanguageController>().keys.value.calendarJobs ??
                  'Calendar Jobs',
              style: const TextStyle(color: Colors.white),
            ),
            backgroundColor: primaryColor,
            elevation: 0,
            centerTitle: true,
            iconTheme: const IconThemeData(color: Colors.white),
            actions: [
              IconButton(
                icon: const Icon(
                  Icons.assessment,
                  color: Colors.orangeAccent,
                ),
                onPressed: () {
                  Get.toNamed(Routes.CALENDAR_REPORTS_VIEW);
                },
                tooltip:
                    Get.find<LanguageController>().keys.value.financialReport ??
                        'Financial Report',
              ),
            ],
          ),
          body: Column(
            children: [
              // Calendar
              Container(
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                child: CalendarDatePicker2(
                  config: CalendarDatePicker2Config(
                    calendarType: CalendarDatePicker2Type.single,
                    firstDayOfWeek: 1,
                    selectedDayTextStyle: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.w700,
                    ),
                    selectedDayHighlightColor: primaryColor,
                    centerAlignModePicker: true,
                    customModePickerIcon: const Padding(
                      padding: EdgeInsets.all(8.0),
                      child: Icon(Icons.arrow_drop_down_circle_outlined),
                    ),
                    dayBuilder: ({
                      required date,
                      textStyle,
                      decoration,
                      isSelected,
                      isDisabled,
                      isToday,
                    }) {
                      return _buildDayWidget(
                        controller,
                        date,
                        textStyle,
                        decoration,
                        isSelected,
                        isDisabled,
                        isToday,
                      );
                    },
                  ),
                  value: controller.selectedDate,
                  onValueChanged: (dates) {
                    if (dates.isNotEmpty) {
                      controller.onDateSelected(dates.first);
                    }
                  },
                  onDisplayedMonthChanged: (date) {
                    controller.onMonthChanged(date);
                  },
                ),
              ),

              // Divider
              const Divider(),

              controller.isLoadingCalendar.value
                  ? const LoadingWidget()
                  :
                  // Selected date jobs
                  Expanded(
                      child: Padding(
                        padding: EdgeInsets.all(16.w),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '${Get.find<LanguageController>().keys.value.jobsFor ?? 'Jobs for'} ${_formatDate(controller.selectedDate.first)}',
                              style: TextStyle(
                                fontSize: 16.sp,
                                fontWeight: FontWeight.bold,
                                color: primaryColor,
                              ),
                            ),
                            SizedBox(height: 10.h),
                            Expanded(
                              child: controller.selectedDateJobs.isEmpty
                                  ? Center(
                                      child: Text(
                                        Get.find<LanguageController>()
                                                .keys
                                                .value
                                                .noJobsForThisDay ??
                                            'No jobs for this day',
                                        style: TextStyle(
                                          fontSize: 14.sp,
                                          color: Colors.grey,
                                        ),
                                      ),
                                    )
                                  : ListView.builder(
                                      itemCount:
                                          controller.selectedDateJobs.length,
                                      itemBuilder: (context, index) {
                                        final job =
                                            controller.selectedDateJobs[index];
                                        return CalendarJobTile(
                                          job: job,
                                          onStartTask: () => controller
                                              .updateTaskTime(job: job),
                                          onEndTask: () =>
                                              controller.updateTaskTime(
                                            job: job,
                                            endTime: _getCurrentTimeString(),
                                          ),
                                        );
                                      },
                                    ),
                            ),
                          ],
                        ),
                      ),
                    ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDayWidget(
    JobsController controller,
    DateTime date,
    TextStyle? textStyle,
    BoxDecoration? decoration,
    bool? isSelected,
    bool? isDisabled,
    bool? isToday,
  ) {
    final jobsCount = controller.getJobsCountForDate(date);

    return Container(
      decoration: decoration,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            '${date.day}',
            style: textStyle,
          ),
          if (jobsCount > 0) ...[
            SizedBox(height: 2.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(
                jobsCount > 3 ? 3 : jobsCount,
                (index) => Container(
                  width: 4.w,
                  height: 4.h,
                  margin: EdgeInsets.symmetric(horizontal: 1.w),
                  decoration: BoxDecoration(
                    color: isSelected == true ? Colors.white : Colors.green,
                    // primaryColor,
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December'
    ];

    return '${date.day} ${months[date.month - 1]} ${date.year}';
  }

  String _getCurrentTimeString() {
    final now = DateTime.now();
    final hour = now.hour % 12 == 0 ? 12 : now.hour % 12;
    final amPm = now.hour >= 12 ? 'PM' : 'AM';
    return '$hour:${now.minute.toString().padLeft(2, '0')} $amPm';
  }
}
