import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/jobs/models/job_application_model.dart';
import 'package:get_clean/global/controllers/language_controller.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import '../../models/job_model.dart';

class JobApplicationCard extends StatelessWidget {
  final JobApplication jobApplication;

  const JobApplicationCard({
    super.key,
    required this.jobApplication,
  });

  @override
  Widget build(BuildContext context) {
    final langController = Get.find<LanguageController>();
    final isProvider = jobApplication.provider != null;

    return Card(
      color: Colors.white,
      margin: EdgeInsets.only(bottom: 16.h),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
      ),
      child: ExpansionTile(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        title: Text(
          jobApplication.service?.name ?? '',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            // color: primaryColor,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 5.h),
            // Show user name for providers, provider name for users
            if (isProvider && jobApplication.user != null)
              Text(
                '${langController.keys.value.name ?? 'Name'}: ${jobApplication.user?.name ?? ''}',
                style: TextStyle(
                  fontSize: 12.sp,
                ),
              ),
            if (!isProvider && jobApplication.provider != null)
              Text(
                '${langController.keys.value.providerName ?? 'Provider'}: ${jobApplication.provider?.name ?? ''}',
                style: TextStyle(
                  fontSize: 12.sp,
                ),
              ),
            SizedBox(height: 5.h),
            Text(
              '${langController.keys.value.status ?? 'Status'}${jobApplication.status ?? ''}',
              style: TextStyle(
                fontSize: 12.sp,
                color: _getStatusColor(jobApplication.status),
              ),
            ),
            SizedBox(height: 5.h),
          ],
        ),
        children: [
          Padding(
            padding: EdgeInsets.all(16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Work Days
                Text(
                  '${langController.keys.value.workDays ?? 'Work Days'}:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14.sp,
                  ),
                ),
                SizedBox(height: 5.h),
                _buildScheduleList(),
                SizedBox(height: 10.h),

                // Cost
                Text(
                  '${langController.keys.value.cost ?? 'Cost'}: ${jobApplication.providerFixedCost ?? 0}',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14.sp,
                  ),
                ),
                SizedBox(height: 10.h),

                // Notes if available
                if (jobApplication.notes != null &&
                    jobApplication.notes!.isNotEmpty) ...[
                  Text(
                    '${langController.keys.value.notes ?? 'Notes'}:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14.sp,
                    ),
                  ),
                  SizedBox(height: 5.h),
                  Text(
                    jobApplication.notes ?? '',
                    style: TextStyle(
                      fontSize: 12.sp,
                    ),
                  ),
                  SizedBox(height: 10.h),
                ],

                // Location map for providers
                if (isProvider && jobApplication.location != null) ...[
                  Text(
                    '${langController.keys.value.locationName ?? 'Location'}: ${jobApplication.location?.name ?? ''}',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14.sp,
                    ),
                  ),
                  SizedBox(height: 10.h),
                  _buildLocationMap(),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildScheduleList() {
    if (jobApplication.schedule == null || jobApplication.schedule!.isEmpty) {
      return const Text('No schedule available');
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: jobApplication.schedule!.map((day) {
        return Padding(
          padding: EdgeInsets.only(bottom: 5.h),
          child: Text(
            '${day.day}: ${_formatScheduleHours(day)}',
            style: TextStyle(
              fontSize: 12.sp,
            ),
          ),
        );
      }).toList(),
    );
  }

  String _formatScheduleHours(Schedule day) {
    if (day.hours == null || day.hours!.isEmpty) {
      return 'No hours set';
    }

    return day.hours!.map((hour) {
      return '${hour.startTime} (${hour.duration} ${Get.find<LanguageController>().keys.value.hours ?? 'hours'})';
    }).join(', ');
  }

  Widget _buildLocationMap() {
    if (jobApplication.location == null ||
        jobApplication.location!.latitude == null ||
        jobApplication.location!.longitude == null) {
      return const Text('Location coordinates not available');
    }

    try {
      final lat = double.parse(jobApplication.location!.latitude!);
      final lng = double.parse(jobApplication.location!.longitude!);

      return Container(
        height: 150.h,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(10),
          child: GoogleMap(
            initialCameraPosition: CameraPosition(
              target: LatLng(lat, lng),
              zoom: 14,
            ),
            markers: {
              Marker(
                markerId: const MarkerId('location'),
                position: LatLng(lat, lng),
              ),
            },
            zoomControlsEnabled: false,
            mapToolbarEnabled: false,
            myLocationButtonEnabled: false,
          ),
        ),
      );
    } catch (e) {
      return const Text('Invalid location coordinates');
    }
  }

  Color _getStatusColor(String? status) {
    if (status == null) return Colors.grey;

    switch (status.toLowerCase()) {
      case 'pending':
        return Colors.orange;
      case 'approved':
        return Colors.green;
      case 'rejected':
        return Colors.red;
      case 'completed':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }
}
