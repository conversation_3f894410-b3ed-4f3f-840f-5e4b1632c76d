import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/jobs/controllers/jobs_controller.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/controllers/language_controller.dart';
import 'package:get_clean/global/models/provider_services.dart';
import 'package:get_clean/global/widget/custom_button.dart';
import 'package:get_clean/global/widget/custom_drop_down_button.dart';
import 'package:get_clean/global/widget/custom_form_field.dart';

import '../../../../../global/models/work_location_model.dart';

class JobForm extends StatelessWidget {
  const JobForm({
    Key? key,
  }) : super(key: key);

  // Helper method to build day schedule row
  Widget _buildDaySchedule(
      BuildContext context,
      String dayKey,
      String dayLabel,
      TextEditingController startTimeController,
      TextEditingController durationController,
      JobsController controller) {
    return Obx(() {
      final isExpanded = controller.expandedDays[dayKey]?.value ?? false;

      return Container(
        margin: const EdgeInsets.only(bottom: 10),
        padding: const EdgeInsets.all(10),
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(10),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Day header with toggle button
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  dayLabel,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  icon:
                      Icon(isExpanded ? Icons.remove_circle : Icons.add_circle),
                  color: primaryColor,
                  onPressed: () => controller.toggleDayVisibility(dayKey),
                ),
              ],
            ),

            // Expanded content
            if (isExpanded) ...[
              SizedBox(height: 10.h),
              Row(
                children: [
                  Expanded(
                    child: GestureDetector(
                      onTap: () =>
                          _showTimePicker(context, startTimeController),
                      child: CustomFormField(
                        controller: startTimeController,
                        label: Get.find<LanguageController>()
                                .keys
                                .value
                                .startTime ??
                            'Start Time',
                        hint: '00:00',
                        enabled: false,
                        suffixIcon: const Icon(Icons.access_time),
                        onSuffixIconTap: () =>
                            _showTimePicker(context, startTimeController),
                      ),
                    ),
                  ),
                  SizedBox(width: 10.w),
                  Expanded(
                    child: CustomFormField(
                      controller: durationController,
                      label:
                          Get.find<LanguageController>().keys.value.duration ??
                              'Duration',
                      hint: '0',
                      keyboardType: TextInputType.number,
                      suffixIcon: Padding(
                        padding: const EdgeInsets.only(top: 12),
                        child: Text(
                          Get.find<LanguageController>().keys.value.hours ??
                              'Hours',
                          style: const TextStyle(color: primaryColor),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      );
    });
  }

  // Show time picker
  void _showTimePicker(
      BuildContext context, TextEditingController controller) async {
    final TimeOfDay? pickedTime = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
      builder: (BuildContext context, Widget? child) {
        return MediaQuery(
          data: MediaQuery.of(context).copyWith(alwaysUse24HourFormat: true),
          child: child!,
        );
      },
    );

    if (pickedTime != null) {
      // Format time as HH:MM
      final hour = pickedTime.hour.toString().padLeft(2, '0');
      final minute = pickedTime.minute.toString().padLeft(2, '0');
      controller.text = '$hour:$minute';
    }
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<JobsController>(
      builder: (controller) {
        return Container(
          height: 700.h,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(60),
              topRight: Radius.circular(60),
            ),
          ),
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      Container(
                        height: 2.h,
                        width: 111.w,
                        decoration: const BoxDecoration(
                          color: primaryColor,
                        ),
                      ),
                      SizedBox(height: 20.h),

                      // Service Dropdown
                      CustomDropDownButton(
                        label:
                            Get.find<LanguageController>().keys.value.service!,
                        hint: Get.find<LanguageController>()
                                .keys
                                .value
                                .selectService ??
                            'Select Service',
                        value: controller.selectedService.value,
                        onChanged: (value) => controller
                            .onServiceChanged(value as ProviderServices?),
                        items: controller.services
                            .map((service) => DropdownMenuItem(
                                  value: service,
                                  child: Text(
                                    service.name ?? '',
                                    style: const TextStyle(fontSize: 14),
                                  ),
                                ))
                            .toList(),
                        buttonHeight: 50.h,
                        buttonWidth: Get.width,
                      ),

                      // Location Dropdown
                      CustomDropDownButton(
                        label: Get.find<LanguageController>()
                                .keys
                                .value
                                .locationName ??
                            'Location',
                        hint: Get.find<LanguageController>()
                                .keys
                                .value
                                .selectLocation ??
                            'Select Location',
                        value: controller.selectedLocation.value,
                        onChanged: (value) => controller
                            .onLocationChanged(value as WorkLocation?),
                        items: controller.locations
                            .map((location) => DropdownMenuItem(
                                  value: location,
                                  child: Text(
                                    location.name,
                                    style: const TextStyle(fontSize: 14),
                                  ),
                                ))
                            .toList(),
                        buttonHeight: 50.h,
                        buttonWidth: Get.width,
                      ),

                      // CustomDropDownButton(
                      //   label:
                      //       Get.find<LanguageController>().keys.value.locationName ??
                      //           'Location',
                      //   hint: Get.find<LanguageController>()
                      //           .keys
                      //           .value
                      //           .selectLocation ??
                      //       'Select Location',
                      //   value: controller.selectedLocation.value,
                      //   onChanged: controller.onLocationChanged,
                      //   items: controller.locations
                      //       .map((location) => DropdownMenuItem(
                      //             value: location,
                      //             child: Text(
                      //               location.name,
                      //               style: const TextStyle(fontSize: 14),
                      //             ),
                      //           ))
                      //       .toList(),
                      //   buttonHeight: 50.h,
                      //   buttonWidth: Get.width,
                      // ),

                      // Notes Field
                      CustomFormField(
                        controller: controller.notesController,
                        label:
                            Get.find<LanguageController>().keys.value.notes ??
                                'Notes',
                        hint: Get.find<LanguageController>().keys.value.notes ??
                            'Enter notes',
                        maxLines: 3,
                      ),

                      // Schedule Section
                      Text(
                        Get.find<LanguageController>().keys.value.schedule ??
                            'Schedule',
                        style: const TextStyle(
                          color: primaryColor,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),

                      SizedBox(height: 10.h),

                      // Saturday Schedule
                      _buildDaySchedule(
                        context,
                        'saturday',
                        Get.find<LanguageController>().keys.value.saturday ??
                            'Saturday',
                        controller.saturdayStartTimeController,
                        controller.saturdayDurationController,
                        controller,
                      ),

                      // Sunday Schedule
                      _buildDaySchedule(
                        context,
                        'sunday',
                        Get.find<LanguageController>().keys.value.sunday ??
                            'Sunday',
                        controller.sundayStartTimeController,
                        controller.sundayDurationController,
                        controller,
                      ),

                      // Monday Schedule
                      _buildDaySchedule(
                        context,
                        'monday',
                        Get.find<LanguageController>().keys.value.monday ??
                            'Monday',
                        controller.mondayStartTimeController,
                        controller.mondayDurationController,
                        controller,
                      ),

                      // Tuesday Schedule
                      _buildDaySchedule(
                        context,
                        'tuesday',
                        Get.find<LanguageController>().keys.value.tuesday ??
                            'Tuesday',
                        controller.tuesdayStartTimeController,
                        controller.tuesdayDurationController,
                        controller,
                      ),

                      // Wednesday Schedule
                      _buildDaySchedule(
                        context,
                        'wednesday',
                        Get.find<LanguageController>().keys.value.wednesday ??
                            'Wednesday',
                        controller.wednesdayStartTimeController,
                        controller.wednesdayDurationController,
                        controller,
                      ),

                      // Thursday Schedule
                      _buildDaySchedule(
                        context,
                        'thursday',
                        Get.find<LanguageController>().keys.value.thursday ??
                            'Thursday',
                        controller.thursdayStartTimeController,
                        controller.thursdayDurationController,
                        controller,
                      ),

                      // Friday Schedule
                      _buildDaySchedule(
                        context,
                        'friday',
                        Get.find<LanguageController>().keys.value.friday ??
                            'Friday',
                        controller.fridayStartTimeController,
                        controller.fridayDurationController,
                        controller,
                      ),
                    ],
                  ),
                ),
              ),
              CustomButton(
                label: (Get.find<LanguageController>().keys.value.add ?? 'Add'),
                onTap: () async {
                  final result = await controller.addJob();
                  if (result) {
                    Get.back();
                  }
                },
                height: 43.h,
                width: 129.w,
              ),
            ],
          ),
        );
      },
    );
  }
}
