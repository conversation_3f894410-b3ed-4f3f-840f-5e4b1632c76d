import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/jobs/controllers/jobs_controller.dart';
import 'package:get_clean/app/modules/jobs/views/widgets/job_application_card.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/controllers/language_controller.dart';
import 'package:get_clean/global/widget/loading_widget.dart';

class JobApplicationsView extends StatelessWidget {
  const JobApplicationsView({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<JobsController>(
      builder: (controller) {
        return Scaffold(
          appBar: AppBar(
            title: Text(
              Get.find<LanguageController>().keys.value.jobApplications ??
                  'Job Applications',
              style: const TextStyle(color: Colors.white),
            ),
            backgroundColor: primaryColor,
            elevation: 0,
            centerTitle: true,
            iconTheme: const IconThemeData(color: Colors.white),
          ),
          body: controller.isLoadingApplications.value
              ? const LoadingWidget()
              : controller.jobApplications.isEmpty
                  ? Center(
                      child: Text(
                        Get.find<LanguageController>()
                                .keys
                                .value
                                .noJobApplicationsFound ??
                            'No Job Applications Found',
                        style: const TextStyle(fontSize: 16),
                      ),
                    )
                  : RefreshIndicator(
                      onRefresh: () => controller.fetchJobApplications(),
                      child: ListView.builder(
                        padding: EdgeInsets.all(16.w),
                        itemCount: controller.jobApplications.length,
                        itemBuilder: (context, index) {
                          final jobApplication =
                              controller.jobApplications[index];
                          return JobApplicationCard(
                            jobApplication: jobApplication,
                          );
                        },
                      ),
                    ),
        );
      },
    );
  }
}
