import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/jobs/controllers/jobs_controller.dart';
import 'package:get_clean/app/modules/jobs/models/job_model.dart';
import 'package:get_clean/app/modules/jobs/views/widgets/job_form.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/controllers/language_controller.dart';

class JobsView extends StatefulWidget {
  const JobsView({Key? key}) : super(key: key);

  @override
  State<JobsView> createState() => _JobsViewState();
}

class _JobsViewState extends State<JobsView> {
  final controller = Get.find<JobsController>();

  // Helper method to get translated day name
  String _getTranslatedDay(String day) {
    final langController = Get.find<LanguageController>();

    switch (day.toLowerCase()) {
      case 'monday':
        return langController.keys.value.monday ?? 'Monday';
      case 'tuesday':
        return langController.keys.value.tuesday ?? 'Tuesday';
      case 'wednesday':
        return langController.keys.value.wednesday ?? 'Wednesday';
      case 'thursday':
        return langController.keys.value.thursday ?? 'Thursday';
      case 'friday':
        return langController.keys.value.friday ?? 'Friday';
      case 'saturday':
        return langController.keys.value.saturday ?? 'Saturday';
      case 'sunday':
        return langController.keys.value.sunday ?? 'Sunday';
      default:
        return day;
    }
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.fetchJobs();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          Get.find<LanguageController>().keys.value.jobs ?? 'Jobs',
          style: const TextStyle(color: Colors.white),
        ),
        backgroundColor: primaryColor,
        elevation: 0,
        centerTitle: true,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }

        if (controller.jobs.isEmpty) {
          return Center(
            child: Text(
              Get.find<LanguageController>().keys.value.noJobsFound ??
                  'No jobs found',
              style: const TextStyle(fontSize: 16),
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: controller.jobs.length,
          itemBuilder: (context, index) {
            final job = controller.jobs[index];
            return _buildJobCard(job);
          },
        );
      }),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          controller.resetForm();
          _showAddJobBottomSheet();
        },
        backgroundColor: primaryColor,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildJobCard(Job job) {
    return Card(
      color: Colors.white,
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    job.service?.name ?? '',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Column(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: _getStatusColor(job.status),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        job.status ?? 'pending',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 8),
            if (job.location != null)
              Row(
                children: [
                  const Icon(Icons.location_on, size: 16, color: Colors.grey),
                  const SizedBox(width: 4),
                  Text(
                    job.location!.name,
                    style: const TextStyle(fontSize: 14, color: Colors.grey),
                  ),
                ],
              ),
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Expanded(
                  child: Text(
                    '${Get.find<LanguageController>().keys.value.notes ?? 'Notes'}: ${job.notes?.isEmpty ?? true ? '-' : job.notes!}',
                    style: const TextStyle(fontSize: 14),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(
                      top:
                          job.notes != null && job.notes!.isNotEmpty ? 0 : 8.0),
                  child: GestureDetector(
                    onTap: () => _showDeleteConfirmation(job),
                    child: const Icon(Icons.delete, color: Colors.red),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            if (job.schedule != null && job.schedule!.isNotEmpty) ...[
              const Divider(),
              Text(
                Get.find<LanguageController>().keys.value.schedule ??
                    'Schedule',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              ...job.schedule!.map((schedule) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 4),
                    Text(
                      _getTranslatedDay(schedule.day ?? ''),
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (schedule.hours != null && schedule.hours!.isNotEmpty)
                      ...schedule.hours!.map((hour) {
                        return Padding(
                          padding: const EdgeInsets.only(top: 4),
                          child: Text(
                            '${hour.startTime} - ${hour.endTime} (${hour.duration} ${Get.find<LanguageController>().keys.value.hours ?? 'hours'})',
                            style: const TextStyle(fontSize: 14),
                          ),
                        );
                      }),
                  ],
                );
              }),
            ],
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(String? status) {
    switch (status) {
      case 'pending':
        return Colors.orange;
      case 'approved':
        return Colors.green;
      case 'rejected':
        return Colors.red;
      case 'completed':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  void _showAddJobBottomSheet() {
    Get.bottomSheet(
      const JobForm(),
      isScrollControlled: true,
      isDismissible: true,
      enableDrag: true,
    );
  }

  void _showDeleteConfirmation(Job job) {
    Get.dialog(
      AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        title: Text(Get.find<LanguageController>().keys.value.deleteJob ??
            'Delete Job'),
        content: Text(
          Get.find<LanguageController>().keys.value.deleteJobConfirmation ??
              'Are you sure you want to delete this job?',
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text(
              Get.find<LanguageController>().keys.value.cancel ?? 'Cancel',
              style: const TextStyle(color: Colors.black),
            ),
          ),
          TextButton(
            onPressed: () async {
              Get.back();
              if (job.id != null) {
                await controller.deleteJob(job.id!);
              }
            },
            child: Text(
              Get.find<LanguageController>().keys.value.delete ?? 'Delete',
              style: const TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );
  }
}
