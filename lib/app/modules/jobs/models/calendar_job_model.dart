class CalendarJobsResponse {
  final bool success;
  final String month;
  final List<CalendarJob> data;

  CalendarJobsResponse({
    required this.success,
    required this.month,
    required this.data,
  });

  factory CalendarJobsResponse.fromJson(Map<String, dynamic> json) {
    return CalendarJobsResponse(
      success: json['success'] ?? false,
      month: json['month'] ?? '',
      data: json['data'] != null
          ? List<CalendarJob>.from(
              json['data'].map((x) => CalendarJob.fromJson(x)))
          : [],
    );
  }
}

class CalendarJob {
  final String type; // "order" or "job_application"
  final int? orderId;
  final int? jobApplicationId;
  final int? dId;
  final String date;
  final String day;
  final String startTime;
  final String endTime;
  final String status;
  final Task? task;
  final CalendarService? service;
  final Location? location;
  final String? address;
  final String? lat;
  final String? long;

  CalendarJob({
    this.type = '',
    this.orderId,
    this.jobApplicationId,
    this.dId,
    this.date = '',
    this.day = '',
    this.startTime = '',
    this.endTime = '',
    this.status = '',
    this.task,
    this.service,
    this.location,
    this.address,
    this.lat,
    this.long,
  });

  factory CalendarJob.fromJson(Map<String, dynamic> json) {
    return CalendarJob(
      type: json['type'] ?? '',
      orderId: json['order_id'],
      jobApplicationId: json['job_application_id'],
      dId: json['d_id'],
      date: json['date'] ?? '',
      day: json['day'] ?? '',
      startTime: json['start_time'] ?? '',
      endTime: json['end_time'] ?? '',
      status: json['status'] ?? '',
      task: json['task'] != null ? Task.fromJson(json['task']) : null,
      service: json['service'] != null
          ? CalendarService.fromJson(json['service'])
          : null,
      location:
          json['location'] != null ? Location.fromJson(json['location']) : null,
      address: json['address'],
      lat: json['lat'],
      long: json['long'],
    );
  }

  // Helper method to get parsed date
  DateTime get parsedDate {
    try {
      if (type == 'order') {
        return DateTime.parse(date);
      } else {
        return DateTime.parse(date);
      }
    } catch (e) {
      return DateTime.now();
    }
  }

  // Helper method to get display start time
  String get displayStartTime {
    if (task != null) {
      return task!.startTime;
    }
    if (type == 'order') {
      try {
        final dateTime = DateTime.parse(startTime);
        return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
      } catch (e) {
        return startTime;
      }
    }
    return startTime;
  }

  // Helper method to get display end time
  String get displayEndTime {
    if (task != null) {
      return task!.endTime;
    }
    if (type == 'order') {
      try {
        final dateTime = DateTime.parse(endTime);
        return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
      } catch (e) {
        return endTime;
      }
    }
    return endTime;
  }

  // Helper method to get status color
  String get statusColor {
    switch (status.toLowerCase()) {
      case 'approved':
        return '#4CAF50'; // Green
      case 'absent':
        return '#F44336'; // Red
      case 'not_started':
        return '#FF9800'; // Orange
      case 'ended':
        return '#2196F3'; // Blue
      default:
        return '#9E9E9E'; // Grey
    }
  }

  // Helper method to check if work can be started
  bool get canStart {
    return type == 'job_application' &&
        task == null &&
        status.toLowerCase() != 'ended';
  }

  // Helper method to check if work can be ended
  bool get canEnd {
    return type == 'job_application' &&
        task != null &&
        status.toLowerCase() != 'ended';
  }

  // Helper method to get location coordinates for orders
  double? get orderLatitude {
    if (type == 'order' && lat != null) {
      try {
        return double.parse(lat!.split(':')[0]);
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  double? get orderLongitude {
    if (type == 'order' && long != null) {
      try {
        return double.parse(long!.split(':')[0]);
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  // Helper method to get location coordinates for job applications
  double? get jobLatitude {
    if (type == 'job_application' && location != null) {
      try {
        return double.parse(location!.latitude);
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  double? get jobLongitude {
    if (type == 'job_application' && location != null) {
      try {
        return double.parse(location!.longitude);
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  // Helper method to get the appropriate latitude
  double? get latitude {
    return type == 'order' ? orderLatitude : jobLatitude;
  }

  // Helper method to get the appropriate longitude
  double? get longitude {
    return type == 'order' ? orderLongitude : jobLongitude;
  }
}

class Task {
  final int id;
  final String startTime;
  final String endTime;

  Task({
    required this.id,
    required this.startTime,
    required this.endTime,
  });

  factory Task.fromJson(Map<String, dynamic> json) {
    return Task(
      id: json['id'] ?? 0,
      startTime: json['start_time'] ?? '',
      endTime: json['end_time'] ?? '',
    );
  }
}

class CalendarService {
  final int id;
  final String name;
  final String? image;

  CalendarService({
    required this.id,
    required this.name,
    this.image,
  });

  factory CalendarService.fromJson(Map<String, dynamic> json) {
    return CalendarService(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      image: json['image'] ?? '',
    );
  }
}

class PricingOption {
  final int id;
  final String name;
  final bool hasTypes;
  final List<OptionType>? optionTypes;

  PricingOption({
    required this.id,
    required this.name,
    required this.hasTypes,
    this.optionTypes,
  });

  factory PricingOption.fromJson(Map<String, dynamic> json) {
    return PricingOption(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      hasTypes: json['has_types'] ?? false,
      optionTypes: json['option_types'] != null
          ? List<OptionType>.from(
              json['option_types'].map((x) => OptionType.fromJson(x)))
          : null,
    );
  }
}

class OptionType {
  final int id;
  final String name;
  final String? image;

  OptionType({
    required this.id,
    required this.name,
    this.image,
  });

  factory OptionType.fromJson(Map<String, dynamic> json) {
    return OptionType(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      image: json['image'],
    );
  }
}

class TypeSetting {
  final int id;
  final String name;
  final String? estimatedDuration;

  TypeSetting({
    required this.id,
    required this.name,
    this.estimatedDuration,
  });

  factory TypeSetting.fromJson(Map<String, dynamic> json) {
    return TypeSetting(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      estimatedDuration: json['estimated_duration'],
    );
  }
}

class Location {
  final int id;
  final String name;
  final String latitude;
  final String longitude;

  Location({
    required this.id,
    required this.name,
    required this.latitude,
    required this.longitude,
  });

  factory Location.fromJson(Map<String, dynamic> json) {
    return Location(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      latitude: json['latitude'] ?? '',
      longitude: json['longitude'] ?? '',
    );
  }
}

enum JobStatus {
  approved,
  absent,
  notStarted,
  ended,
  confirmed;

  static JobStatus fromString(String status) {
    switch (status.toLowerCase()) {
      case 'approved':
        return JobStatus.approved;
      case 'absent':
        return JobStatus.absent;
      case 'not_started':
        return JobStatus.notStarted;
      case 'ended':
        return JobStatus.ended;
      case 'confirmed':
        return JobStatus.confirmed;
      default:
        return JobStatus.notStarted;
    }
  }

  String get displayName {
    switch (this) {
      case JobStatus.approved:
        return 'Approved';
      case JobStatus.absent:
        return 'Absent';
      case JobStatus.notStarted:
        return 'Not Started';
      case JobStatus.ended:
        return 'Ended';
      case JobStatus.confirmed:
        return 'Confirmed';
    }
  }
}
