import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/constants/theme.dart';
import 'package:get/get.dart';
import '../../../../../global/controllers/language_controller.dart';
import '../../../../../global/models/user_billings.dart';

class UserWalletItem extends StatelessWidget {
  final BillingData billing;
  final int index;
  const UserWalletItem({Key? key, required this.billing, required this.index})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(5),
      margin: const EdgeInsets.all(5),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey[400]!,
            blurRadius: 1,
          ),
        ],
        borderRadius: BorderRadius.circular(5),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CircleAvatar(
            backgroundColor: primaryColor,
            radius: 12,
            child: Text(
              (index + 1).toString(),
              style: smallWhiteTextStyle,
            ),
          ),
          const SizedBox(width: 10),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (billing.isConfirmed!)
                  const Icon(
                    FontAwesomeIcons.check,
                  ),
                if (!billing.isConfirmed!)
                  const Icon(
                    FontAwesomeIcons.minus,
                  ),
                Text(
                  billing.date!,
                  style: smallGreyTextStyle,
                ),
              ],
            ),
          ),
          const SizedBox(width: 10),
          Text(
            billing.amount!.toStringAsFixed(2) +
                Get.find<LanguageController>().keys.value.ils!,
            style: regularTextStyle,
          ),
        ],
      ),
    );
  }
}
