import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../global/constants/constants.dart';
import '../../../../global/constants/theme.dart';
import '../../../../global/controllers/language_controller.dart';
import '../../../../global/widget/loading_widget.dart';
import '../controllers/states/user_wallet_state.dart';
import '../controllers/user_wallet_controller.dart';
import 'widgets/user_wallet_item.dart';

class UserWalletView extends GetView<UserWalletController> {
  const UserWalletView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return GetBuilder<UserWalletController>(builder: (controller) {
      if (controller.state.value is UserWalletLoading) {
        return const Scaffold(
          body: LoadingWidget(),
        );
      }
      return DefaultTabController(
        length: 2,
        child: Scaffold(
          backgroundColor: Colors.white,
          bottomSheet: Container(
            color: Colors.white,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(25),
                  topRight: Radius.circular(25),
                ),
                boxShadow: [
                  BoxShadow(
                    blurRadius: 4,
                    color: Colors.grey[400]!,
                  ),
                ],
              ),
              height: 60.h,
              width: Get.width,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 15.w,
                      vertical: 2.h,
                    ),
                    margin: const EdgeInsets.all(15),
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: primaryColor,
                    ),
                    child: Text(
                      Get.find<LanguageController>().keys.value.total!,
                      style: regularWhiteTextStyle,
                    ),
                  ),
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 15.w,
                      vertical: 2.h,
                    ),
                    margin: const EdgeInsets.all(15),
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: primaryColor,
                    ),
                    child: Text(
                      controller.choosedIndex.value == 0
                          ? controller.deposit.toStringAsFixed(2) +
                              Get.find<LanguageController>().keys.value.ils!
                          : controller.totalAmount.toStringAsFixed(2) +
                              Get.find<LanguageController>().keys.value.ils!,
                      style: regularWhiteTextStyle,
                    ),
                  ),
                ],
              ),
            ),
          ),
          body: Container(
            width: Get.width,
            height: Get.height,
            padding: const EdgeInsets.all(10),
            decoration: const BoxDecoration(
              image: DecorationImage(
                image: AssetImage(
                  'assets/images/main_background.png',
                ),
                fit: BoxFit.fill,
              ),
            ),
            child: SafeArea(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      IconButton(
                        onPressed: Get.back,
                        icon: const Icon(
                          CupertinoIcons.back,
                          size: 30,
                        ),
                      ),
                      Expanded(
                        child: Text(
                          Get.find<LanguageController>().keys.value.wallet!,
                          style: bigTextStyle,
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 20.h),
                  Container(
                    padding: const EdgeInsets.all(10),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          margin: const EdgeInsets.symmetric(vertical: 10),
                          padding: const EdgeInsets.all(10),
                          width: Get.width,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            border: Border.all(color: primaryColor, width: 0.5),
                            borderRadius: BorderRadius.circular(30),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.grey[400]!,
                                blurRadius: 4,
                              ),
                            ],
                          ),
                          child: Column(
                            children: [
                              Image.asset(
                                'assets/images/digital-wallet.png',
                                height: 109.h,
                                width: 109.w,
                                fit: BoxFit.fill,
                              ),
                              const SizedBox(height: 20),
                              // Text(
                              //   controller.totalBalance.toStringAsFixed(2) +
                              //       Get.find<LanguageController>()
                              //           .keys
                              //           .value
                              //           .ils!,
                              //   style: bigTextStyle,
                              // ),
                            ],
                          ),
                        ),
                        Text(
                          Get.find<LanguageController>()
                              .keys
                              .value
                              .paymentMethod!,
                          style: regularTextStyle,
                        ),
                        Text(
                          Get.find<LanguageController>().keys.value.weAccept!,
                          style: smallGreyTextStyle,
                        ),
                        Image.asset(
                          'assets/images/fawry.png',
                          width: 60.w,
                          height: 40.h,
                          fit: BoxFit.fill,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Icon(
                              FontAwesomeIcons.moneyBill,
                              size: 15.sp,
                              color: primaryColor,
                            ),
                            const SizedBox(width: 10),
                            Text(
                              Get.find<LanguageController>().keys.value.cash!,
                              style: smallTextStyle,
                            ),
                          ],
                        ),
                        TabBar(
                          onTap: controller.onTapBarChange,
                          tabs: [
                            Tab(
                              child: Text(
                                Get.find<LanguageController>()
                                    .keys
                                    .value
                                    .deposit!,
                                style: regularTextStyle,
                              ),
                            ),
                            Tab(
                              child: Text(
                                Get.find<LanguageController>().keys.value.done!,
                                style: regularTextStyle,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(
                          height: 230.h,
                          width: Get.width,
                          child: TabBarView(
                            children: [
                              Padding(
                                padding: EdgeInsets.only(bottom: 15.h),
                                child: SizedBox(
                                  width: Get.width,
                                  height: Get.height,
                                  child: ListView.builder(
                                    itemBuilder: (context, index) {
                                      return UserWalletItem(
                                        billing: controller.deposits[index],
                                        index: index,
                                      );
                                    },
                                    itemCount: controller.deposits.length,
                                  ),
                                ),
                              ),
                              Padding(
                                padding: EdgeInsets.only(bottom: 15.h),
                                child: SizedBox(
                                  width: Get.width,
                                  height: Get.height,
                                  child: ListView.builder(
                                    itemBuilder: (context, index) {
                                      return UserWalletItem(
                                        billing: controller.totalAmounts[index],
                                        index: index,
                                      );
                                    },
                                    itemCount: controller.totalAmounts.length,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    });
  }
}
