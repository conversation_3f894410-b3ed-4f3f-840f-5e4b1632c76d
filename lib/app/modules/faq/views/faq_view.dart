import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:get_clean/global/constants/constants.dart';

import '../../../../global/constants/theme.dart';
import '../../../../global/controllers/language_controller.dart';
import '../controllers/faq_controller.dart';

class FaqView extends GetView<FaqController> {
  const FaqView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: Get.width,
        height: Get.height,
        padding: const EdgeInsets.all(10),
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              'assets/images/main_background.png',
            ),
            fit: BoxFit.fill,
          ),
        ),
        child: Safe<PERSON>rea(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  IconButton(
                    onPressed: Get.back,
                    icon: const Icon(
                      CupertinoIcons.back,
                      size: 30,
                    ),
                  ),
                  Expanded(
                    child: Text(
                      Get.find<LanguageController>().keys.value.faq!,
                      style: bigTextStyle,
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              Expanded(
                child: ListView.builder(
                  itemBuilder: (context, index) {
                    return Column(
                      children: [
                        ExpansionTile(
                          expandedAlignment: Alignment.centerLeft,
                          iconColor: primaryColor,
                          collapsedIconColor: primaryColor,
                          title: Text(
                            controller.globalController.faq.value.data![index]
                                .question!,
                            style: big2TextStyle,
                          ),
                          children: <Widget>[
                            Text(
                              controller.globalController.faq.value.data![index]
                                  .answer!,
                              style: regularTextStyle,
                            ),
                          ],
                        ),
                        const Divider(),
                      ],
                    );
                  },
                  itemCount: controller.globalController.faq.value.data!.length,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
