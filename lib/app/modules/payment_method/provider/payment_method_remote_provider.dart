import 'dart:developer';

import 'package:get_clean/app/modules/payment_method/controllers/states/payment_method_states.dart';
import 'package:get_clean/global/help_functions/help_functions.dart';

import '../../../../global/constants/constants.dart';
import '../../../../global/dio/dio_helper.dart';

class PaymentMethodRemoteProvider {
  final helper = DioHelper();

  Future<PaymentMethodStates> approveOrderAsUser(int orderID) async {
    try {
      final response = await helper.postData(
        confirmBookingURL + orderID.toString(),
        {"": null},
      );
      log(response['success'].toString());
      if (response['success'] == true) {
        showSuccessToast(response['message'] ?? '');
        return PaymentMethodSuccess(
          response['data']['payment_link'],
        );
      } else {
        showErrorToast(response['message']);
        return PaymentMethodError();
      }
    } catch (e) {
      log(e.toString());
      return PaymentMethodError();
    }
  }

  Future<PaymentMethodStates> payRestOfTotalPrice(int orderID) async {
    try {
      final response = await helper.postData(
        payRestOfTotalPriceURL + orderID.toString(),
        {"": null},
      );
      log(response['success'].toString());
      if (response['success'] == true) {
        showSuccessToast(response['message'] ?? '');
        return PaymentMethodSuccess(
          response['data']['payment_link'],
        );
      } else {
        showErrorToast(response['message']);
        return PaymentMethodError();
      }
    } catch (e) {
      log(e.toString());
      return PaymentMethodError();
    }
  }
}
