import 'package:flutter/material.dart';
// import 'package:flutter_credit_card/credit_card_model.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/payment_method/controllers/states/payment_method_states.dart';
import 'package:get_clean/app/modules/payment_method/provider/payment_method_remote_provider.dart';
import 'package:get_clean/app/routes/app_pages.dart';
import 'package:get_clean/global/constants/theme.dart';
import 'package:get_clean/global/widget/custom_button.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../global/controllers/language_controller.dart';
import '../../../../global/models/user_booking.dart';

class PaymentMethodController extends GetxController {
  final formKey = GlobalKey<FormState>();
  final cardNumber = ''.obs;
  final cvvCode = ''.obs;
  final cardHolderName = ''.obs;
  final expiryDate = ''.obs;

  final BookingData order = Get.arguments['order'];
  final bool payDeposit = Get.arguments['payDeposit'];

  final state = PaymentMethodStates().obs;

  final provider = PaymentMethodRemoteProvider();

  final border = OutlineInputBorder(
    borderRadius: BorderRadius.circular(15),
    borderSide: BorderSide(
      color: Colors.grey.withOpacity(0.7),
      width: 2.0,
    ),
  );

  // void onCreditCardModelChange(CreditCardModel? creditCardModel) {
  //   cardNumber.value = creditCardModel!.cardNumber;
  //   expiryDate.value = creditCardModel.expiryDate;
  //   cardHolderName.value = creditCardModel.cardHolderName;
  //   cvvCode.value = creditCardModel.cvvCode;
  //
  //   update();
  // }

  void showCancelDialog() {
    Get.defaultDialog(
      confirm: CustomButton(
        label: Get.find<LanguageController>().keys.value.yes!,
        onTap: showCancelBottomSheet,
        height: 50,
        width: Get.width * 0.2,
      ),
      cancel: CustomButton(
        label: Get.find<LanguageController>().keys.value.no!,
        onTap: () => Get.back(),
        height: 50,
        width: Get.width * 0.2,
      ),
      middleText:
          Get.find<LanguageController>().keys.value.cancelRequestMessage!,
      title: '',
      middleTextStyle: big2TextStyle,
    );
  }

  void showCancelBottomSheet() {
    Get.back();
    Get.bottomSheet(
      Container(
        decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(45),
              topRight: Radius.circular(45),
            )),
        height: Get.height * 0.35,
        padding: const EdgeInsets.all(15),
        child: Column(
          children: [
            Expanded(
              child: Column(
                children: [
                  Image.asset(
                    'assets/images/wallet.png',
                    width: 100,
                    height: 100,
                    fit: BoxFit.fill,
                  ),
                  Text(
                    Get.find<LanguageController>()
                        .keys
                        .value
                        .ifYouCancelYouLoseMoney!,
                    style: big2TextStyle,
                  ),
                ],
              ),
            ),
            Row(
              children: [
                CustomButton(
                  label: Get.find<LanguageController>().keys.value.confirm!,
                  onTap: () => Get.toNamed(Routes.ORDER_CANCELED),
                  height: 50,
                  width: Get.width * 0.4,
                ),
                CustomButton(
                  label: Get.find<LanguageController>().keys.value.cancel!,
                  onTap: () => Get.back(),
                  height: 50,
                  width: Get.width * 0.4,
                  color: Colors.red,
                ),
              ],
            )
          ],
        ),
      ),
    );
  }

  void confirmPayment() async {
    state.value = PaymentMethodLoading();
    update();
    if (payDeposit) {
      state.value = await provider.approveOrderAsUser(order.id!.toInt());
    } else {
      state.value = await provider.payRestOfTotalPrice(order.id!.toInt());
    }

    update();
    await launchUrl(Uri.parse(state.value.paymentLink!));
    Get.toNamed(
      Routes.PAYMENT_COMPELETED,
      arguments: {
        'order': order,
        'payDeposit': payDeposit,
      },
    );
  }
}
