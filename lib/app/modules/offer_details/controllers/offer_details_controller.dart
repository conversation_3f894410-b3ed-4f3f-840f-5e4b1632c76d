import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/offer_details/provider/remote_provider.dart';
import 'package:get_clean/global/controllers/language_controller.dart';
import 'package:get_clean/global/global_states/offer_appointments_state.dart';
import 'package:get_clean/global/help_functions/help_functions.dart';
import 'package:get_clean/global/models/home_model.dart';
import 'package:get_clean/global/models/offer_appointments_model.dart';

import '../views/widgets/book_offer_bottom_sheet.dart';

class OfferDetailsController extends GetxController {
  final Offers offer = Get.arguments['offer'];
  final languageController = Get.find<LanguageController>();
  final provider = OfferDetailsRemoteProvider();
  final state = OfferAppointmentState().obs;
  final model = OfferAppointmentsModel().obs;
  final choosedDate = ''.obs;
  final choosedTime = ''.obs;
  TextEditingController address = TextEditingController();
  final Rx<Appointments> choosedAppointment = Appointments().obs;

  @override
  void onInit() {
    super.onInit();
    getOfferAppointments();
  }

  void getOfferAppointments() async {
    state.value = await provider.getOfferAppointments(offer.id!);
    if (state.value is OfferAppointmentSuccessState) {
      model.value = state.value.offerAppointments!;
    }
    update();
  }

  void onBookPressed() {
    Get.bottomSheet(
      BookOfferBottomSheet(
        appointment: model.value,
      ),
      isScrollControlled: true,
    );
  }

  void changeChoosedDate(String date, Appointments appointment) {
    choosedDate.value = date;
    choosedAppointment.value = appointment;
    update();
  }

  void onChangeChoosedTime(String time) {
    choosedTime.value = time;
    update();
  }

  void onBookOffer() async {
    showWaitingIndicator();
    final response = await provider.submitOffer(
      offer.id!,
      address.text,
      choosedDate.value,
      choosedTime.value,
    );
    hideWaitingIndicator();

    if (response) {
      Get.back();
    }
  }
}
