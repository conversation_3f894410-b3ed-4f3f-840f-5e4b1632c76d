import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/dio/dio_helper.dart';
import 'package:get_clean/global/global_states/offer_appointments_state.dart';
import 'package:get_clean/global/help_functions/help_functions.dart';
import 'package:get_clean/global/models/offer_appointments_model.dart';

class OfferDetailsRemoteProvider {
  DioHelper helper = DioHelper();

  Future<OfferAppointmentState> getOfferAppointments(int offerID) async {
    try {
      final response =
          await helper.getData(getOfferAppointmentsURL + offerID.toString());
      if (response['success'] == true) {
        return OfferAppointmentSuccessState(
          OfferAppointmentsModel.fromJson(response),
        );
      } else {
        showErrorToast(response['message']);
        return OfferAppointmentErrorState(response['message']);
      }
    } catch (e) {
      return OfferAppointmentErrorState(e.toString());
    }
  }

  Future<bool> submitOffer(
    int offerID,
    String address,
    String choosedDate,
    String choosedTime,
  ) async {
    try {
      final response = await helper.postData(
        bookOfferAppointmentURL + offerID.toString(),
        {
          "address": address,
          "date": choosedDate,
          "time": choosedTime,
        },
      );
      if (response['success'] == true) {
        showSuccessToast(response['message']);
        return true;
      } else {
        showErrorToast(response['message']);
        return false;
      }
    } catch (e) {
      return false;
    }
  }
}
