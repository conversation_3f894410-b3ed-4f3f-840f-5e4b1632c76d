import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/offer_details/controllers/offer_details_controller.dart';
import 'package:get_clean/global/constants/theme.dart';
import 'package:get_clean/global/models/offer_appointments_model.dart';
import 'package:get_clean/global/widget/custom_button.dart';
import 'package:get_clean/global/widget/custom_form_field.dart';

import '../../../../../global/constants/constants.dart';
import '../../../../../global/controllers/language_controller.dart';

class BookOfferBottomSheet extends StatelessWidget {
  final OfferAppointmentsModel appointment;
  const BookOfferBottomSheet({
    Key? key,
    required this.appointment,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    log(jsonEncode(appointment.data!.appointments.toString()));
    return GetBuilder<OfferDetailsController>(
      builder: (controller) {
        return Container(
          height: 754.h,
          width: Get.width,
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(60.sp),
              topRight: Radius.circular(60.sp),
            ),
          ),
          child: SingleChildScrollView(
            child: Column(
              children: [
                Container(
                  decoration: BoxDecoration(
                    color: primaryColor,
                    borderRadius: BorderRadius.circular(25),
                  ),
                  margin: const EdgeInsets.all(10),
                  height: 2.h,
                  width: 111.w,
                ),
                CustomFormField(
                  keyboardType: TextInputType.text,
                  controller: controller.address,
                  label: Get.find<LanguageController>().keys.value.address!,
                  hint: Get.find<LanguageController>().keys.value.address!,
                ),
                Padding(
                  padding: const EdgeInsets.all(10.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              Get.find<LanguageController>().keys.value.area!,
                              style: big2TextStyle,
                            ),
                            Text(
                              appointment.data!.offer!.address!.area!.name!,
                              style: regularBlackTextStyle,
                            ),
                          ],
                        ),
                      ),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              Get.find<LanguageController>().keys.value.city!,
                              style: big2TextStyle,
                            ),
                            Text(
                              appointment.data!.offer!.address!.name!,
                              style: regularBlackTextStyle,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(
                  height: 100.h,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemBuilder: (context, index) {
                      return InkWell(
                        onTap: () => controller.changeChoosedDate(
                          appointment.data!.appointments![index].date!,
                          appointment.data!.appointments![index],
                        ),
                        child: Center(
                          child: Container(
                            height: 86.h,
                            width: 86.w,
                            margin: const EdgeInsets.symmetric(horizontal: 5),
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(9.sp),
                              border: Border.all(color: primaryColor),
                              color: controller.choosedDate.value ==
                                      appointment
                                          .data!.appointments![index].date!
                                  ? primaryColor
                                  : Colors.white,
                            ),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  appointment.data!.appointments![index]
                                      .dateDetails!.dayNumber!,
                                  style: controller.choosedDate.value ==
                                          appointment
                                              .data!.appointments![index].date!
                                      ? regularWhiteTextStyle
                                      : regularTextStyle,
                                ),
                                Text(
                                  appointment.data!.appointments![index]
                                      .dateDetails!.dayName!,
                                  style: controller.choosedDate.value ==
                                          appointment
                                              .data!.appointments![index].date!
                                      ? regularWhiteTextStyle
                                      : regularTextStyle,
                                ),
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                    itemCount: appointment.data!.appointments!.length,
                  ),
                ),
                SizedBox(height: 10.h),
                Text(
                  controller.choosedDate.value,
                  style: big2TextStyle,
                ),
                SizedBox(height: 10.h),
                if (controller.choosedAppointment.value.date != null)
                  Container(
                    alignment: controller.languageController.isArabic
                        ? Alignment.centerRight
                        : Alignment.centerLeft,
                    child: Text(
                      Get.find<LanguageController>()
                          .keys
                          .value
                          .providerAvilableTimes!,
                      style: bigTextStyle,
                      textAlign: TextAlign.start,
                    ),
                  ),
                SizedBox(height: 10.h),
                if (controller.choosedAppointment.value.date != null)
                  SizedBox(
                    height: 100.h,
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemBuilder: (context, index) {
                        return InkWell(
                          onTap: () => controller.onChangeChoosedTime(
                            controller
                                .choosedAppointment.value.bookingTimes![index],
                          ),
                          child: Center(
                            child: Container(
                              height: 86.h,
                              width: 86.w,
                              margin: const EdgeInsets.symmetric(horizontal: 5),
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(9.sp),
                                border: Border.all(color: primaryColor),
                                color: controller.choosedTime.value ==
                                        controller.choosedAppointment.value
                                            .bookingTimes![index]
                                    ? primaryColor
                                    : Colors.white,
                              ),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    controller.choosedAppointment.value
                                        .bookingTimes![index],
                                    style: controller.choosedTime.value ==
                                            controller.choosedAppointment.value
                                                .bookingTimes![index]
                                        ? regularWhiteTextStyle
                                        : regularTextStyle,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                      itemCount: controller
                          .choosedAppointment.value.bookingTimes!.length,
                    ),
                  ),
                SizedBox(height: 10.h),
                CustomButton(
                  label: Get.find<LanguageController>().keys.value.done!,
                  onTap: controller.onBookOffer,
                  height: 43.h,
                  width: 129.w,
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
