import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/widget/custom_button.dart';
import '../../../../global/constants/theme.dart';
import '../../../../global/controllers/language_controller.dart';
import '../../../../global/widget/custom_rating_bar.dart';
import '../controllers/offer_details_controller.dart';

class OfferDetailsView extends GetView<OfferDetailsController> {
  const OfferDetailsView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(Get.find<LanguageController>().keys.value.offerDetails!),
        centerTitle: true,
      ),
      body: Container(
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              'assets/images/main_background_bottom.png',
            ),
            fit: BoxFit.fill,
          ),
        ),
        alignment: Alignment.center,
        child: Stack(
          children: [
            Positioned(
              top: 10.sp,
              left: 10.sp,
              right: 10.sp,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(10.sp),
                child: Image.network(
                  controller.offer.image!,
                  fit: BoxFit.fill,
                  height: Get.height * 0.4,
                ),
              ),
            ),
            Positioned(
              top: 300.sp,
              left: 10.sp,
              right: 10.sp,
              bottom: 0,
              child: Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(25),
                  color: Colors.white,
                  image: const DecorationImage(
                    image: AssetImage(
                      'assets/images/main_background_bottom.png',
                    ),
                    fit: BoxFit.fill,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey[400]!,
                      blurRadius: 2,
                      spreadRadius: 1,
                    ),
                  ],
                ),
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Text(
                              controller.offer.name!,
                              style: bigTextStyle,
                              overflow: TextOverflow.visible,
                            ),
                          ),
                          CustomButton(
                            label:
                                '${Get.find<LanguageController>().keys.value.onlyFor!} ${controller.offer.price!} ${Get.find<LanguageController>().keys.value.ils!}',
                            onTap: () {},
                            height: 25.h,
                            width: 100.w,
                            borderRadius: 5.0,
                            fontSize: 12,
                          ),
                        ],
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Text(
                              '${controller.offer.address!.name!},${controller.offer.address!.area!.name!}',
                              style: regularTextStyle,
                              overflow: TextOverflow.visible,
                            ),
                          ),
                          CustomButton(
                            label:
                                '${Get.find<LanguageController>().keys.value.from!} ${controller.offer.startDate!} ${Get.find<LanguageController>().keys.value.to!} ${controller.offer.endDate!}',
                            onTap: () {},
                            height: 25.h,
                            width: 170.w,
                            borderRadius: 5.0,
                            fontSize: 12,
                          ),
                        ],
                      ),
                      Row(
                        children: [
                          CircleAvatar(
                            radius: 10.sp,
                            backgroundImage:
                                NetworkImage(controller.offer.provider!.image!),
                          ),
                          SizedBox(width: 5.w),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                controller.offer.provider!.name!,
                                style: smallTextStyle,
                              ),
                              CustomRatingBar(
                                iconSize: 10.0.sp,
                                padding: 1.0.sp,
                                canChangeRate: false,
                                initialRating: controller
                                    .offer.provider!.rating!
                                    .toDouble(),
                              ),
                            ],
                          ),
                        ],
                      ),
                      const Divider(
                        color: primaryColor,
                      ),
                      Text(
                        controller.offer.description!,
                        style: regularTextStyle,
                      ),
                      SizedBox(height: 20.h),
                      Center(
                        child: CustomButton(
                          label: Get.find<LanguageController>()
                              .keys
                              .value
                              .bookNow!,
                          onTap: controller.onBookPressed,
                          height: 50.h,
                          width: 150.w,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
