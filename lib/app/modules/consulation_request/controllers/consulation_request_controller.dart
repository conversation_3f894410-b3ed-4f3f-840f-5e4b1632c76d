import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/consulation_request/provider/remote_provider.dart';
import 'package:get_clean/global/constants/theme.dart';
import 'package:get_clean/global/help_functions/help_functions.dart';

import '../../../../global/controllers/language_controller.dart';
import '../../../routes/app_pages.dart';

class ConsulationRequestController extends GetxController {
  final provider = ConsulationRequestRemoteProvider();

  void onFinacialConsultigPressed() async {
    showWaitingIndicator();
    final response = await provider.sendFinacialConsultigRequest();
    hideWaitingIndicator();
    if (response) {
      gotoHome();
    }
  }

  void onLegalAdvicePressed() async {
    showWaitingIndicator();
    final response = await provider.sendLegalAdviceRequest();
    hideWaitingIndicator();
    if (response) {
      gotoHome();
    }
  }

  void gotoHome() {
    Timer(const Duration(seconds: 3), () {
      Get.offAllNamed(Routes.HOME);
    });

    Get.dialog(
      Scaffold(
        backgroundColor: Colors.transparent,
        body: Center(
          child: Container(
            width: Get.width * 0.8,
            height: Get.height * 0.4,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(25),
              color: Colors.white,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(
                  'assets/images/check.png',
                ),
                const SizedBox(height: 10),
                Text(
                  Get.find<LanguageController>()
                      .keys
                      .value
                      .requestSentSuccessfully!,
                  style: big2TextStyle,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
