import 'dart:developer';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/dio/dio_helper.dart';
import 'package:get_clean/global/help_functions/help_functions.dart';

class ConsulationRequestRemoteProvider {
  DioHelper helper = DioHelper();

  Future<bool> sendFinacialConsultigRequest() async {
    try {
      final response = await helper.postData(
        sendFinacialConsultigRequestURL,
        {'': null},
      );
      if (response['success'] == true) {
        return true;
      } else {
        showErrorToast(response['message']);
        return false;
      }
    } catch (e) {
      log(e.toString());
      return false;
    }
  }

  Future<bool> sendLegalAdviceRequest() async {
    try {
      final response = await helper.postData(
        sendLegalAdviceRequestURL,
        {'': null},
      );
      if (response['success'] == true) {
        return true;
      } else {
        showErrorToast(response['message']);
        return false;
      }
    } catch (e) {
      log(e.toString());
      return false;
    }
  }
}
