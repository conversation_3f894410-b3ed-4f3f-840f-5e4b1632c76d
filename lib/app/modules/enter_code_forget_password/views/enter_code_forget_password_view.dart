import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/enter_code_forget_password/controllers/state/enter_code_forget_password_state.dart';
import 'package:get_clean/global/widget/loading_widget.dart';
import 'package:pin_code_fields/pin_code_fields.dart';

import '../../../../global/constants/constants.dart';
import '../../../../global/constants/theme.dart';
import '../../../../global/controllers/language_controller.dart';
import '../../../../global/widget/custom_button.dart';
import '../controllers/enter_code_forget_password_controller.dart';

class EnterCodeForgetPasswordView
    extends GetView<EnterCodeForgetPasswordController> {
  const EnterCodeForgetPasswordView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return GetBuilder<EnterCodeForgetPasswordController>(
      builder: (controller) {
        return Form(
          key: controller.key,
          child: Scaffold(
            body: Container(
              width: Get.width,
              height: Get.height,
              padding: const EdgeInsets.all(10),
              decoration: const BoxDecoration(
                image: DecorationImage(
                  image: AssetImage(
                    'assets/images/main_background.png',
                  ),
                  fit: BoxFit.fill,
                ),
              ),
              child: SafeArea(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.asset(
                      'assets/images/spam.png',
                    ),
                    Text(
                      Get.find<LanguageController>()
                          .keys
                          .value
                          .pleaseEnterVerificationCode!,
                      style: big2TextStyle,
                      textAlign: TextAlign.center,
                    ),
                    Directionality(
                      textDirection: TextDirection.ltr,
                      child: Padding(
                        padding: const EdgeInsets.all(10.0),
                        child: SizedBox(
                          width: Get.width * 0.6,
                          child: PinCodeTextField(
                            validator: (value) {
                              if (value!.isEmpty) {
                                return Get.find<LanguageController>()
                                    .keys
                                    .value
                                    .pleaseInsertCode!;
                              }
                              if (value.length < 4) {
                                return Get.find<LanguageController>()
                                    .keys
                                    .value
                                    .pleaseInsertCorrectCode!;
                              }

                              return null;
                            },
                            controller: controller.codeController,
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            appContext: context,
                            length: 4,
                            onChanged: (value) {},
                            enablePinAutofill: true,
                            pinTheme: PinTheme(
                              shape: PinCodeFieldShape.box,
                              inactiveColor: primaryColor,
                              activeColor: primaryColor,
                              inactiveFillColor: primaryColor,
                              activeFillColor: primaryColor,
                              disabledColor: primaryColor,
                              selectedFillColor: primaryColor,
                              selectedColor: primaryColor,
                              borderRadius: BorderRadius.circular(10),
                              fieldWidth: 50.w,
                              fieldHeight: 50.h,
                            ),
                          ),
                        ),
                      ),
                    ),
                    // Text(
                    //   Get.find<LanguageController>().keys.value.didntreceiverotp!,
                    // ),
                    // TextButton(
                    //   onPressed: controller.resendCode,
                    //   child: Text(Get.find<LanguageController>().keys.value.resendCode!),
                    // ),
                    controller.state.value
                            is EnterCodeForgetPasswordLoadingState
                        ? const LoadingWidget()
                        : CustomButton(
                            label:
                                Get.find<LanguageController>().keys.value.next!,
                            onTap: controller.onSubmitCode,
                            height: 43.h,
                            width: 130.w,
                          ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
