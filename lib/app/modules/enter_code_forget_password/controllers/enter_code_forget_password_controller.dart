import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/enter_code_forget_password/controllers/state/enter_code_forget_password_state.dart';
import 'package:get_storage/get_storage.dart';

import '../../../../global/constants/constants.dart';
import '../../../routes/app_pages.dart';
import '../remote_provider/enter_code_forget_password_remote_provider.dart';

class EnterCodeForgetPasswordController extends GetxController {
  TextEditingController codeController = TextEditingController();

  final state = EnterCodeForgetPasswordState().obs;

  EnterCodeForgetPasswordRemoteProvider provider =
      EnterCodeForgetPasswordRemoteProvider();

  GlobalKey<FormState> key = GlobalKey<FormState>();

  void onSubmitCode() async {
    if (key.currentState!.validate()) {
      state.value = EnterCodeForgetPasswordLoadingState();
      update();

      state.value = await provider.sendCode(codeController.text);
      update();

      if (state.value is EnterCodeForgetPasswordSuccessState) {
        GetStorage().write(tokenKey, state.value.user!.accessToken);
        Get.toNamed(
          Routes.ENTER_NEW_PASSWORD_FORGET_PASSWORD,
        );
      }
    }
  }

  void resendCode() {}

  @override
  void dispose() {
    codeController.dispose();
    super.dispose();
  }
}
