import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/my_profile/controllers/my_profile_controller.dart';
import 'package:get_clean/app/modules/provider_page_filtered/views/widgets/map_location_picker.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/controllers/language_controller.dart';
import 'package:get_clean/global/widget/custom_button.dart';

import '../../../../../global/widget/custom_form_field.dart';

class WorkLocationForm extends StatelessWidget {
  final bool isEdit;

  const WorkLocationForm({
    super.key,
    this.isEdit = false,
  });

  @override
  Widget build(BuildContext context) {
    return GetBuilder<MyProfileController>(
      builder: (controller) {
        return Container(
          height: 300.h,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(60),
              topRight: Radius.circular(60),
            ),
          ),
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              Container(
                height: 2.h,
                width: 111.w,
                decoration: const BoxDecoration(
                  color: primaryColor,
                ),
              ),
              SizedBox(height: 20.h),
              CustomFormField(
                keyboardType: TextInputType.text,
                controller: controller.workLocationNameController,
                label: Get.find<LanguageController>().keys.value.locationName ??
                    'Location Name',
                hint: Get.find<LanguageController>()
                        .keys
                        .value
                        .enterLocationName ??
                    'Enter location name',
              ),
              SizedBox(height: 20.h),
              GestureDetector(
                onTap: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (BuildContext context) => MapLocationPicker(
                        selectedMarkers: controller.workLocationMarker,
                        onSave: (marker) {
                          controller.setWorkLocationMarker(marker);
                        },
                      ),
                    ),
                  );
                },
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      Get.find<LanguageController>()
                              .keys
                              .value
                              .setLocationInMap ??
                          'Set Location on Map',
                      style: const TextStyle(
                        color: bgColor,
                        decoration: TextDecoration.underline,
                        decorationColor: bgColor,
                      ),
                    ),
                    if (controller.workLocationMarker.isNotEmpty)
                      const Padding(
                        padding: EdgeInsets.symmetric(horizontal: 8.0),
                        child: Icon(Icons.check_circle, color: primaryColor),
                      ),
                  ],
                ),
              ),
              SizedBox(height: 30.h),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (isEdit)
                    CustomButton(
                      label: Get.find<LanguageController>().keys.value.cancel ??
                          'Cancel',
                      onTap: () {
                        controller.clearWorkLocationSelection();
                        Get.back();
                      },
                      height: 43.h,
                      width: 129.w,
                      color: Colors.grey,
                    ),
                  if (isEdit) SizedBox(width: 20.w),
                  CustomButton(
                    label: isEdit
                        ? (Get.find<LanguageController>().keys.value.edit ??
                            'Edit')
                        : (Get.find<LanguageController>().keys.value.add ??
                            'Add'),
                    onTap: () async {
                      final result = isEdit
                          ? await controller.editWorkLocation()
                          : await controller.addWorkLocation();
                      if (result) {
                        Get.back();
                      }
                    },
                    height: 43.h,
                    width: 129.w,
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }
}
