import 'dart:convert';
import 'dart:developer';

import 'package:get_clean/app/modules/enter_phone_number/controllers/enter_phone_number_controller.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/dio/dio_helper.dart';
import 'package:get_clean/global/help_functions/help_functions.dart';
import 'package:get_clean/global/models/forget_password_model.dart';

import '../controllers/states/phone_number_state.dart';

class EnterPhoneRemoteProvider {
  DioHelper helper = DioHelper();

  Future<PhoneNumberState> sendPhoneNumber(phone) async {
    try {
      log(phone);
      final response = await helper.postData(
        forgetPasswordSendPhoneURL,
        {"email_phone": '$mainPhoneCode$phone'},
      );
      log('phone:$mainPhoneCode$phone\nResponse ${response}');
      if (response['success'] == true) {
        log(jsonEncode(response));
        return PhoneNumberSuccessState(
          ForgetPaawordModel.fromJson(response),
        );
      } else {
        showErrorToast(response['message']);
        return PhoneNumberFailedState(response['message']);
      }
    } catch (e) {
      log(e.toString());
      return PhoneNumberFailedState(e.toString());
    }
  }
}
