// import 'package:get/get.dart';
// import 'package:get_clean/app/modules/my_booking/controllers/states/my_booking_state.dart';
// import 'package:get_clean/app/modules/my_booking/provider/remote_provider.dart';
// import 'package:get_clean/global/help_functions/help_functions.dart';
//
// import '../../../../global/models/user_booking.dart';
// import '../../home/<USER>/home_controller.dart';
//
// class MyBookingController extends GetxController {
//   final provider = MyBookingRemoteProvider();
//   final state = MyBookingState().obs;
//   final myBookings = UserBookings().obs;
//
//   final user = Get.find<HomeController>().user;
//
//   final choosedOrdersType = 0.obs;
//
//   @override
//   void onInit() {
//     super.onInit();
//     getMyBooking();
//   }
//
//   bool isUser(BookingData bookingData) {
//     return bookingData.user!.id! == user.value.id!;
//   }
//
//   List<BookingData> filterBookings() {
//     List<BookingData> data = [];
//
//     if (choosedOrdersType.value == 0) {
//       for (var element in myBookings.value.bookingData ?? []) {
//         if (element.isPending!) {
//           data.add(element);
//         }
//       }
//     } else if (choosedOrdersType.value == 1) {
//       for (var element in myBookings.value.bookingData ?? []) {
//         if (element.isApproved!) {
//           data.add(element);
//         }
//       }
//     } else if (choosedOrdersType.value == 2) {
//       for (var element in myBookings.value.bookingData ?? []) {
//         if (element.isConfirmed!) {
//           data.add(element);
//         }
//       }
//     } else {
//       for (var element in myBookings.value.bookingData ?? []) {
//         if (!element.isPending! &&
//             !element.isApproved! &&
//             !element.isConfirmed!) {
//           data.add(element);
//         }
//       }
//     }
//     return data;
//   }
//
//   Future<List<BookingData>> getMyBooking() async {
//     state.value = MyBookingLoadingState();
//     update();
//
//     // state.value = await provider.getMyBooking();
//     update();
//
//     if (state.value is MyBookingSuccessState) {
//       final s = state.value as MyBookingSuccessState;
//       myBookings.value = s.userBookings!;
//       update();
//
//       return s.userBookings?.bookingData ?? [];
//     } else {
//       final s = state.value as MyBookingErrorState;
//       showErrorToast(s.errorMessage!);
//
//       return [];
//     }
//   }
//
//   void changeChoosedOrderType(int type) {
//     choosedOrdersType.value = type;
//     update();
//   }
// }
