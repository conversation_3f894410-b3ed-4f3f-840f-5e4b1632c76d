// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:get/get.dart';
// import 'package:get_clean/app/modules/my_booking/controllers/states/my_booking_state.dart';
// import 'package:get_clean/global/widget/bottom_navigation_bar.dart';
// import 'package:get_clean/global/widget/loading_widget.dart';
//
// import '../../../../global/constants/constants.dart';
// import '../../../../global/controllers/language_controller.dart';
// import '../../../../global/widget/order_widget.dart';
// import '../controllers/my_booking_controller.dart';
//
// class MyBookingView extends GetView<MyBookingController> {
//   const MyBookingView({Key? key}) : super(key: key);
//
//   @override
//   Widget build(BuildContext context) {
//     return GetBuilder<MyBookingController>(builder: (controller) {
//       return Scaffold(
//         bottomNavigationBar: const BottomNavBarWidget(),
//         appBar: AppBar(
//           title: Text(
//             Get.find<LanguageController>().keys.value.myBooking!,
//           ),
//           centerTitle: true,
//           backgroundColor: Colors.white,
//           foregroundColor: primaryColor,
//           elevation: 0,
//         ),
//         body: Container(
//           padding: const EdgeInsets.all(10),
//           decoration: const BoxDecoration(
//             image: DecorationImage(
//               image: AssetImage(
//                 'assets/images/main_background_bottom.png',
//               ),
//               fit: BoxFit.fill,
//             ),
//           ),
//           child: Builder(builder: (context) {
//             if (controller.state.value is MyBookingLoadingState) {
//               return const LoadingWidget();
//             }
//             return Column(
//               children: [
//                 SizedBox(
//                   height: 50.h,
//                   child: SingleChildScrollView(
//                     scrollDirection: Axis.horizontal,
//                     child: Container(
//                       margin: const EdgeInsets.all(10),
//                       // width: 304.w,
//                       height: 35.h,
//                       decoration: BoxDecoration(
//                         borderRadius: BorderRadius.circular(5),
//                         color: Colors.white,
//                         border: Border.all(
//                           color: primaryColor,
//                         ),
//                       ),
//                       child: Row(
//                         mainAxisAlignment: MainAxisAlignment.center,
//                         children: [
//                           InkWell(
//                             onTap: () => controller.changeChoosedOrderType(0),
//                             child: Row(
//                               mainAxisAlignment: MainAxisAlignment.center,
//                               children: [
//                                 Container(
//                                   alignment: Alignment.center,
//                                   height: 35.h,
//                                   width: 100.w,
//                                   color: controller.choosedOrdersType.value == 0
//                                       ? primaryColor
//                                       : Colors.white,
//                                   child: Text(
//                                     Get.find<LanguageController>()
//                                         .keys
//                                         .value
//                                         .pending!,
//                                     style: TextStyle(
//                                       fontSize: 16.sp,
//                                       color:
//                                           controller.choosedOrdersType.value ==
//                                                   0
//                                               ? Colors.white
//                                               : primaryColor,
//                                     ),
//                                     textAlign: TextAlign.center,
//                                   ),
//                                 ),
//                                 Container(
//                                   width: 1.w,
//                                   height: 35.h,
//                                   color: primaryColor,
//                                 ),
//                               ],
//                             ),
//                           ),
//                           InkWell(
//                             onTap: () => controller.changeChoosedOrderType(1),
//                             child: Row(
//                               children: [
//                                 Container(
//                                   alignment: Alignment.center,
//                                   height: 35.h,
//                                   width: 100.w,
//                                   color: controller.choosedOrdersType.value == 1
//                                       ? primaryColor
//                                       : Colors.white,
//                                   child: Text(
//                                     Get.find<LanguageController>()
//                                         .keys
//                                         .value
//                                         .unPaidOrders!,
//                                     style: TextStyle(
//                                       fontSize: 16.sp,
//                                       color:
//                                           controller.choosedOrdersType.value ==
//                                                   1
//                                               ? Colors.white
//                                               : primaryColor,
//                                     ),
//                                     textAlign: TextAlign.center,
//                                   ),
//                                 ),
//                                 Container(
//                                   width: 1.w,
//                                   height: 35.h,
//                                   color: primaryColor,
//                                 ),
//                               ],
//                             ),
//                           ),
//                           InkWell(
//                             onTap: () => controller.changeChoosedOrderType(2),
//                             child: Row(
//                               children: [
//                                 Container(
//                                   alignment: Alignment.center,
//                                   height: 35.h,
//                                   width: 100.w,
//                                   color: controller.choosedOrdersType.value == 2
//                                       ? primaryColor
//                                       : Colors.white,
//                                   child: Text(
//                                     Get.find<LanguageController>()
//                                         .keys
//                                         .value
//                                         .accepted!,
//                                     style: TextStyle(
//                                       fontSize: 16.sp,
//                                       color:
//                                           controller.choosedOrdersType.value ==
//                                                   2
//                                               ? Colors.white
//                                               : primaryColor,
//                                     ),
//                                     textAlign: TextAlign.center,
//                                   ),
//                                 ),
//                                 Container(
//                                   width: 1.w,
//                                   height: 35.h,
//                                   color: primaryColor,
//                                 ),
//                               ],
//                             ),
//                           ),
//                           InkWell(
//                             onTap: () => controller.changeChoosedOrderType(3),
//                             child: Row(
//                               children: [
//                                 Container(
//                                   alignment: Alignment.center,
//                                   height: 35.h,
//                                   width: 100.w,
//                                   color: controller.choosedOrdersType.value == 3
//                                       ? primaryColor
//                                       : Colors.white,
//                                   child: Text(
//                                     Get.find<LanguageController>()
//                                         .keys
//                                         .value
//                                         .oldOrders!,
//                                     style: TextStyle(
//                                       fontSize: 16.sp,
//                                       color:
//                                           controller.choosedOrdersType.value ==
//                                                   3
//                                               ? Colors.white
//                                               : primaryColor,
//                                     ),
//                                     textAlign: TextAlign.center,
//                                   ),
//                                 ),
//                               ],
//                             ),
//                           ),
//                         ],
//                       ),
//                     ),
//                   ),
//                 ),
//                 Expanded(
//                   child: ListView.builder(
//                     itemBuilder: (context, index) {
//                       return OrderWidget(
//                         bookingData: controller.filterBookings()[index],
//                         isUser: controller
//                             .isUser(controller.filterBookings()[index]),
//                       );
//                     },
//                     itemCount: controller.filterBookings().length,
//                   ),
//                 ),
//               ],
//             );
//           }),
//         ),
//       );
//     });
//   }
// }
