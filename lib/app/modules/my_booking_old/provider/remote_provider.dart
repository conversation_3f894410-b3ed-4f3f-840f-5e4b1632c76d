// import 'dart:developer';
//
// import 'package:get_clean/app/modules/my_orders/controllers/states/my_orders_states.dart';
// import 'package:get_clean/global/constants/constants.dart';
// import 'package:get_clean/global/dio/dio_helper.dart';
// import 'package:get_clean/global/models/user_booking.dart';
//
// class MyBookingRemoteProvider {
//   DioHelper helper = DioHelper();
//
//   // Future<MyBookingState> getMyBooking() async {
//   //   try {
//   //     final response = await helper.getData(
//   //       getAllBookingsURL,
//   //     );
//   //     if (response['success'] == true) {
//   //       return MyBookingSuccessState(UserBookings.fromJson(response));
//   //     } else {
//   //       return MyBookingErrorState(response['message']);
//   //     }
//   //   } catch (e) {
//   //     log(e.toString());
//   //     return MyBookingErrorState(e.toString());
//   //   }
//   // }
//   //
//   Future<MyOrdersState> getMyBooking() async {
//     try {
//       final response = await helper.getData(
//         getAllBookingsURL,
//       );
//       if (response['success'] == true) {
//         log('BBBBBB ${response}');
//         return MyOrdersSuccessState(UserBookings.fromJson(response));
//         // MyBookingSuccessState(UserBookings.fromJson(response));
//       } else {
//         return MyOrdersErrorState(response['message']);
//         // MyBookingErrorState(response['message']);
//       }
//     } catch (e) {
//       log(e.toString());
//       return MyOrdersErrorState(e.toString());
//       // MyBookingErrorState(e.toString());
//     }
//   }
// }
