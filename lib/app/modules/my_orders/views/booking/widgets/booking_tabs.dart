import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/my_orders/controllers/my_booking_controller.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/controllers/language_controller.dart';

class BookingTabs extends StatelessWidget {
  final bool isBooking;

  const BookingTabs({super.key, this.isBooking = false});

  @override
  Widget build(BuildContext context) {
    return Wrap(
      crossAxisAlignment: WrapCrossAlignment.start,
      children: [
        _CustomInkWellOrder(
          orderType: 0,
          displayText: Get.find<LanguageController>().keys.value.pending!,
        ),
        _CustomInkWellOrder(
          orderType: 1,
          displayText: Get.find<LanguageController>().keys.value.unPaidOrders!,
        ),
        _CustomInkWellOrder(
          orderType: 2,
          displayText: Get.find<LanguageController>().keys.value.accepted!,
        ),
        _CustomInkWellOrder(
          orderType: 3,
          displayText: Get.find<LanguageController>().keys.value.waiting!,
        ),
        // _CustomInkWellOrder(
        //   orderType: 4,
        //   displayText:
        //       Get.find<LanguageController>().keys.value.received!,
        // ),

        _CustomInkWellOrder(
          orderType: 4,
          displayText:
              Get.find<LanguageController>().keys.value.waitingForApproval!,
        ),
        _CustomInkWellOrder(
          orderType: 5,
          displayText: Get.find<LanguageController>().keys.value.deliver!,
        ),
        _CustomInkWellOrder(
          orderType: 6,
          displayText: Get.find<LanguageController>().keys.value.oldOrders!,
          haveDivider: false,
        ),
      ],
    );
  }
}

class _CustomInkWellOrder extends GetView<MyBookingController> {
  final int orderType;
  final String displayText;
  final bool haveDivider;

  const _CustomInkWellOrder({
    super.key,
    required this.orderType,
    required this.displayText,
    this.haveDivider = true,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() => InkWell(
          onTap: () {
            controller.changeChoosedOrderType(orderType);
          },
          child: Container(
            height: 40.h,
            width: 110.w,
            padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 5.h),
            margin: EdgeInsets.symmetric(horizontal: 5.w, vertical: 5.h),
            decoration: BoxDecoration(
              color: controller.choosedOrdersType.value == orderType
                  ? primaryColor
                  : Colors.white,
              border: Border.all(
                color: primaryColor,
                width: 0.5.w,
              ),
              borderRadius: BorderRadius.circular(15),
            ),
            child: FittedBox(
              fit: BoxFit.scaleDown,
              child: Text(
                displayText,
                style: TextStyle(
                  fontSize: 16.sp,
                  color: controller.choosedOrdersType.value == orderType
                      ? Colors.white
                      : primaryColor,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ));
  }
}
