import 'dart:developer';

import 'package:get_clean/app/modules/home/<USER>/home_controller.dart';

import '../../../../global/constants/constants.dart';
import '../../../../global/dio/dio_helper.dart';
import '../../../../global/models/user_booking.dart';
import '../controllers/states/my_orders_states.dart';

class MyOrdersRemoteProvider {
  DioHelper helper = DioHelper();

  Future<MyOrdersState> getMyOrders({
    required String status,
  }) async {
    try {
      final response = await helper.getData(
        '$getProviderOrdersByStatusURL/$status',
      );

      if (response['success'] == true) {
        return MyOrdersSuccessState(UserBookings.fromJson(response));
      } else {
        return MyOrdersErrorState(response['message']);
      }
    } catch (e, s) {
      log('$e $s');
      return MyOrdersErrorState(e.toString());
    }
  }

  //getFutureByGroupId
  Future<MyOrdersState> getFutureByGroupId(String id) async {
    try {
      final url =
          isProvider() ? getFutureByGroupIdURL : getFutureByUserGroupIdURL;

      final response = await helper.getData(
        url + id,
      );

      if (response['success'] == true) {
        return MyOrdersSuccessState(UserBookings.fromJson(response));
      } else {
        return MyOrdersErrorState(response['message']);
      }
    } catch (e) {
      log(e.toString());
      return MyOrdersErrorState(e.toString());
    }
  }
}
