import 'dart:developer';

import 'package:get/get.dart';

import '../../../../global/help_functions/help_functions.dart';
import '../../../../global/models/user_booking.dart';
import '../../home/<USER>/home_controller.dart';
import '../provider/my_orders_remote_provider.dart';
import 'states/my_orders_states.dart';

class MyOrdersController extends GetxController {
  final provider = MyOrdersRemoteProvider();
  final state = MyOrdersState().obs;
  final myOrders = UserBookings().obs;

  final user = Get.find<HomeController>().user;

  final choosedOrdersType = 0.obs;

  // final isBooking = false.obs;

  // setIsBooking(bool value) {
  //   isBooking.value = value;
  //   update();
  // }

  @override
  void onInit() {
    super.onInit();

    log('FFFFF ${Get.arguments}');

    log('DDDDDD422222');

    if (Get.find<HomeController>().user.value.type! != 'user') {
      getMyOrders();
    }
  }

  bool isUser(BookingData bookingData) {
    return (bookingData.user?.id ?? 0) == user.value.id;
  }

  List<BookingData> filterBookings() {
    List<BookingData> data = [];
    data = myOrders.value.bookingData ?? <BookingData>[];

    return data;

    for (var element in myOrders.value.bookingData ?? <BookingData>[]) {
      if (choosedOrdersType.value == 0) {
        if (element.isPending!) {
          data.add(element);
        }
      } else if (choosedOrdersType.value == 1) {
        if (element.isApproved!) {
          data.add(element);
        }
      } else if (choosedOrdersType.value == 2) {
        if (element.isConfirmed!) {
          data.add(element);
        }
      } else if (choosedOrdersType.value == 3) {
        if (element.status == "waiting_dropdawn") {
          data.add(element);
        }
        // } else if (choosedOrdersType.value == 4) {
        //   if (element.status == "received") {
        //     data.add(element);
        //   }
      } else if (choosedOrdersType.value == 4) {
        if (element.status == "waiting_approval") {
          data.add(element);
        }
      } else if (choosedOrdersType.value == 5) {
        if (element.status == "delivered") {
          data.add(element);
        }
      } else if (choosedOrdersType.value == 6) {
        if (!element.isPending! &&
            !element.isApproved! &&
            !element.isConfirmed! &&
            !(element.status == "waiting_dropdawn") &&
            !(element.status == "received") &&
            !(element.status == "waiting_approval") &&
            !(element.status == "delivered")) {
          data.add(element);
        }
      }
    }

    return data;
  }

  Future<List<BookingData>> getMyOrders({
    String status = 'pending',
  }) async {
    state.value = MyOrdersLoadingState();
    update();

    state.value = await provider.getMyOrders(
      status: status,
    );
    update();

    if (state.value is MyOrdersSuccessState) {
      final s = state.value as MyOrdersSuccessState;
      myOrders.value = s.orders!;
      update();

      return s.orders?.bookingData ?? [];
    } else {
      final s = state.value as MyOrdersErrorState;
      showErrorToast(s.errorMessage!);

      return [];
    }
  }

  void changeChoosedOrderType(int type) {
    choosedOrdersType.value = type;

    getMyOrders(status: convertTypeIndex(type));

    update();
  }

  // convert typeIndex (0-> pending)
  String convertTypeIndex(int typeIndex) {
    switch (typeIndex) {
      case 0:
        return 'pending';
      case 1:
        return 'approved';
      case 2:
        return 'confirmed';
      case 3:
        return 'waiting_dropdawn';
      case 4:
        return 'waiting_approval';
      case 5:
        return 'delivered';
      case 6:
        return 'completed';
      default:
        return 'pending';
    }
  }

  // group state
  final groupState = MyOrdersState().obs;
  final groupOrders = <String, UserBookings>{}.obs;

  Future<List<BookingData>> getFutureByGroupId(String groupId) async {
    groupState.value = MyOrdersLoadingState();
    update();

    groupState.value = await provider.getFutureByGroupId(groupId);
    update();

    if (groupState.value is MyOrdersSuccessState) {
      final s = groupState.value as MyOrdersSuccessState;
      // groupOrders.value = s.orders!;
      groupOrders[groupId] = s.orders!;
      update();

      return s.orders?.bookingData ?? [];
    } else {
      final s = groupState.value as MyOrdersErrorState;
      showErrorToast(s.errorMessage!);

      return [];
    }
  }
}
