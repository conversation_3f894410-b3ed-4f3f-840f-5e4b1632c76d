import 'dart:developer';

import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/dio/dio_helper.dart';
import 'package:get_clean/global/help_functions/help_functions.dart';

import '../models/calendar_reports_model.dart';

class CalendarReportsRemoteProvider {
  final DioHelper helper = DioHelper();

  // Get financial report
  Future<CalendarReportsResponse?> getFinancialReport({
    required int month,
    required int year,
  }) async {
    try {
      final response = await helper
          .postData(financialReportURL, {'month': month, 'year': year});
      // .postData('$financialReportURL?month=$month&year=$year', {});

      log('Financial Report Response: $response');
      if (response['success'] == true) {
        return CalendarReportsResponse.fromJson(response);
      } else {
        showErrorToast(response['message'] ?? 'Failed to get financial report');
        return null;
      }
    } catch (e, s) {
      log('Error getting financial report: ${e.toString()} $s');
      showErrorToast('Error loading financial report');
      return null;
    }
  }
}
