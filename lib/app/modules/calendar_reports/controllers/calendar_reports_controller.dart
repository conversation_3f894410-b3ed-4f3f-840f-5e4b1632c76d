import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_clean/app/modules/home/<USER>/home_controller.dart';
import 'package:get_clean/global/enums/user_type.dart';

import '../../../../global/constants/theme.dart';
import '../../../../global/controllers/language_controller.dart';
import '../../../routes/app_pages.dart';
import '../../privacy_policy/views/privacy_policy_view.dart';
import '../models/calendar_reports_model.dart';
import '../provider/calendar_reports_remote_provider.dart';

class CalendarReportsController extends GetxController {
  final CalendarReportsRemoteProvider provider =
      CalendarReportsRemoteProvider();

  // Observable variables
  final isLoading = false.obs;
  final isPaymentLoading = false.obs;
  final selectedMonth = DateTime.now().month.obs;
  final selectedYear = DateTime.now().year.obs;
  final reportsData = CalendarReportsData().obs;
  final expandedTasks = <int>[].obs; // Track which grouped tasks are expanded

  // User info
  final homeController = Get.find<HomeController>();

  @override
  void onInit() {
    super.onInit();
    fetchFinancialReport();
  }

  // Get current user type
  UserType get userType {
    final type = homeController.user.value.type ?? 'user';
    return type.userType;
  }

  // Check if user is a regular user (not provider/company)
  bool get isUser => userType == UserType.user;

  // Check if user is provider or company
  bool get isProviderOrCompany =>
      userType == UserType.provider || userType == UserType.company;

  // Get financial closure status label
  bool get isClosedFinancialClosureStatus {
    if (!isProviderOrCompany) return false;

    final unpaidTasks = reportsData.value.summary?.unpaidTasks ?? 0;
    return unpaidTasks < 0;
  }

  // Check if payment button should be shown
  bool get shouldShowPaymentButton {
    if (!isUser) return false;

    final summary = reportsData.value.summary;
    if (summary == null) return false;

    return (summary.showpayment == true) && (summary.unpaidTasks ?? 0) > 0;
  }

  // Get months list for dropdown
  List<Map<String, dynamic>> get monthsList {
    return List.generate(12, (index) {
      final monthNumber = index + 1;
      final monthNames = [
        'January',
        'February',
        'March',
        'April',
        'May',
        'June',
        'July',
        'August',
        'September',
        'October',
        'November',
        'December'
      ];
      return {
        'value': monthNumber,
        'label': monthNames[index],
      };
    });
  }

  // Get years list for dropdown (current year and previous 5 years)
// Get years list with 2 future years, current year, and 3 previous years
//   List<Map<String, dynamic>> get yearsList {
//     final currentYear = DateTime.now().year;
//     return List.generate(6, (index) {
//       final year = currentYear + 2 - index; // +2 for two future years
//       return {
//         'value': year,
//         'label': year.toString(),
//       };
//     });
//   }

  // Get years list for dropdown (2 future years, current year, and 3 previous years)
  List<Map<String, dynamic>> get yearsList {
    final currentYear = DateTime.now().year;
    final availableYears = <int>[];

    // Always include current year
    availableYears.add(currentYear);

    // Include previous years that have been logged/used
    for (int year = currentYear - 1; year >= 2025; year--) {
      availableYears.add(year);
    }

    return availableYears
        .map((year) => {
              'value': year,
              'label': year.toString(),
            })
        .toList();
  }

  // Fetch financial report from API
  Future<void> fetchFinancialReport() async {
    isLoading.value = true;
    update();

    try {
      final response = await provider.getFinancialReport(
        month: selectedMonth.value,
        year: selectedYear.value,
      );

      if (response != null && response.data != null) {
        reportsData.value = response.data!;
        log('Financial Report: ${reportsData.value.tasks?.length} tasks');
      }
    } catch (e) {
      log('Error fetching financial report: ${e.toString()}');
    } finally {
      isLoading.value = false;
      update();
    }
  }

  // Update selected month
  void updateMonth(int month) {
    selectedMonth.value = month;
    update(); // Trigger UI update
    fetchFinancialReport();
  }

  // Update selected year
  void updateYear(int year) {
    selectedYear.value = year;
    update(); // Trigger UI update
    fetchFinancialReport();
  }

  // Toggle expanded state for grouped tasks
  void toggleTaskExpansion(int taskId) {
    if (expandedTasks.contains(taskId)) {
      expandedTasks.remove(taskId);
    } else {
      expandedTasks.add(taskId);
    }
    update();
  }

  // Check if task is expanded
  bool isTaskExpanded(int taskId) {
    return expandedTasks.contains(taskId);
  }

  // Make payment for unpaid tasks
  Future<void> makePayment() async {
    if (!shouldShowPaymentButton) return;

    isPaymentLoading.value = true;
    update();

    try {
      Get.dialog(AlertDialog(
        contentPadding: const EdgeInsets.all(0),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: Text(
          Get.find<LanguageController>().keys.value.privacyPolicy!,
          style: bigTextStyle,
        ),
        content: const PrivacyPolicyView(
          fromPayment: true,
        ),
        actions: [
          TextButton(
            onPressed: Get.back,
            child: Text(
              Get.find<LanguageController>().keys.value.close!,
              style: regularTextStyle,
            ),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              Get.toNamed(
                Routes.PAYMENT_WEBVIEW,
                arguments: {
                  'url': reportsData.value.summary?.paymentLink ?? '',
                  // 'order': order,
                  // 'tip': paidTips.value,
                  // 'payDeposit': payDeposit,
                },
              );
            },
            child: Text(
              Get.find<LanguageController>().keys.value.accept!,
              style: regularTextStyle,
            ),
          ),
        ],
      ));
      // fetchFinancialReport();
    } catch (e) {
      log('Error making payment: ${e.toString()}');
    } finally {
      isPaymentLoading.value = false;
      update();
    }
  }

  // Format currency
  String formatCurrency(dynamic amount) {
    if (amount == null) return '0';
    return amount.toString();
  }

  // Get status display name
  String getStatusDisplayNameAr(String? status) {
    if (status == null) return '';

    switch (status.toLowerCase()) {
      case 'ended':
        return 'منتهي';
      case 'approved':
        return 'موافق عليه';
      case 'pending':
        return 'في الانتظار';
      case 'cancelled':
        return 'ملغي';
      default:
        return status;
    }
  }

  //getStatusDisplayNameEn
  String getStatusDisplayNameEn(String? status) {
    if (status == null) return '';

    switch (status.toLowerCase()) {
      case 'ended':
        return 'Ended';
      case 'approved':
        return 'Approved';
      case 'pending':
        return 'Pending';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status;
    }
  }

  // Get payment status display name
  String getPaymentStatusDisplayNameAr(String? status) {
    if (status == null) return '';

    switch (status.toLowerCase()) {
      case 'paid':
        return 'مدفوع';
      case 'unpaid':
        return 'غير مدفوع';
      case 'pending':
        return 'في الانتظار';
      default:
        return status;
    }
  }

  //getPaymentStatusDisplayNameEn
  String getPaymentStatusDisplayNameEn(String? status) {
    if (status == null) return '';

    switch (status.toLowerCase()) {
      case 'paid':
        return 'Paid';
      case 'unpaid':
        return 'Unpaid';
      case 'pending':
        return 'Pending';
      default:
        return status;
    }
  }
}
