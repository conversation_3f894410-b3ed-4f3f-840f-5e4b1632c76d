import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/controllers/language_controller.dart';
import 'package:get_clean/global/widget/loading_widget.dart';

import '../controllers/calendar_reports_controller.dart';
import 'widgets/filter_section.dart';
import 'widgets/payment_button.dart';
import 'widgets/report_task_card.dart';
import 'widgets/summary_cards.dart';

class CalendarReportsView extends StatelessWidget {
  const CalendarReportsView({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<CalendarReportsController>(
      builder: (controller) {
        return Scaffold(
            appBar: AppBar(
              title: Text(
                Get.find<LanguageController>().keys.value.financialReport ??
                    'Financial Report',
                style: const TextStyle(color: Colors.white),
              ),
              backgroundColor: primaryColor,
              elevation: 0,
              centerTitle: true,
              iconTheme: const IconThemeData(color: Colors.white),
            ),
            body: Obx(
              () => controller.isLoading.value
                  ? const LoadingWidget()
                  : SafeArea(
                      child: Column(
                        children: [
                          // Filter Section
                          const FilterSection(),

                          if (controller.isProviderOrCompany) ...[
                            Container(
                              width: double.infinity,
                              margin: EdgeInsets.symmetric(
                                  horizontal: 16.w, vertical: 8.h),
                              padding: EdgeInsets.all(12.w),
                              decoration: BoxDecoration(
                                color: (controller.reportsData.value.summary
                                                ?.unpaidTasks ??
                                            0) ==
                                        0
                                    ? Colors.green.shade50
                                    : Colors.red.shade50,
                                borderRadius: BorderRadius.circular(8.r),
                                border: Border.all(
                                  color: (controller.reportsData.value.summary
                                                  ?.unpaidTasks ??
                                              0) ==
                                          0
                                      ? Colors.green.shade200
                                      : Colors.red.shade200,
                                ),
                              ),
                              child: Row(
                                children: [
                                  Icon(
                                    (controller.reportsData.value.summary
                                                    ?.unpaidTasks ??
                                                0) ==
                                            0
                                        ? Icons.check_circle
                                        : Icons.warning,
                                    color: (controller.reportsData.value.summary
                                                    ?.unpaidTasks ??
                                                0) ==
                                            0
                                        ? Colors.green
                                        : Colors.red,
                                    size: 20.sp,
                                  ),
                                  SizedBox(width: 8.w),
                                  Text(
                                    controller.isClosedFinancialClosureStatus
                                        ? Get.find<LanguageController>()
                                            .keys
                                            .value
                                            .closedFinancially!
                                        : Get.find<LanguageController>()
                                            .keys
                                            .value
                                            .unClosedFinancially!,
                                    style: TextStyle(
                                      fontSize: 14.sp,
                                      fontWeight: FontWeight.w600,
                                      color: (controller.reportsData.value
                                                      .summary?.unpaidTasks ??
                                                  0) ==
                                              0
                                          ? Colors.green.shade700
                                          : Colors.red.shade700,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],

                          // Summary Cards

                          // Tasks List
                          Expanded(
                            child: ListView(
                              children: [
                                const SummaryCards(),
                                controller.reportsData.value.tasks?.isEmpty ??
                                        true
                                    ? Center(
                                        child: Text(
                                          Get.find<LanguageController>()
                                                  .keys
                                                  .value
                                                  .noTasksFound ??
                                              'No tasks found for this period',
                                          style: TextStyle(
                                            fontSize: 14.sp,
                                            color: Colors.grey,
                                          ),
                                        ),
                                      )
                                    : ListView.builder(
                                        shrinkWrap: true,
                                        physics:
                                            const NeverScrollableScrollPhysics(),
                                        padding: EdgeInsets.all(16.w),
                                        itemCount: controller.reportsData.value
                                                .tasks?.length ??
                                            0,
                                        itemBuilder: (context, index) {
                                          final task = controller
                                              .reportsData.value.tasks![index];
                                          return ReportTaskCard(
                                            task: task,
                                            onToggleExpansion: () =>
                                                controller.toggleTaskExpansion(
                                                    task.id ?? 0),
                                            isExpanded: controller
                                                .isTaskExpanded(task.id ?? 0),
                                          );
                                        },
                                      ),
                              ],
                            ),
                          ),

                          // Payment Button (for users only)
                          if (controller.shouldShowPaymentButton)
                            const PaymentButton(),
                        ],
                      ),
                    ),
            ));
      },
    );
  }
}
