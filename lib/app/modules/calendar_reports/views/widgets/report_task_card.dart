import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/controllers/language_controller.dart';

import '../../controllers/calendar_reports_controller.dart';
import '../../models/calendar_reports_model.dart';

class ReportTaskCard extends StatelessWidget {
  final ReportTask task;
  final VoidCallback onToggleExpansion;
  final bool isExpanded;

  const ReportTaskCard({
    super.key,
    required this.task,
    required this.onToggleExpansion,
    required this.isExpanded,
  });

  @override
  Widget build(BuildContext context) {
    return GetBuilder<CalendarReportsController>(
      builder: (controller) {
        return Container(
          margin: EdgeInsets.only(bottom: 12.h),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12.r),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                spreadRadius: 1,
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              // Main Task Info
              Padding(
                padding: EdgeInsets.all(16.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header Row
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            task.serviceName ?? '',
                            style: TextStyle(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.bold,
                              color: primaryColor,
                            ),
                          ),
                        ),
                        if (task.status?.isNotEmpty ?? false)
                          Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 8.w, vertical: 4.h),
                            decoration: BoxDecoration(
                              color:
                                  _getStatusColor(task.status).withOpacity(0.1),
                              borderRadius: BorderRadius.circular(12.r),
                            ),
                            child: Text(
                              Get.find<LanguageController>()
                                          .selectedLanguage
                                          .value
                                          .slug ==
                                      'en'
                                  ? controller
                                      .getStatusDisplayNameEn(task.status)
                                  : controller
                                      .getStatusDisplayNameAr(task.status),
                              style: TextStyle(
                                fontSize: 12.sp,
                                fontWeight: FontWeight.w500,
                                color: _getStatusColor(task.status),
                              ),
                            ),
                          ),
                        if (task.paymentStatus?.isNotEmpty ?? false)
                          Container(
                            margin: EdgeInsets.only(left: 8.w),
                            padding: EdgeInsets.symmetric(
                                horizontal: 8.w, vertical: 4.h),
                            decoration: BoxDecoration(
                              color: _getPaymentStatusColor(task.paymentStatus)
                                  .withOpacity(0.1),
                              borderRadius: BorderRadius.circular(12.r),
                            ),
                            child: Text(
                              Get.find<LanguageController>()
                                          .selectedLanguage
                                          .value
                                          .slug ==
                                      'en'
                                  ? controller.getPaymentStatusDisplayNameEn(
                                      task.paymentStatus)
                                  : controller.getPaymentStatusDisplayNameAr(
                                      task.paymentStatus),
                              style: TextStyle(
                                fontSize: 12.sp,
                                fontWeight: FontWeight.w500,
                                color:
                                    _getPaymentStatusColor(task.paymentStatus),
                              ),
                            ),
                          ),
                      ],
                    ),
                    SizedBox(height: 8.h),

                    // Location
                    _buildInfoRow(
                      icon: Icons.location_on,
                      label:
                          Get.find<LanguageController>().keys.value.location ??
                              'Location',
                      value: task.locationName ?? '',
                    ),

                    if (task.isGrouped == false) ...[
                      // Execution Date
                      _buildInfoRow(
                        icon: Icons.calendar_today,
                        label: Get.find<LanguageController>()
                                .keys
                                .value
                                .executionDate ??
                            'Date',
                        value: task.executionDate ?? '',
                      ),

                      // Time
                      _buildInfoRow(
                        icon: Icons.access_time,
                        label: Get.find<LanguageController>().keys.value.time ??
                            'Time',
                        value:
                            '${task.startTime ?? ''} - ${task.endTime ?? ''}',
                      ),
                    ],
                    // Hours (only show if not grouped or if grouped and has hours)
                    if (task.isGrouped != true || task.hours != null) ...[
                      _buildInfoRow(
                        icon: Icons.schedule,
                        label:
                            Get.find<LanguageController>().keys.value.hours ??
                                'Hours',
                        value: task.hours?.toString() ?? '0',
                      ),
                    ],

                    // Amount
                    _buildInfoRow(
                      icon: Icons.attach_money,
                      label: Get.find<LanguageController>().keys.value.amount ??
                          'Amount',
                      value: controller.formatCurrency(task.amount),
                    ),

                    // Show All Tasks Button for grouped tasks
                    if (task.isGrouped == true &&
                        task.schedules?.isNotEmpty == true) ...[
                      SizedBox(height: 8.h),
                      GestureDetector(
                        onTap: onToggleExpansion,
                        child: Container(
                          padding: EdgeInsets.symmetric(vertical: 8.h),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                isExpanded
                                    ? (Get.find<LanguageController>()
                                            .keys
                                            .value
                                            .hideAllTasks ??
                                        'Hide All Tasks')
                                    : (Get.find<LanguageController>()
                                            .keys
                                            .value
                                            .showAllTasks ??
                                        'Show All Tasks'),
                                style: TextStyle(
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.w500,
                                  color: primaryColor,
                                ),
                              ),
                              SizedBox(width: 4.w),
                              Icon(
                                isExpanded
                                    ? Icons.keyboard_arrow_up
                                    : Icons.keyboard_arrow_down,
                                color: primaryColor,
                                size: 20.sp,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),

              // Expanded Schedules (for grouped tasks)
              if (task.isGrouped == true &&
                  isExpanded &&
                  task.schedules?.isNotEmpty == true) ...[
                Container(
                  decoration: BoxDecoration(
                    color: Colors.grey[50],
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(12.r),
                      bottomRight: Radius.circular(12.r),
                    ),
                  ),
                  child: Column(
                    children: task.schedules!.map((schedule) {
                      return Container(
                        padding: EdgeInsets.all(16.w),
                        decoration: BoxDecoration(
                          border: Border(
                            top: BorderSide(color: Colors.grey[200]!),
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildInfoRow(
                              icon: Icons.calendar_today,
                              label: Get.find<LanguageController>()
                                      .keys
                                      .value
                                      .executionDate ??
                                  'Date',
                              value: schedule.executionDate ?? '',
                            ),
                            _buildInfoRow(
                              icon: Icons.access_time,
                              label: Get.find<LanguageController>()
                                      .keys
                                      .value
                                      .time ??
                                  'Time',
                              value:
                                  '${schedule.startTime ?? ''} - ${schedule.endTime ?? ''}',
                            ),
                            if ((schedule.durationMinutes ?? 0) > 0)
                              _buildInfoRow(
                                icon: Icons.schedule,
                                label: Get.find<LanguageController>()
                                        .keys
                                        .value
                                        .duration ??
                                    'Duration',
                                value:
                                    '${schedule.durationMinutes ?? 0} ${Get.find<LanguageController>().keys.value.minutes ?? 'minutes'}',
                              ),
                            Container(
                              margin: EdgeInsets.only(top: 8.h),
                              padding: EdgeInsets.symmetric(
                                  horizontal: 8.w, vertical: 4.h),
                              decoration: BoxDecoration(
                                color: _getStatusColor(schedule.status)
                                    .withOpacity(0.1),
                                borderRadius: BorderRadius.circular(12.r),
                              ),
                              child: Text(
                                Get.find<LanguageController>()
                                            .selectedLanguage
                                            .value
                                            .slug ==
                                        'en'
                                    ? controller
                                        .getStatusDisplayNameEn(schedule.status)
                                    : controller.getStatusDisplayNameAr(
                                        schedule.status),
                                style: TextStyle(
                                  fontSize: 12.sp,
                                  fontWeight: FontWeight.w500,
                                  color: _getStatusColor(schedule.status),
                                ),
                              ),
                            ),

                            // _buildInfoRow(
                            //   icon: Icons.info,
                            //   label: Get.find<LanguageController>()
                            //           .keys
                            //           .value
                            //           .status ??
                            //       'Status',
                            //   value: Get.find<LanguageController>()
                            //               .selectedLanguage
                            //               .value
                            //               .slug ==
                            //           'en'
                            //       ? controller
                            //           .getStatusDisplayNameEn(schedule.status)
                            //       : controller
                            //           .getStatusDisplayNameAr(schedule.status),
                            // ),
                          ],
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildInfoRow({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4.h),
      child: Row(
        children: [
          Icon(
            icon,
            size: 16.sp,
            color: Colors.grey[600],
          ),
          SizedBox(width: 8.w),
          Text(
            '$label: ',
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w500,
              color: Colors.grey[600],
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }

  //

  Color _getStatusColor(String? status) {
    if (status == null) return Colors.grey;

    switch (status.toLowerCase()) {
      case 'ended':
        return Colors.green;
      case 'started':
        return Colors.green;
      case 'approved':
        return Colors.blue;
      case 'pending':
        return Colors.orange;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String getPaymentStatusDisplayNameEn(String? status) {
    if (status == null) return '';

    switch (status.toLowerCase()) {
      case 'paid':
        return 'Paid';
      case 'unpaid':
        return 'Unpaid';
      case 'pending':
        return 'Pending';
      default:
        return status;
    }
  }

  //_getPaymentStatusColor
  Color _getPaymentStatusColor(String? status) {
    if (status == null) return Colors.grey;

    switch (status.toLowerCase()) {
      case 'paid':
        return Colors.green;
      case 'unpaid':
        return Colors.red;
      case 'pending':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }
}
