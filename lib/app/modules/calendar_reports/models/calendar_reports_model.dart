class CalendarReportsResponse {
  bool? success;
  int? code;
  CalendarReportsData? data;
  String? message;

  CalendarReportsResponse({
    this.success,
    this.code,
    this.data,
    this.message,
  });

  CalendarReportsResponse.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    code = json['code'];
    data = json['data'] != null
        ? CalendarReportsData.fromJson(json['data'])
        : null;
    message = json['message'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['success'] = success;
    data['code'] = code;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['message'] = message;
    return data;
  }
}

class CalendarReportsData {
  List<ReportTask>? tasks;
  ReportSummary? summary;

  CalendarReportsData({this.tasks, this.summary});

  CalendarReportsData.fromJson(Map<String, dynamic> json) {
    if (json['tasks'] != null) {
      tasks = <ReportTask>[];
      json['tasks'].forEach((v) {
        tasks!.add(ReportTask.fromJson(v));
      });
    }
    summary = json['summary'] != null
        ? ReportSummary.fromJson(json['summary'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (tasks != null) {
      data['tasks'] = tasks!.map((v) => v.toJson()).toList();
    }
    if (summary != null) {
      data['summary'] = summary!.toJson();
    }
    return data;
  }
}

class ReportTask {
  int? id;
  int? jobApplicationId;
  String? personName;
  String? serviceName;
  String? locationName;
  String? calculationType;
  String? providerPaymentMethod;
  String? rate;
  dynamic amount;
  dynamic transportationCost;
  dynamic totalAmount;
  int? isFinanciallyClosed;
  String? paymentDate;
  String? paymentStatus;
  List<TaskSchedule>? schedules;
  bool? isGrouped;
  TaskTypeInfo? taskTypeInfo;
  dynamic hoursInfo;
  String? calculationNote;
  String? executionDate;
  String? startTime;
  String? endTime;
  int? hours;
  String? status;
  String? notes;

  ReportTask({
    this.id,
    this.jobApplicationId,
    this.personName,
    this.serviceName,
    this.locationName,
    this.calculationType,
    this.providerPaymentMethod,
    this.rate,
    this.amount,
    this.transportationCost,
    this.totalAmount,
    this.isFinanciallyClosed,
    this.paymentDate,
    this.paymentStatus,
    this.schedules,
    this.isGrouped,
    this.taskTypeInfo,
    this.hoursInfo,
    this.calculationNote,
    this.executionDate,
    this.startTime,
    this.endTime,
    this.hours,
    this.status,
    this.notes,
  });

  ReportTask.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    jobApplicationId = json['job_application_id'];
    personName = json['person_name'];
    serviceName = json['service_name'];
    locationName = json['location_name'];
    calculationType = json['calculation_type'];
    providerPaymentMethod = json['provider_payment_method'];
    rate = json['rate']?.toString();
    amount = json['amount'];
    transportationCost = json['transportation_cost'];
    totalAmount = json['total_amount'];
    isFinanciallyClosed = json['is_financially_closed'];
    paymentDate = json['payment_date'];
    paymentStatus = json['payment_status'];
    if (json['schedules'] != null) {
      schedules = <TaskSchedule>[];
      json['schedules'].forEach((v) {
        schedules!.add(TaskSchedule.fromJson(v));
      });
    }
    isGrouped = json['is_grouped'];
    taskTypeInfo = json['task_type_info'] != null
        ? TaskTypeInfo.fromJson(json['task_type_info'])
        : null;
    hoursInfo = json['hours_info'];
    calculationNote = json['calculation_note'];
    executionDate = json['execution_date'];
    startTime = json['start_time'];
    endTime = json['end_time'];
    hours = json['hours'];
    status = json['status'];
    notes = json['notes'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['job_application_id'] = jobApplicationId;
    data['person_name'] = personName;
    data['service_name'] = serviceName;
    data['location_name'] = locationName;
    data['calculation_type'] = calculationType;
    data['provider_payment_method'] = providerPaymentMethod;
    data['rate'] = rate;
    data['amount'] = amount;
    data['transportation_cost'] = transportationCost;
    data['total_amount'] = totalAmount;
    data['is_financially_closed'] = isFinanciallyClosed;
    data['payment_date'] = paymentDate;
    data['payment_status'] = paymentStatus;
    if (schedules != null) {
      data['schedules'] = schedules!.map((v) => v.toJson()).toList();
    }
    data['is_grouped'] = isGrouped;
    if (taskTypeInfo != null) {
      data['task_type_info'] = taskTypeInfo!.toJson();
    }
    data['hours_info'] = hoursInfo;
    data['calculation_note'] = calculationNote;
    data['execution_date'] = executionDate;
    data['start_time'] = startTime;
    data['end_time'] = endTime;
    data['hours'] = hours;
    data['status'] = status;
    data['notes'] = notes;
    return data;
  }
}

class TaskSchedule {
  String? executionDate;
  String? startTime;
  String? endTime;
  int? durationMinutes;
  String? status;
  String? notes;
  String? customerTypeLabel;
  String? providerTypeLabel;

  TaskSchedule({
    this.executionDate,
    this.startTime,
    this.endTime,
    this.durationMinutes,
    this.status,
    this.notes,
    this.customerTypeLabel,
    this.providerTypeLabel,
  });

  TaskSchedule.fromJson(Map<String, dynamic> json) {
    executionDate = json['execution_date'];
    startTime = json['start_time'];
    endTime = json['end_time'];
    durationMinutes = json['duration_minutes'];
    status = json['status'];
    notes = json['notes'];
    customerTypeLabel = json['customer_type_label'];
    providerTypeLabel = json['provider_type_label'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['execution_date'] = executionDate;
    data['start_time'] = startTime;
    data['end_time'] = endTime;
    data['duration_minutes'] = durationMinutes;
    data['status'] = status;
    data['notes'] = notes;
    data['customer_type_label'] = customerTypeLabel;
    data['provider_type_label'] = providerTypeLabel;
    return data;
  }
}

class TaskTypeInfo {
  String? customerType;
  String? providerType;

  TaskTypeInfo({this.customerType, this.providerType});

  TaskTypeInfo.fromJson(Map<String, dynamic> json) {
    customerType = json['customer_type'];
    providerType = json['provider_type'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['customer_type'] = customerType;
    data['provider_type'] = providerType;
    return data;
  }
}

class ReportSummary {
  int? totalTasks;
  int? paidTasks;
  int? unpaidTasks;
  int? fixedAmountTasks;
  int? hourlyTasks;
  String? fixedAmountTasksLabel;
  String? hourlyTasksLabel;
  int? totalJobApplications;
  int? totalHours;
  dynamic totalAmount;
  dynamic totalTransportation;
  dynamic grandTotal;
  List<PreviousPayment>? previousPayments;
  int? totalPreviousPayments;
  dynamic totalPreviousAmount;
  String? month;
  String? year;
  String? userType;
  String? calculationNote;
  String? paymentLink;
  bool? showpayment;

  ReportSummary({
    this.totalTasks,
    this.paidTasks,
    this.unpaidTasks,
    this.fixedAmountTasks,
    this.hourlyTasks,
    this.fixedAmountTasksLabel,
    this.hourlyTasksLabel,
    this.totalJobApplications,
    this.totalHours,
    this.totalAmount,
    this.totalTransportation,
    this.grandTotal,
    this.previousPayments,
    this.totalPreviousPayments,
    this.totalPreviousAmount,
    this.month,
    this.year,
    this.userType,
    this.calculationNote,
    this.showpayment,
    this.paymentLink,
  });

  ReportSummary.fromJson(Map<String, dynamic> json) {
    totalTasks = json['total_tasks'];
    paidTasks = json['paid_tasks'];
    unpaidTasks = json['unpaid_tasks'];
    fixedAmountTasks = json['fixed_amount_tasks'];
    hourlyTasks = json['hourly_tasks'];
    fixedAmountTasksLabel = json['fixed_amount_tasks_label'];
    hourlyTasksLabel = json['hourly_tasks_label'];
    totalJobApplications = json['total_job_applications'];
    totalHours = json['total_hours'];
    totalAmount = json['total_amount'];
    totalTransportation = json['total_transportation'];
    grandTotal = json['grand_total'];
    if (json['previous_payments'] != null) {
      previousPayments = <PreviousPayment>[];
      json['previous_payments'].forEach((v) {
        previousPayments!.add(PreviousPayment.fromJson(v));
      });
    }
    totalPreviousPayments = json['total_previous_payments'];
    totalPreviousAmount = json['total_previous_amount'];
    month = json['month'];
    year = json['year'];
    userType = json['user_type'];
    calculationNote = json['calculation_note'];
    showpayment = json['showpayment'];
    paymentLink = json['payment_link'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['total_tasks'] = totalTasks;
    data['paid_tasks'] = paidTasks;
    data['unpaid_tasks'] = unpaidTasks;
    data['fixed_amount_tasks'] = fixedAmountTasks;
    data['hourly_tasks'] = hourlyTasks;
    data['fixed_amount_tasks_label'] = fixedAmountTasksLabel;
    data['hourly_tasks_label'] = hourlyTasksLabel;
    data['total_job_applications'] = totalJobApplications;
    data['total_hours'] = totalHours;
    data['total_amount'] = totalAmount;
    data['total_transportation'] = totalTransportation;
    data['grand_total'] = grandTotal;
    if (previousPayments != null) {
      data['previous_payments'] =
          previousPayments!.map((v) => v.toJson()).toList();
    }
    data['total_previous_payments'] = totalPreviousPayments;
    data['total_previous_amount'] = totalPreviousAmount;
    data['month'] = month;
    data['year'] = year;
    data['user_type'] = userType;
    data['calculation_note'] = calculationNote;
    data['showpayment'] = showpayment;
    data['payment_link'] = paymentLink;
    return data;
  }
}

class PreviousPayment {
  int? jobApplicationId;
  String? paymentDate;
  dynamic amount;
  dynamic transportation;
  dynamic total;

  PreviousPayment({
    this.jobApplicationId,
    this.paymentDate,
    this.amount,
    this.transportation,
    this.total,
  });

  PreviousPayment.fromJson(Map<String, dynamic> json) {
    jobApplicationId = json['job_application_id'];
    paymentDate = json['payment_date'];
    amount = json['amount'];
    transportation = json['transportation'];
    total = json['total'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['job_application_id'] = jobApplicationId;
    data['payment_date'] = paymentDate;
    data['amount'] = amount;
    data['transportation'] = transportation;
    data['total'] = total;
    return data;
  }
}
