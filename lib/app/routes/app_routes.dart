// ignore_for_file: constant_identifier_names

part of 'app_pages.dart';
// DO NOT EDIT. This is code generated via package:get_cli/get_cli.dart

abstract class Routes {
  Routes._();
  static const HOME = _Paths.HOME;
  static const JOBS_VIEW = _Paths.JOBS_VIEW;
  static const JOB_APPLICATIONS_VIEW = _Paths.JOB_APPLICATIONS_VIEW;
  static const CALENDAR_JOBS_VIEW = _Paths.CALENDAR_JOBS_VIEW;
  static const CALENDAR_REPORTS_VIEW = _Paths.CALENDAR_REPORTS_VIEW;
  static const WORK_LOCATION_VIEW = _Paths.WORK_LOCATION_VIEW;
  static const ALL_HOME_VIEW = _Paths.ALL_HOME_VIEW;
  static const MAIN_PAGE = _Paths.MAIN_PAGE;
  static const SPLASH_SCREEN = _Paths.SPLASH_SCREEN;
  static const FAVORITES = _Paths.FAVORITES;
  static const ALBUMS = _Paths.ALBUMS;
  static const LOGIN = _Paths.LOGIN;
  static const SIGNUP = _Paths.SIGNUP;
  static const VERIFICATION_CODE = _Paths.VERIFICATION_CODE;
  static const CONGRATULATION_PAGE = _Paths.CONGRATULATION_PAGE;
  static const MY_ORDERS = _Paths.MY_ORDERS;
  static const OFFERS = _Paths.OFFERS;
  static const NOTIFICATIONS = _Paths.NOTIFICATIONS;
  static const MY_CART = _Paths.MY_CART;
  static const SERVICE_PROVIDERS = _Paths.SERVICE_PROVIDERS;
  static const ALL_POPULAR_SERVICES = _Paths.ALL_POPULAR_SERVICES;
  static const ALL_SERVICES = _Paths.ALL_SERVICES;
  static const ABOUT_US = _Paths.ABOUT_US;
  static const PRIVACY_POLICY = _Paths.PRIVACY_POLICY;
  static const TERMS_AND_CONDITIONS = _Paths.TERMS_AND_CONDITIONS;
  static const CONSULATION_REQUEST = _Paths.CONSULATION_REQUEST;
  static const FAQ = _Paths.FAQ;
  static const LANGUAGES = _Paths.LANGUAGES;
  static const MY_BOOKING = _Paths.MY_BOOKING;
  static const OFFER_SERVICE = _Paths.OFFER_SERVICE;
  static const PROVIDER_OFFER_SERVICE = _Paths.PROVIDER_OFFER_SERVICE;
  static const CALCULATE_OFFER_PAGE = _Paths.CALCULATE_OFFER_PAGE;
  static const MY_BOOKING_DETAILS = _Paths.MY_BOOKING_DETAILS;
  static const PAYMENT = _Paths.PAYMENT;
  // static const PAYMENT_METHOD = _Paths.PAYMENT_METHOD;
  static const PAYMENT_COMPELETED = _Paths.PAYMENT_COMPELETED;
  static const RATE_PROVIDER = _Paths.RATE_PROVIDER;
  static const ORDER_CANCELED = _Paths.ORDER_CANCELED;
  static const ORDER_DETAILS = _Paths.ORDER_DETAILS;
  static const WALLET = _Paths.WALLET;
  static const ENTER_PHONE_NUMBER = _Paths.ENTER_PHONE_NUMBER;
  static const ENTER_CODE_FORGET_PASSWORD = _Paths.ENTER_CODE_FORGET_PASSWORD;
  static const ENTER_NEW_PASSWORD_FORGET_PASSWORD =
      _Paths.ENTER_NEW_PASSWORD_FORGET_PASSWORD;
  static const PASSWORD_CHANGED_SUCCESSFULLY =
      _Paths.PASSWORD_CHANGED_SUCCESSFULLY;
  static const PROVIDER_PAGE = _Paths.PROVIDER_PAGE;
  static const PROVIDER_PAGE_FROM_DYNAMIC_LINK =
      _Paths.PROVIDER_PAGE_FROM_DYNAMIC_LINK;
  static const PROVIDER_REVIEWS = _Paths.PROVIDER_REVIEWS;
  static const MY_PROFILE = _Paths.MY_PROFILE;
  static const PROVIDER_PAGE_FILTERED = _Paths.PROVIDER_PAGE_FILTERED;
  static const REQUEST_SENT_SUCCESSFULLY = _Paths.REQUEST_SENT_SUCCESSFULLY;
  static const EDIT_SCHDUEL = _Paths.EDIT_SCHDUEL;
  static const CHANGE_PASSWORD = _Paths.CHANGE_PASSWORD;
  static const PRICING = _Paths.PRICING;
  static const ADD_NEW_SERVICE = _Paths.ADD_NEW_SERVICE;
  static const EDIT_SERVICE = _Paths.EDIT_SERVICE;
  static const MY_OFFERS = _Paths.MY_OFFERS;
  static const ADD_OFFER = _Paths.ADD_OFFER;
  static const EDIT_OFFER = _Paths.EDIT_OFFER;
  static const OFFER_DETAILS = _Paths.OFFER_DETAILS;
  static const ORDER_APPROVED = _Paths.ORDER_APPROVED;
  static const ORDER_COMPELETED = _Paths.ORDER_COMPELETED;
  static const CHAT = _Paths.CHAT;
  static const USER_WALLET = _Paths.USER_WALLET;
  static const PROVIDER_FROM_SLIDER = _Paths.PROVIDER_FROM_SLIDER;
  static const PAYMENT_WEBVIEW = _Paths.PAYMENT_WEBVIEW;
  static const FILTER_ORDERS = _Paths.FILTER_ORDERS;
}

abstract class _Paths {
  _Paths._();
  static const HOME = '/home';
  static const ALL_HOME_VIEW = '/all-home-view';
  static const WORK_LOCATION_VIEW = '/work-location-view';
  static const MAIN_PAGE = '/main-page';
  static const SPLASH_SCREEN = '/splash-screen';
  static const FAVORITES = '/favorites';
  static const ALBUMS = '/albums';
  static const LOGIN = '/login';
  static const SIGNUP = '/signup';
  static const VERIFICATION_CODE = '/verification-code';
  static const CONGRATULATION_PAGE = '/congratulation-page';
  static const MY_ORDERS = '/my-orders';
  static const OFFERS = '/offers';
  static const NOTIFICATIONS = '/notifications';
  static const MY_CART = '/cart';
  static const SERVICE_PROVIDERS = '/service-providers';
  static const ALL_POPULAR_SERVICES = '/all-popular-services';
  static const ALL_SERVICES = '/all-services';
  static const ABOUT_US = '/about-us';
  static const PRIVACY_POLICY = '/privacy-policy';
  static const TERMS_AND_CONDITIONS = '/terms-and-conditions';
  static const CONSULATION_REQUEST = '/consulation-request';
  static const FAQ = '/faq';
  static const LANGUAGES = '/languages';
  static const MY_BOOKING = '/my-booking';
  static const OFFER_SERVICE = '/offer-service';
  static const PROVIDER_OFFER_SERVICE = '/provider-offer-service';
  static const CALCULATE_OFFER_PAGE = '/calculate-offer-page';
  static const MY_BOOKING_DETAILS = '/my-booking-details';
  static const PAYMENT = '/payment';
  // static const PAYMENT_METHOD = '/payment-method';
  static const PAYMENT_COMPELETED = '/payment-compeleted';
  static const RATE_PROVIDER = '/rate-provider';
  static const ORDER_CANCELED = '/order-canceled';
  static const ORDER_DETAILS = '/order-details';
  static const WALLET = '/wallet';
  static const ENTER_PHONE_NUMBER = '/enter-phone-number';
  static const ENTER_CODE_FORGET_PASSWORD = '/enter-code-forget-password';
  static const ENTER_NEW_PASSWORD_FORGET_PASSWORD =
      '/enter-new-password-forget-password';
  static const PASSWORD_CHANGED_SUCCESSFULLY = '/password-changed-successfully';
  static const PROVIDER_PAGE = '/provider-page';
  static const PROVIDER_PAGE_FROM_DYNAMIC_LINK =
      '/provider-page-from-dynamic-link';
  static const PROVIDER_REVIEWS = '/provider-reviews';
  static const MY_PROFILE = '/my-profile';
  static const PROVIDER_PAGE_FILTERED = '/provider-page-filtered';
  static const REQUEST_SENT_SUCCESSFULLY = '/request-sent-successfully';
  static const EDIT_SCHDUEL = '/edit-schduel';
  static const CHANGE_PASSWORD = '/change-password';
  static const PRICING = '/pricing';
  static const ADD_NEW_SERVICE = '/add-new-service';
  static const EDIT_SERVICE = '/edit-service';
  static const MY_OFFERS = '/my-offers';
  static const ADD_OFFER = '/add-offer';
  static const EDIT_OFFER = '/edit-offer';
  static const OFFER_DETAILS = '/offer-details';
  static const ORDER_APPROVED = '/order-approved';
  static const ORDER_COMPELETED = '/order-compeleted';
  static const CHAT = '/chat';
  static const USER_WALLET = '/user-wallet';
  static const PROVIDER_FROM_SLIDER = '/provider-from-slider';
  static const PAYMENT_WEBVIEW = '/payment-webview';
  static const FILTER_ORDERS = '/filter-orders';
  static const JOBS_VIEW = '/jobs-view';
  static const JOB_APPLICATIONS_VIEW = '/job-applications-view';
  static const CALENDAR_JOBS_VIEW = '/calendar-jobs-view';
  static const CALENDAR_REPORTS_VIEW = '/calendar-reports-view';
}
