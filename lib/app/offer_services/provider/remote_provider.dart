import 'dart:convert';
import 'dart:developer';

import 'package:get_clean/global/constants/constants.dart';
import 'package:get_clean/global/dio/dio_helper.dart';
import 'package:get_clean/global/help_functions/help_functions.dart';
import 'package:get_clean/global/models/service_offer_model.dart';

import '../controllers/states/offer_services_states.dart';

class OfferServiceRemoteProvider {
  DioHelper helper = DioHelper();

  Future<OfferServiceState> getMyServices() async {
    try {
      final response = await helper.getData(getProviderServicesURL);
      log(jsonEncode(response));
      if (response['success'] == true) {
        return OfferServiceSuccessState(ServiceOffersModel.fromJson(response));
      } else {
        showErrorToast(response['message']);
        return OfferServiceFailedState(response['message']);
      }
    } catch (e) {
      return OfferServiceFailedState(e.toString());
    }
  }

  Future<bool> deleteService(int serviceID) async {
    try {
      final response = await helper.postData(
        deleteServiceURL + serviceID.toString(),
        {'': null},
      );

      if (response['success'] == true) {
        showSuccessToast(response['message']);
        return true;
      } else {
        showErrorToast(response['message']);
        return false;
      }
    } catch (e) {
      log(e.toString());
      return false;
    }
  }
}
