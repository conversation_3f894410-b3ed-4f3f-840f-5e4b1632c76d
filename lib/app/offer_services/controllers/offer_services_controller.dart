// import 'dart:developer';
//
// import 'package:get/get.dart';
// import 'package:get_clean/app/modules/my_orders/controllers/my_booking_controller.dart';
// import 'package:get_clean/global/models/user_booking.dart';
//
// class OfferServicesController extends GetxController {
//   // final AllOfferServicesModel offerServices =
//   //     Get.find<GlobalValuesController>().allOfferServices.value;
//   //
//   // void onOfferServiceTapped(offerService) {
//   //   Get.toNamed(
//   //     Routes.OFFERSERVICE_DETAILS,
//   //     arguments: {'offerService': OfferServices.fromJson(offerService.toJson())},
//   //   );
//   // }
//
//   final myOfferOrders = <BookingData>[].obs;
//
//   final loading = false.obs;
//
//   @override
//   void onInit() {
//     super.onInit();
//
//     getMyBooking();
//   }
//
//   void getMyBooking() async {
//     loading.value = true;
//
//     myOfferOrders.value = await Get.find<MyBookingController>().getMyBooking();
//
//     //? filter orders by offer service
//     myOfferOrders.value = myOfferOrders
//         .where((element) => element.orderData?.isOffer == true)
//         .toList();
//
//     log('asfassaf ${myOfferOrders.length}');
//
//     loading.value = false;
//
//     update();
//   }
// }
//
// //AAAAXvUh9-U:APA91bH3YuldsGRsJ61afeLftmNoHqih0PQjKe2roPgLg8m1GZcs2wXJ171H8oDlhoe9v6KLjy-K--IJ31ra1I5AtDPpA-M28Vt39AY-eUPD96kZnpfseaXpYQCm196e4YfEX59y2PhA
//
// //await http.post(
// //       Uri.parse('https://fcm.googleapis.com/fcm/send'),
// //       headers: <String, String>{
// //         'Content-Type': 'application/json',
// //         'Authorization': 'key=${Config().serverToken}',
// //       },
// //       body: jsonEncode(
// //         <String, dynamic>{
// //           'notification': <String, dynamic>{
// //             'title': 'Title',
// //             'body': "Body",
// //           },
// //           'priority': 'high',
// //           'data': <String, dynamic>{
// //             'click_action': 'FLUTTER_NOTIFICATION_CLICK',
// //             'id': '1',
// //             'status': 'done'
// //           },
// //           'to': ‘general’ //? Topic name
// //         },
// //       ),
// //     );
