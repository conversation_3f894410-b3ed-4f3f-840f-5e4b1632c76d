import 'package:get_clean/global/models/service_offer_model.dart';

class OfferServiceState {
  ServiceOffersModel? myServicesModel;
  String? errorMessage;
}

class OfferServiceSuccessState extends OfferServiceState {
  OfferServiceSuccessState(ServiceOffersModel myServicesModel) {
    this.myServicesModel = myServicesModel;
  }
}

class OfferServiceFailedState extends OfferServiceState {
  OfferServiceFailedState(String errorMessage) {
    this.errorMessage = errorMessage;
  }
}

class OfferServiceLoadingState extends OfferServiceState {}
