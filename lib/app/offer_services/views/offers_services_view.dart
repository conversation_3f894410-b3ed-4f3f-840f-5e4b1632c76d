// import 'dart:developer';
//
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:get_clean/app/modules/my_orders/controllers/my_booking_controller.dart';
// import 'package:get_clean/app/offer_services/controllers/offer_services_controller.dart';
// import 'package:get_clean/global/controllers/language_controller.dart';
// import 'package:get_clean/global/widget/bottom_navigation_bar.dart';
// import 'package:get_clean/global/widget/order_widget.dart';
//
// class OfferServicesView extends GetView<OfferServicesController> {
//   const OfferServicesView({Key? key}) : super(key: key);
//
//   @override
//   Widget build(BuildContext context) {
//     Get.put<MyBookingController>(
//       MyBookingController(),
//     );
//     Get.put<OfferServicesController>(
//       OfferServicesController(),
//     );
//
//     log('afasfas ${controller.myOfferOrders.length}');
//
//     return Scaffold(
//       bottomNavigationBar: const BottomNavBarWidget(),
//       appBar: AppBar(
//         title: Text(Get.find<LanguageController>().keys.value.myOfferServices!),
//         centerTitle: true,
//       ),
//       body: Container(
//         decoration: const BoxDecoration(
//           image: DecorationImage(
//             image: AssetImage(
//               'assets/images/main_background_bottom.png',
//             ),
//             fit: BoxFit.fill,
//           ),
//         ),
//         alignment: Alignment.center,
//         padding: const EdgeInsets.all(10),
//         child: Obx(() {
//           if (controller.loading.value) {
//             return const Center(
//               child: CircularProgressIndicator(),
//             );
//           } else {
//             return ListView.builder(
//               itemBuilder: (context, index) {
//                 return OrderWidget(
//                   bookingData: controller.myOfferOrders[index],
//                   isUser: true,
//                   showPhone: true,
//                   isOffer: true,
//                 );
//               },
//               itemCount: controller.myOfferOrders.length,
//             );
//           }
//         }),
//         // Column(
//         //   crossAxisAlignment: CrossAxisAlignment.start,
//         //   children: [
//         // Expanded(
//         //   child: ListView.builder(
//         //     itemBuilder: (context, index) {
//         //       return OfferServiceWidget(
//         //         address:
//         //             '${controller.offerServices.data![index].address!.name!},${controller.offerServices.data![index].address!.area!.name!}',
//         //         name: controller.offerServices.data![index].name,
//         //         onBookPressed: () => controller
//         //             .onOfferServiceTapped(controller.offerServices.data![index]),
//         //         onOfferServicePressed: () => controller
//         //             .onOfferServiceTapped(controller.offerServices.data![index]),
//         //         imageURL: controller.offerServices.data![index].image,
//         //         providerImage:
//         //             controller.offerServices.data![index].provider!.image!,
//         //         providerName:
//         //             controller.offerServices.data![index].provider!.name!,
//         //         rating: controller.offerServices.data![index].provider!.rating!
//         //             .toDouble(),
//         //       );
//         //     },
//         //     itemCount: controller.offerServices.data!.length,
//         //   ),
//         // ),
//
//         // ],
//         // ),
//       ),
//     );
//   }
// }
