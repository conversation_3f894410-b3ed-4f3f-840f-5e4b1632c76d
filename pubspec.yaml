name: get_clean
version: 1.0.54+54
publish_to: none
description: A new Flutter project.
environment:
  sdk: '>=3.0.0'

dependencies:
  cupertino_icons: ^1.0.8
  get: ^4.6.6
  google_fonts: ^6.2.1
  font_awesome_flutter: ^10.7.0
  get_storage: ^2.1.1
  dio: ^5.4.3+1
  fluttertoast: ^8.2.12
  intl:
  flutter_svg: ^2.0.10+1
  dropdown_button2: ^2.3.9
  pin_code_fields: ^8.0.1
  carousel_slider: ^5.0.0
#  flutter_credit_card: ^4.1.0
  flutter_rating_bar: ^4.0.1
  flutter_calendar_carousel:
  flutter_screenutil: ^5.9.1
  file_picker: ^8.0.3
  flutter_html:
  date_time_picker:
  dropdown_textfield:
    path: packages/dropdown_textfield
  url_launcher: ^6.3.1
  bubble: ^1.2.1
  intl_phone_number_input: ^0.7.4
  flutter_local_notifications: ^19.2.1
  #  flutter_local_notifications: ^19.0.0
  badges: ^3.1.2
  webview_flutter:
  cached_network_image: ^3.3.1
  dropdown_search: ^6.0.1
  flutter_keyboard_visibility: ^6.0.0

  #! Firebase
  firebase_core:
  firebase_messaging:
  firebase_auth: ^5.5.1
  firebase_dynamic_links:
  flutter_hooks:

  google_maps_flutter:
  geolocator:

  share_plus: ^10.1.4
  calendar_date_picker2: ^1.0.2
  restart_app: ^1.3.2
  flutter_staggered_grid_view: ^0.7.0

  flutter:
    sdk: flutter
#  firebase_core: ^2.12.0
#  firebase_auth: ^4.6.0

dependency_overrides:
  url_launcher: ^6.3.1

dev_dependencies:
  flutter_lints: ^5.0.0
  #  change_app_package_name: ^1.1.0
  #  flutter_app_name: ^0.1.0
  #  flutter_launcher_icons: ^0.13.1
  #  flutter_native_splash: ^2.3.0
  flutter_test:
    sdk: flutter

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/animated/
    - assets/locales/
    - shorebird.yaml


# flutter pub run flutter_app_name
flutter_app_name:
  name: "Vish Vish"

# flutter pub run flutter_native_splash:create
flutter_native_splash:
  image: assets/images/logo.png
  color: "#FFFFFF"

# flutter pub run flutter_launcher_icons:main
flutter_icons:
  android: true
  ios: true
  remove_alpha_ios: true
  image_path: "assets/images/logo.png"

  web:
    generate: true
    image_path: "assets/images/logo.png"
    background_color: "#FFFFFF"
    theme_color: "#FFFFFF"
  windows:
    generate: true
    image_path: "assets/images/logo.png"
    icon_size: 48 # min:48, max:256, default: 48
  macos:
    generate: true
    image_path: "assets/images/logo.png"
#name: get_clean
#version: 1.0.53+53
#publish_to: none
#description: A new Flutter project.
#environment:
#  sdk: '>=3.0.0'
#
#dependencies:
#  cupertino_icons: ^1.0.8
#  get: ^4.6.6
#  google_fonts: ^6.2.1
#  font_awesome_flutter: ^10.7.0
#  get_storage: ^2.1.1
#  dio: ^5.4.3+1
#  fluttertoast: ^8.2.5
#  intl:
#  flutter_svg: ^2.0.10+1
#  dropdown_button2: ^2.3.9
#  pin_code_fields: ^8.0.1
#  carousel_slider: ^5.0.0
##  flutter_credit_card: ^4.0.1
#  flutter_rating_bar: ^4.0.1
#  flutter_calendar_carousel:
#  flutter_screenutil: ^5.9.1
#  file_picker: ^8.0.3
#  flutter_html:
#  date_time_picker:
#  dropdown_textfield:
#    path: packages/dropdown_textfield
#  url_launcher: ^6.2.6
#  bubble: ^1.2.1
#  intl_phone_number_input: ^0.7.4
#  flutter_local_notifications: ^17.1.2
#  badges: ^3.1.2
#  webview_flutter:
#  cached_network_image: ^3.3.1
#  dropdown_search: ^5.0.6
#  flutter_keyboard_visibility: ^6.0.0
#
#  #! Firebase
#  firebase_core:
#  firebase_messaging:
#  firebase_auth:
#  firebase_dynamic_links:
#  flutter_hooks:
#
#  google_maps_flutter:
#  geolocator:
#
#  share_plus: ^10.0.2
##  share: ^2.0.4
#  calendar_date_picker2: ^1.0.2
#  restart_app:
#
#  flutter:
#    sdk: flutter
##  firebase_core: ^2.12.0
##  firebase_auth: ^4.6.0
#
#dependency_overrides:
#  archive: ^3.6.1
#  win32: ^5.5.4
#
#
#dev_dependencies:
#  flutter_lints: ^4.0.0
#  #  change_app_package_name: ^1.1.0
#  #  flutter_app_name: ^0.1.0
#  #  flutter_launcher_icons: ^0.13.1
#  #  flutter_native_splash: ^2.3.0
#  flutter_test:
#    sdk: flutter
#
#flutter:
#  uses-material-design: true
#
#  assets:
#    - assets/images/
#    - assets/animated/
#    - assets/locales/
#    - shorebird.yaml
#
#
## flutter pub run flutter_app_name
#flutter_app_name:
#  name: "Vish Vish"
#
## flutter pub run flutter_native_splash:create
#flutter_native_splash:
#  image: assets/images/logo.png
#  color: "#FFFFFF"
#
## flutter pub run flutter_launcher_icons:main
#flutter_icons:
#  android: true
#  ios: true
#  remove_alpha_ios: true
#  image_path: "assets/images/logo.png"
#
#  web:
#    generate: true
#    image_path: "assets/images/logo.png"
#    background_color: "#FFFFFF"
#    theme_color: "#FFFFFF"
#  windows:
#    generate: true
#    image_path: "assets/images/logo.png"
#    icon_size: 48 # min:48, max:256, default: 48
#  macos:
#    generate: true
#    image_path: "assets/images/logo.png"