import 'package:flutter_test/flutter_test.dart';
import 'package:get_clean/global/models/coupon_model.dart';

void main() {
  group('Coupon Feature Tests', () {
    test('CouponModel should parse JSON correctly', () {
      final json = {
        "success": true,
        "code": 1,
        "message": "Coupon is valid",
        "data": {
          "id": 1,
          "code": "381536B7",
          "discount_value": "100.00",
          "discount_type": "fixed",
          "usage_count": 1,
          "usage_limit": 3,
          "start_date": "2025-05-19",
          "end_date": "2025-05-22"
        }
      };

      final coupon = CouponModel.fromJson(json);

      expect(coupon.success, true);
      expect(coupon.code, 1);
      expect(coupon.message, "Coupon is valid");
      expect(coupon.data?.id, 1);
      expect(coupon.data?.code, "381536B7");
      expect(coupon.data?.discountValue, "100.00");
      expect(coupon.data?.discountType, "fixed");
      expect(coupon.data?.usageCount, 1);
      expect(coupon.data?.usageLimit, 3);
      expect(coupon.data?.startDate, "2025-05-19");
      expect(coupon.data?.endDate, "2025-05-22");
    });

    test('CouponModel should handle invalid coupon response', () {
      final json = {
        "success": false,
        "code": 0,
        "message": "Invalid coupon code",
        "data": null
      };

      final coupon = CouponModel.fromJson(json);

      expect(coupon.success, false);
      expect(coupon.code, 0);
      expect(coupon.message, "Invalid coupon code");
      expect(coupon.data, null);
    });

    test('CouponData should convert to JSON correctly', () {
      final couponData = CouponData(
        id: 1,
        code: "381536B7",
        discountValue: "100.00",
        discountType: "fixed",
        usageCount: 1,
        usageLimit: 3,
        startDate: "2025-05-19",
        endDate: "2025-05-22",
      );

      final json = couponData.toJson();

      expect(json['id'], 1);
      expect(json['code'], "381536B7");
      expect(json['discount_value'], "100.00");
      expect(json['discount_type'], "fixed");
      expect(json['usage_count'], 1);
      expect(json['usage_limit'], 3);
      expect(json['start_date'], "2025-05-19");
      expect(json['end_date'], "2025-05-22");
    });
  });
}
